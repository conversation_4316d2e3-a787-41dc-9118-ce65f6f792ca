jquery-migrate.min.js?ver=3.4.1:2 JQMIGRATE: Migrate is installed, version 3.4.1
(index):459 Uncaught SyntaxError: Unexpected token '<' (at (index):459:96)
(index):978 OCHandyDude Debug Logs (from PHP)
(index):979 [2025-08-18 14:01:43.703021] [391a8108] [BookingForm-PHP] DEBUG: Starting Easy!Appointments iframe generation {booking_url: 'configured', user_logged_in: true, current_user: 'adm'}
(index):980 [2025-08-18 14:01:43.703138] [391a8108] [BookingForm-PHP] INFO: Generating Easy!Appointments iframe {iframe_id: 'ochd-easyappointments-iframe-68a394b7aba8b', final_url: 'https://book.ochandydude.pro/', query_params: Array(0), has_attributes: true, attributes: {…}}
(index):981 [2025-08-18 14:01:45.061672] [391a8108] [BookingForm-PHP] INFO: Auto-fill data localized for booking form {isLoggedIn: true, hasUserData: true, userData: {…}}
(index):982 [2025-08-18 14:01:45.061842] [391a8108] [BookingForm-PHP] INFO: Debug test script enqueued for booking form {script_url: 'https://ochandydude.pro/wp-content/plugins/ochandydude-master/assets/js/debug-logging-test.js', debug_config: {…}}
(index):895 [2025-08-18T21:01:45.527Z] [391a8108] [DebugLogger] INFO: Debug logging initialized {wpDebugEnabled: true, viewport: '1481x957', userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb…KHTML, like Gecko) Chrome/********* Safari/537.36', url: 'https://ochandydude.pro/'}
profile-menu.js?ver=1.4:21 Profile menu: Using fallback event handling
profile-menu-navigation.js?ver=1.0:6 Profile Menu Navigation: Initializing...
profile-menu-navigation.js?ver=1.0:183 Profile Menu Navigation: Debug functions available via window.debugProfileNavigation
mobile-menu.js?ver=4.9:134 OCHandyDude Menu: Mobile menu initialized successfully
(index):895 [2025-08-18T21:01:45.854Z] [391a8108] [BookingForm] INFO: Booking form script initialized {viewport: '1481x957', userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb…KHTML, like Gecko) Chrome/********* Safari/537.36', timestamp: 1755550905854}
(index):895 [2025-08-18T21:01:45.857Z] [391a8108] [BookingForm-Iframe] INFO: Height override system temporarily disabled {reason: 'Investigating height truncation at 1-second mark', timestamp: 1755550905857}
(index):895 [2025-08-18T21:01:45.858Z] [391a8108] [DebugTest] INFO: Debug logging test script fully initialized {testingEnabled: true, monitoringEnabled: true, periodicReporting: true, eventListeners: true}
(index):897 [2025-08-18T21:01:45.860Z] [391a8108] [FixTest] INFO: Booking form fix test script initialized
(index):897 [2025-08-18T21:01:45.861Z] [391a8108] [HeightFixTest] INFO: Height fix test script initialized
(index):895 [2025-08-18T21:01:45.863Z] [391a8108] [HeightFixTest] INFO: Starting iframe height monitoring {iframeId: 'ochd-easyappointments-iframe-68a394b7aba8b', initialHeight: '', initialMinHeight: '800px', initialMaxHeight: ''}
(index):897 [2025-08-18T21:01:45.865Z] [391a8108] [VisualLayoutTest] INFO: Visual layout test script initialized
(index):897 [2025-08-18T21:01:45.868Z] [391a8108] [VisualLayoutTest] INFO: Layout change monitoring started
header-icon-manager.js?ver=1.5:246 Header Icon Manager: Profile icon initialized successfully
header-icon-manager.js?ver=1.5:270 Header Icon Manager: Mobile menu toggle initialized successfully
header-icon-manager.js?ver=1.5:310 Header Icon Manager: Cart icon initialized successfully
header-icon-manager.js?ver=1.5:342 Header Icon Manager initialized with exclusive activation
profile-menu-navigation.js?ver=1.0:26 Profile Menu Navigation: Found 4 menu links
profile-menu-navigation.js?ver=1.0:41 Setting up link 1: "My Profile" -> https://ochandydude.pro/my-account/#profile (navigate)
profile-menu-navigation.js?ver=1.0:41 Setting up link 2: "My Bookings" -> https://ochandydude.pro/my-account/#bookings (navigate)
profile-menu-navigation.js?ver=1.0:41 Setting up link 3: "Account Settings" -> https://auth.ochandydude.pro/realms/ochandydude/account/ (external)
profile-menu-navigation.js?ver=1.0:41 Setting up link 4: "Logout" -> https://ochandydude.pro/?custom-logout=true&_wpnonce=4a0f328377 (logout)
profile-menu-navigation.js?ver=1.0:33 Profile Menu Navigation: All links initialized
custom.js:544 Sending ready message to parent window
custom.js:546 Custom.js loaded and ready message sent
(index):895 [2025-08-18T21:01:46.357Z] [391a8108] [BookingForm-Iframe] INFO: Received ready message from iframe {data: {…}, ochdBookingDataAvailable: true}
booking-form.js?ver=21.1:354 Sending data to iframe: {isLoggedIn: '1', ajax_url: 'https://ochandydude.pro/wp-admin/admin-ajax.php', login_url: 'https://auth.ochandydude.pro/realms/ochandydude/pr…dmin-ajax.php%3Faction%3Dopenid-connect-authorize', nonce: '77eff24c70', userData: {…}}
(index):895 [2025-08-18T21:01:46.358Z] [391a8108] [BookingForm-Iframe] INFO: Sending auto-fill data to iframe {isLoggedIn: '1', hasUserData: true, userData: {…}}
(index):895 [2025-08-18T21:01:46.359Z] [391a8108] [BookingForm-Iframe] INFO: Auto-fill message sent to iframe {targetOrigin: 'https://book.ochandydude.pro', messageSize: 621, success: true}
(index):895 [2025-08-18T21:01:46.361Z] [391a8108] [BookingForm-Iframe] INFO: Message received from booking iframe {data: {…}, origin: 'https://book.ochandydude.pro', timestamp: 1755550906361}
custom.js:12 Message received in iframe: {origin: 'https://ochandydude.pro', expectedOrigin: 'https://ochandydude.pro', hasData: true, dataSource: 'OCHandyDudeParent'}
custom.js:29 Valid message received from parent: {source: 'OCHandyDudeParent', isLoggedIn: '1', loginUrl: 'https://auth.ochandydude.pro/realms/ochandydude/pr…dmin-ajax.php%3Faction%3Dopenid-connect-authorize', ajaxUrl: 'https://ochandydude.pro/wp-admin/admin-ajax.php', userData: {…}, …}
custom.js:43 All required data present - initializing functionality
custom.js:47 User is logged in - starting autofill
(index):895 [2025-08-18T21:01:46.428Z] [391a8108] [BookingForm-Iframe] INFO: Applying smart height management {iframeId: 'ochd-easyappointments-iframe-68a394b7aba8b', currentHeight: '', timestamp: 1755550906428}
(index):895 [2025-08-18T21:01:46.429Z] [391a8108] [BookingForm-Height] INFO: Height changed: 1400px → 1400px (Smart height management applied) {oldHeight: '1400px', newHeight: '1400px', reason: 'Smart height management applied', timestamp: 1755550906429}
(index):895 [2025-08-18T21:01:46.430Z] [391a8108] [HeightFixTest] INFO: Style attribute changed on iframe {oldValue: 'border: none; width: 100%; display: block; min-height: 800px; margin: 0; padding: 0;', newValue: 'border: none; width: 100%; display: block; min-hei…; padding: 0px; height: 1400px; max-height: none;', timeElapsed: '0s'}
(index):895 [2025-08-18T21:01:46.432Z] [391a8108] [HeightFixTest] INFO: Style attribute changed on iframe {oldValue: 'border: none; width: 100%; display: block; min-hei…800px; margin: 0px; padding: 0px; height: 1400px;', newValue: 'border: none; width: 100%; display: block; min-hei…; padding: 0px; height: 1400px; max-height: none;', timeElapsed: '0s'}
(index):895 [2025-08-18T21:01:46.433Z] [391a8108] [VisualLayoutTest] INFO: Iframe style changed - checking layout {newStyle: 'border: none; width: 100%; display: block; min-hei…; padding: 0px; height: 1400px; max-height: none;', timestamp: 1755550906433}
(index):895 [2025-08-18T21:01:46.434Z] [391a8108] [VisualLayoutTest] INFO: Iframe style changed - checking layout {newStyle: 'border: none; width: 100%; display: block; min-hei…; padding: 0px; height: 1400px; max-height: none;', timestamp: 1755550906434}
(index):895 [2025-08-18T21:01:46.450Z] [391a8108] [HeightFixTest] INFO: Height status at 500ms {timing: '500ms', styleHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):897 [2025-08-18T21:01:46.550Z] [391a8108] [VisualLayoutTest] INFO: Applying layout fixes
(index):895 [2025-08-18T21:01:46.551Z] [391a8108] [VisualLayoutTest] INFO: Layout fixes applied {fixes: Array(4), newHeight: '1400px', newMargin: '0px', newPadding: '0px'}
(index):895 [2025-08-18T21:01:46.553Z] [391a8108] [HeightFixTest] INFO: Style attribute changed on iframe {oldValue: 'border: none; width: 100%; display: block; min-hei…; padding: 0px; height: 1400px; max-height: none;', newValue: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timeElapsed: '0s'}
(index):895 [2025-08-18T21:01:46.555Z] [391a8108] [HeightFixTest] INFO: Style attribute changed on iframe {oldValue: 'border: none; width: 100%; display: block; min-hei…1400px; max-height: none; box-sizing: border-box;', newValue: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timeElapsed: '0s'}
(index):895 [2025-08-18T21:01:46.556Z] [391a8108] [VisualLayoutTest] INFO: Iframe style changed - checking layout {newStyle: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timestamp: 1755550906556}
(index):895 [2025-08-18T21:01:46.557Z] [391a8108] [VisualLayoutTest] INFO: Iframe style changed - checking layout {newStyle: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timestamp: 1755550906557}
(index):897 [2025-08-18T21:01:46.562Z] [391a8108] [VisualLayoutTest] INFO: Applying layout fixes
(index):895 [2025-08-18T21:01:46.563Z] [391a8108] [VisualLayoutTest] INFO: Layout fixes applied {fixes: Array(4), newHeight: '1400px', newMargin: '0px', newPadding: '0px'}
mobile-menu.js?ver=4.9:32 OCHandyDude Menu: Menu content loaded successfully
(index):897 [2025-08-18T21:01:46.660Z] [391a8108] [VisualLayoutTest] INFO: Applying layout fixes
(index):895 [2025-08-18T21:01:46.661Z] [391a8108] [VisualLayoutTest] INFO: Layout fixes applied {fixes: Array(4), newHeight: '1400px', newMargin: '0px', newPadding: '0px'}
(index):897 [2025-08-18T21:01:46.662Z] [391a8108] [VisualLayoutTest] INFO: Applying layout fixes
(index):895 [2025-08-18T21:01:46.664Z] [391a8108] [VisualLayoutTest] INFO: Layout fixes applied {fixes: Array(4), newHeight: '1400px', newMargin: '0px', newPadding: '0px'}
(index):895 [2025-08-18T21:01:46.870Z] [391a8108] [HeightFixTest] INFO: Height check 1/20 {timeElapsed: '1s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:46.871Z] [391a8108] [HeightFixTest] WARN: Height change detected! {timeElapsed: '1s', oldHeight: '', newHeight: '1400px', changeType: 'EXPANSION'}
log @ (index):895
warn @ (index):926
(anonymous) @ height-fix-test.js?ver=1.0:55
setInterval
monitorIframeHeight @ height-fix-test.js?ver=1.0:38
(anonymous) @ height-fix-test.js?ver=1.0:17
(index):895 [2025-08-18T21:01:46.872Z] [391a8108] [HeightFixTest] WARN: CRITICAL: 1-second mark reached - monitoring for truncation {heightAt1Second: '1400px', computedHeightAt1Second: '1400px', expectedIssueTime: true}
log @ (index):895
warn @ (index):926
(anonymous) @ height-fix-test.js?ver=1.0:81
setInterval
monitorIframeHeight @ height-fix-test.js?ver=1.0:38
(anonymous) @ height-fix-test.js?ver=1.0:17
(index):895 [2025-08-18T21:01:46.874Z] [391a8108] [HeightFixTest] INFO: Height status at 1000ms {timing: '1000ms', styleHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:47.366Z] [391a8108] [HeightFixTest] INFO: Height status at 1500ms {timing: '1500ms', styleHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):897 [2025-08-18T21:01:47.860Z] [391a8108] [FixTest] INFO: === BOOKING FORM FIX TESTS START ===
(index):897 [2025-08-18T21:01:47.861Z] [391a8108] [FixTest] INFO: Testing AJAX URL availability
(index):895 [2025-08-18T21:01:47.862Z] [391a8108] [FixTest] INFO: AJAX URL availability results {globalAjaxUrl: false, debugAjaxUrl: true, bookingAjaxUrl: true, debugAjaxData: {…}}
(index):897 [2025-08-18T21:01:47.862Z] [391a8108] [FixTest] INFO: Testing booking data availability
(index):895 [2025-08-18T21:01:47.863Z] [391a8108] [FixTest] INFO: Booking data available {isLoggedIn: '1', hasUserData: true, hasAjaxUrl: true, hasLoginUrl: true, hasNonce: true, …}
(index):897 [2025-08-18T21:01:47.864Z] [391a8108] [FixTest] INFO: All required user data fields present
(index):897 [2025-08-18T21:01:47.864Z] [391a8108] [FixTest] INFO: Testing debug AJAX availability
(index):895 [2025-08-18T21:01:47.865Z] [391a8108] [FixTest] INFO: Debug AJAX data available {hasAjaxUrl: true, hasNonce: true, enabled: '1', wpDebugEnabled: '1'}
(index):897 [2025-08-18T21:01:47.866Z] [391a8108] [FixTest] INFO: Testing iframe communication
(index):895 [2025-08-18T21:01:47.867Z] [391a8108] [FixTest] INFO: Found booking iframe {index: 0, id: 'ochd-easyappointments-iframe-68a394b7aba8b', src: 'https://book.ochandydude.pro/', loaded: false}
(index):895 [2025-08-18T21:01:47.867Z] [391a8108] [FixTest] INFO: Test message sent to iframe {iframeIndex: 0, message: {…}}
(index):897 [2025-08-18T21:01:47.868Z] [391a8108] [FixTest] INFO: Testing for JavaScript errors
(index):897 [2025-08-18T21:01:47.870Z] [391a8108] [FixTest] INFO: === BOOKING FORM FIX TESTS END ===
custom.js:12 Message received in iframe: {origin: 'https://ochandydude.pro', expectedOrigin: 'https://ochandydude.pro', hasData: true, dataSource: 'OCHandyDudeFixTest'}
custom.js:25 Invalid data or source - ignoring message
(index):895 [2025-08-18T21:01:47.871Z] [391a8108] [HeightFixTest] INFO: Height check 2/20 {timeElapsed: '2s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):897 [2025-08-18T21:01:47.872Z] [391a8108] [HeightFixTest] INFO: Testing debug logger methods
(index):897 [2025-08-18T21:01:47.873Z] [391a8108] [HeightFixTest] INFO: Info method test
(index):897 [2025-08-18T21:01:47.874Z] [391a8108] [HeightFixTest] WARN: Warn method test
log @ (index):897
warn @ (index):926
(anonymous) @ height-fix-test.js?ver=1.0:135
setTimeout
(anonymous) @ height-fix-test.js?ver=1.0:129
(index):897 [2025-08-18T21:01:47.874Z] [391a8108] [HeightFixTest] ERROR: Error method test
log @ (index):897
error @ (index):930
(anonymous) @ height-fix-test.js?ver=1.0:136
setTimeout
(anonymous) @ height-fix-test.js?ver=1.0:129
(index):897 [2025-08-18T21:01:47.875Z] [391a8108] [HeightFixTest] INFO: All debug logger methods working correctly
(index):895 [2025-08-18T21:01:47.883Z] [391a8108] [HeightFixTest] INFO: Height status at 2000ms {timing: '2000ms', styleHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):897 [2025-08-18T21:01:47.884Z] [391a8108] [VisualLayoutTest] INFO: === VISUAL LAYOUT TESTS START ===
(index):897 [2025-08-18T21:01:47.886Z] [391a8108] [VisualLayoutTest] INFO: Testing iframe dimensions and spacing
(index):895 [2025-08-18T21:01:47.888Z] [391a8108] [VisualLayoutTest] INFO: Iframe dimensions analysis {styleHeight: '1400px', styleWidth: '100%', styleMargin: '0px', stylePadding: '0px', computedHeight: '1400px', …}
(index):897 [2025-08-18T21:01:47.889Z] [391a8108] [VisualLayoutTest] INFO: Iframe dimensions look good
(index):897 [2025-08-18T21:01:47.890Z] [391a8108] [VisualLayoutTest] INFO: Testing container spacing
(index):895 [2025-08-18T21:01:47.892Z] [391a8108] [VisualLayoutTest] INFO: Container spacing analysis {margin: '0px', padding: '0px', marginTop: '0px', marginBottom: '0px', paddingTop: '0px', …}
(index):895 [2025-08-18T21:01:47.893Z] [391a8108] [VisualLayoutTest] INFO: Wrapper spacing analysis {margin: '0px', padding: '0px 8px', marginTop: '0px', marginBottom: '0px', paddingTop: '0px', …}
(index):897 [2025-08-18T21:01:47.894Z] [391a8108] [VisualLayoutTest] INFO: Testing for white space issues
(index):897 [2025-08-18T21:01:47.895Z] [391a8108] [VisualLayoutTest] INFO: Cannot access iframe content (cross-origin)
(index):897 [2025-08-18T21:01:47.896Z] [391a8108] [VisualLayoutTest] INFO: Applying layout fixes
(index):895 [2025-08-18T21:01:47.897Z] [391a8108] [VisualLayoutTest] INFO: Layout fixes applied {fixes: Array(4), newHeight: '1400px', newMargin: '0px', newPadding: '0px'}
(index):897 [2025-08-18T21:01:47.898Z] [391a8108] [VisualLayoutTest] INFO: === VISUAL LAYOUT TESTS END ===
(index):897 [2025-08-18T21:01:48.867Z] [391a8108] [DebugTest] INFO: === DEBUG LOGGING SYSTEM TEST START ===
(index):897 [2025-08-18T21:01:48.870Z] [391a8108] [DebugTest] INFO: Info level test message
(index):897 [2025-08-18T21:01:48.871Z] [391a8108] [DebugTest] WARN: Warning level test message
log @ (index):897
warn @ (index):926
testDebugLoggingSystem @ debug-logging-test.js?ver=1.0:31
(anonymous) @ debug-logging-test.js?ver=1.0:22
setTimeout
(anonymous) @ debug-logging-test.js?ver=1.0:21
(index):897 [2025-08-18T21:01:48.871Z] [391a8108] [DebugTest] ERROR: Error level test message
log @ (index):897
error @ (index):930
testDebugLoggingSystem @ debug-logging-test.js?ver=1.0:32
(anonymous) @ debug-logging-test.js?ver=1.0:22
setTimeout
(anonymous) @ debug-logging-test.js?ver=1.0:21
(index):895 [2025-08-18T21:01:48.873Z] [391a8108] [BookingForm-Iframe] INFO: Test iframe event message {testIframeId: 'test-iframe', testSrc: 'https://example.com', testEvent: 'load'}
(index):895 [2025-08-18T21:01:48.873Z] [391a8108] [BookingForm-Height] INFO: Height changed: 600px → 800px (Test height change) {oldHeight: '600px', newHeight: '800px', reason: 'Test height change', timestamp: 1755550908873}
(index):895 [2025-08-18T21:01:48.875Z] [391a8108] [OCHandyDude-Error] ERROR: Test error caught {error: 'Error: Test error for logging', stack: 'Error: Test error for logging\n    at testDebugLogg…ster/assets/js/debug-logging-test.js?ver=1.0:22:9'}
log @ (index):895
logError @ (index):953
testDebugLoggingSystem @ debug-logging-test.js?ver=1.0:59
(anonymous) @ debug-logging-test.js?ver=1.0:22
setTimeout
(anonymous) @ debug-logging-test.js?ver=1.0:21
(index):895 [2025-08-18T21:01:48.876Z] [391a8108] [DebugTest] INFO: Test with complex data {complexObject: {…}, timestamp: 1755550908876, userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb…KHTML, like Gecko) Chrome/********* Safari/537.36'}
(index):897 [2025-08-18T21:01:48.877Z] [391a8108] [BookingFormTest] INFO: Testing booking form specific logging
(index):895 [2025-08-18T21:01:48.878Z] [391a8108] [BookingForm-Iframe] INFO: Found iframes on page {count: 1, iframes: Array(1)}
(index):895 [2025-08-18T21:01:48.880Z] [391a8108] [BookingForm-Height] INFO: Height changed: 1400px → 900px (Test height change simulation) {oldHeight: '1400px', newHeight: '900px', reason: 'Test height change simulation', timestamp: 1755550908880}
(index):897 [2025-08-18T21:01:48.883Z] [391a8108] [DebugTest] INFO: === DEBUG LOGGING SYSTEM TEST END ===
(index):895 [2025-08-18T21:01:48.884Z] [391a8108] [HeightFixTest] INFO: Style attribute changed on iframe {oldValue: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', newValue: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timeElapsed: '2s'}
(index):895 [2025-08-18T21:01:48.884Z] [391a8108] [VisualLayoutTest] INFO: Iframe style changed - checking layout {newStyle: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timestamp: 1755550908884}
(index):895 [2025-08-18T21:01:48.887Z] [391a8108] [HeightFixTest] INFO: Height check 3/20 {timeElapsed: '3s', currentHeight: '900px', computedHeight: '900px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:48.887Z] [391a8108] [HeightFixTest] WARN: Height change detected! {timeElapsed: '3s', oldHeight: '1400px', newHeight: '900px', changeType: 'TRUNCATION'}
log @ (index):895
warn @ (index):926
(anonymous) @ height-fix-test.js?ver=1.0:55
setInterval
monitorIframeHeight @ height-fix-test.js?ver=1.0:38
(anonymous) @ height-fix-test.js?ver=1.0:17
(index):895 [2025-08-18T21:01:48.893Z] [391a8108] [HeightFixTest] ERROR: Height truncation detected - attempting to restore {truncatedHeight: '900px', restoringTo: '1400px'}
log @ (index):895
error @ (index):930
(anonymous) @ height-fix-test.js?ver=1.0:64
setInterval
monitorIframeHeight @ height-fix-test.js?ver=1.0:38
(anonymous) @ height-fix-test.js?ver=1.0:17
(index):895 [2025-08-18T21:01:48.895Z] [391a8108] [HeightFixTest] INFO: Style attribute changed on iframe {oldValue: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', newValue: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timeElapsed: '3s'}
(index):895 [2025-08-18T21:01:48.897Z] [391a8108] [VisualLayoutTest] INFO: Iframe style changed - checking layout {newStyle: 'border: none; width: 100%; display: block; min-hei…one; box-sizing: border-box; vertical-align: top;', timestamp: 1755550908897}
(index):897 [2025-08-18T21:01:48.903Z] [391a8108] [OCHandyDude-JS] INFO: Error test info message
(index):897 [2025-08-18T21:01:48.904Z] [391a8108] [OCHandyDude-JS] WARN: Error test warn message
log @ (index):897
warn @ (index):926
(anonymous) @ booking-form-fix-test.js?ver=1.0:196
setTimeout
testJavaScriptErrors @ booking-form-fix-test.js?ver=1.0:191
runBookingFormFixTests @ booking-form-fix-test.js?ver=1.0:34
(anonymous) @ booking-form-fix-test.js?ver=1.0:15
setTimeout
(anonymous) @ booking-form-fix-test.js?ver=1.0:14
(index):895 [2025-08-18T21:01:48.905Z] [391a8108] [BookingForm-Iframe] INFO: Error test iframe message {test: true}
(index):895 [2025-08-18T21:01:48.906Z] [391a8108] [FixTest] INFO: JavaScript error test completed {errorsDetected: 0, errors: Array(0)}
(index):897 [2025-08-18T21:01:48.989Z] [391a8108] [VisualLayoutTest] INFO: Applying layout fixes
(index):895 [2025-08-18T21:01:48.990Z] [391a8108] [VisualLayoutTest] INFO: Layout fixes applied {fixes: Array(4), newHeight: '1400px', newMargin: '0px', newPadding: '0px'}
(index):897 [2025-08-18T21:01:49.004Z] [391a8108] [VisualLayoutTest] INFO: Applying layout fixes
(index):895 [2025-08-18T21:01:49.005Z] [391a8108] [VisualLayoutTest] INFO: Layout fixes applied {fixes: Array(4), newHeight: '1400px', newMargin: '0px', newPadding: '0px'}
(index):897 [2025-08-18T21:01:49.245Z] [391a8108] [FixTest] INFO: AJAX test successful
(index):895 [2025-08-18T21:01:49.873Z] [391a8108] [HeightFixTest] INFO: Height check 4/20 {timeElapsed: '4s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:49.874Z] [391a8108] [HeightFixTest] WARN: Height change detected! {timeElapsed: '4s', oldHeight: '900px', newHeight: '1400px', changeType: 'EXPANSION'}
log @ (index):895
warn @ (index):926
(anonymous) @ height-fix-test.js?ver=1.0:55
setInterval
monitorIframeHeight @ height-fix-test.js?ver=1.0:38
(anonymous) @ height-fix-test.js?ver=1.0:17
(index):895 [2025-08-18T21:01:49.885Z] [391a8108] [BookingForm-Height] INFO: Height changed: 900px → 1400px (Restored original height) {oldHeight: '900px', newHeight: '1400px', reason: 'Restored original height', timestamp: 1755550909885}
(index):895 [2025-08-18T21:01:50.865Z] [391a8108] [BookingFormMonitor] INFO: Started monitoring iframe style changes {monitoredIframes: 1}
(index):895 [2025-08-18T21:01:50.866Z] [391a8108] [HeightFixTest] INFO: Height check 5/20 {timeElapsed: '5s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:51.874Z] [391a8108] [HeightFixTest] INFO: Height check 6/20 {timeElapsed: '6s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:52.865Z] [391a8108] [HeightFixTest] INFO: Height check 7/20 {timeElapsed: '7s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:53.875Z] [391a8108] [HeightFixTest] INFO: Height check 8/20 {timeElapsed: '8s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:54.870Z] [391a8108] [HeightFixTest] INFO: Height check 9/20 {timeElapsed: '9s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:55.868Z] [391a8108] [HeightFixTest] INFO: Height check 10/20 {timeElapsed: '10s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:56.870Z] [391a8108] [HeightFixTest] INFO: Height check 11/20 {timeElapsed: '11s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:57.870Z] [391a8108] [HeightFixTest] INFO: Height check 12/20 {timeElapsed: '12s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:58.867Z] [391a8108] [HeightFixTest] INFO: Height check 13/20 {timeElapsed: '13s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:01:59.870Z] [391a8108] [HeightFixTest] INFO: Height check 14/20 {timeElapsed: '14s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:02:00.866Z] [391a8108] [HeightFixTest] INFO: Height check 15/20 {timeElapsed: '15s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:02:01.870Z] [391a8108] [HeightFixTest] INFO: Height check 16/20 {timeElapsed: '16s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:02:02.868Z] [391a8108] [HeightFixTest] INFO: Height check 17/20 {timeElapsed: '17s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:02:03.870Z] [391a8108] [HeightFixTest] INFO: Height check 18/20 {timeElapsed: '18s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:02:04.867Z] [391a8108] [HeightFixTest] INFO: Height check 19/20 {timeElapsed: '19s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:02:05.871Z] [391a8108] [HeightFixTest] INFO: Height check 20/20 {timeElapsed: '20s', currentHeight: '1400px', computedHeight: '1400px', minHeight: '800px', maxHeight: 'none', …}
(index):895 [2025-08-18T21:02:05.872Z] [391a8108] [HeightFixTest] INFO: Height monitoring completed {totalChecks: 20, finalHeight: '1400px', finalComputedHeight: '1400px', truncationDetected: false, hasExcessiveHeight: false, …}
(index):897 [2025-08-18T21:02:05.873Z] [391a8108] [HeightFixTest] INFO: Style mutation observer disconnected
