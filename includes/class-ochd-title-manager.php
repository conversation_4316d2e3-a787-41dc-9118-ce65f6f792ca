<?php
if ( ! defined( 'ABSPATH' ) ) exit;

/**
 * OCHandyDude Title Manager
 * Handles global page title hiding while preserving admin functionality
 */
class OCHandyDude_Title_Manager {

    public function __construct() {
        // Hook into WordPress to hide titles on frontend
        add_action( 'wp_head', [ $this, 'add_title_hiding_css' ] );
        add_filter( 'the_title', [ $this, 'filter_frontend_title' ], 10, 2 );
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_title_styles' ] );
        
        // Ensure titles remain visible in admin
        add_action( 'admin_head', [ $this, 'ensure_admin_titles_visible' ] );
    }

    /**
     * Add CSS to hide page titles on frontend
     */
    public function add_title_hiding_css() {
        if ( is_admin() ) return; // Don't hide in admin
        
        ?>
        <style id="ochd-title-manager">
        /* OCHandyDude Title Manager - Hide page titles globally */
        
        /* Hide main page titles */
        .entry-title,
        .page-title,
        .post-title,
        h1.entry-title,
        h1.page-title,
        h1.post-title,
        .wp-block-post-title,
        .entry-header .entry-title,
        .page-header .page-title,
        .single-title,
        .archive-title {
            display: none !important;
        }
        
        /* Remove spacing/padding left by hidden titles */
        .entry-header,
        .page-header,
        .post-header,
        .content-header,
        .title-wrapper,
        .wp-block-post-title {
            margin: 0 !important;
            padding: 0 !important;
            min-height: 0 !important;
            height: auto !important;
        }
        
        /* Ensure content flows properly without title spacing */
        .entry-content,
        .page-content,
        .post-content,
        .main-content,
        .content-area {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        
        /* Handle common theme-specific title selectors */
        .site-main .entry-title,
        .site-content .page-title,
        .content-area .entry-title,
        .primary .page-title,
        .main .entry-title,
        article .entry-title,
        .hentry .entry-title {
            display: none !important;
        }
        
        /* Remove title containers that might leave empty space */
        .title-container:empty,
        .entry-header:empty,
        .page-header:empty {
            display: none !important;
        }
        
        /* Specific theme compatibility */
        
        /* Twenty Twenty-One, Twenty Twenty-Two, Twenty Twenty-Three */
        .wp-block-group .wp-block-post-title,
        .entry-header .wp-block-post-title {
            display: none !important;
        }
        
        /* Astra Theme */
        .ast-single-post .entry-title,
        .ast-page-builder-template .entry-title {
            display: none !important;
        }
        
        /* GeneratePress */
        .page-header,
        .entry-header {
            display: none !important;
        }
        
        /* OceanWP */
        .page-header-title,
        .blog-entry-title {
            display: none !important;
        }
        
        /* Elementor */
        .elementor-page-title,
        .elementor-heading-title {
            display: block !important; /* Don't hide Elementor custom titles */
        }
        
        /* Preserve our custom styled titles */
        .ochd-profile-container h2,
        .ochd-bookings-container h2,
        .ochd-profile-card h3,
        .ochd-tab-content h3 {
            display: block !important;
        }
        </style>
        <?php
    }

    /**
     * Filter page titles on frontend (additional layer of protection)
     */
    public function filter_frontend_title( $title, $id = null ) {
        // Only filter on frontend, not in admin
        if ( is_admin() ) {
            return $title;
        }
        
        // Only filter main page/post titles, not widget titles or other elements
        if ( in_the_loop() && is_main_query() && ( is_page() || is_single() ) ) {
            // Return empty string to hide the title
            return '';
        }
        
        return $title;
    }

    /**
     * Enqueue additional styles for title management
     */
    public function enqueue_title_styles() {
        if ( is_admin() ) return;
        
        wp_add_inline_style( 'wp-block-library', '
            /* Additional title hiding for block themes */
            .wp-site-blocks .wp-block-post-title,
            .wp-site-blocks .entry-title {
                display: none !important;
            }
            
            /* Ensure proper spacing without titles */
            .wp-site-blocks .wp-block-group {
                margin-top: 0 !important;
            }
        ' );
    }

    /**
     * Ensure titles remain visible in WordPress admin
     */
    public function ensure_admin_titles_visible() {
        ?>
        <style id="ochd-admin-title-visibility">
        /* Ensure all titles are visible in WordPress admin */
        .entry-title,
        .page-title,
        .post-title,
        .wp-block-post-title,
        #title,
        #titlediv input,
        .editor-post-title,
        .editor-post-title__input {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* Ensure title editing fields are visible */
        #titlediv,
        .editor-post-title {
            display: block !important;
        }
        </style>
        <?php
    }

    /**
     * Check if current page should have title hidden
     */
    public function should_hide_title() {
        // Always hide on frontend, never in admin
        if ( is_admin() ) {
            return false;
        }
        
        // Hide on all pages and posts by default
        if ( is_page() || is_single() ) {
            return true;
        }
        
        // Hide on archive pages
        if ( is_archive() || is_home() ) {
            return true;
        }
        
        return false;
    }

    /**
     * Get hidden title for SEO purposes (still available for meta tags, etc.)
     */
    public function get_hidden_title( $post_id = null ) {
        if ( ! $post_id ) {
            $post_id = get_the_ID();
        }
        
        return get_the_title( $post_id );
    }

    /**
     * Debug function to check title hiding status
     */
    public function debug_title_status() {
        if ( ! current_user_can( 'manage_options' ) ) {
            return;
        }
        
        echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;">';
        echo '<strong>OCHandyDude Title Manager Debug:</strong><br>';
        echo 'Is Admin: ' . ( is_admin() ? 'Yes' : 'No' ) . '<br>';
        echo 'Should Hide Title: ' . ( $this->should_hide_title() ? 'Yes' : 'No' ) . '<br>';
        echo 'Current Title: ' . esc_html( get_the_title() ) . '<br>';
        echo 'Page ID: ' . get_the_ID() . '<br>';
        echo '</div>';
    }
}

// Initialize the title manager
new OCHandyDude_Title_Manager();
