<?php

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_Admin_Settings {

    private static $instance = null;

    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action( 'admin_menu', [ $this, 'add_admin_menu' ] );
        add_action( 'admin_init', [ $this, 'register_settings' ] );
        add_action( 'wp_ajax_ochd_test_invoice_ninja_connection', [ $this, 'ajax_test_connection' ] );
        add_action( 'wp_ajax_ochd_switch_profile_mode', [ $this, 'ajax_switch_profile_mode' ] );
        add_action( 'wp_ajax_ochd_easyappointments_connect', [ $this, 'ajax_easyappointments_connect' ] );
        add_action( 'wp_ajax_ochd_easyappointments_disconnect', [ $this, 'ajax_easyappointments_disconnect' ] );
        add_action( 'wp_ajax_ochd_easyappointments_verify', [ $this, 'ajax_easyappointments_verify' ] );
        add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_admin_scripts' ] );

        // Handle settings updates
        add_action( 'update_option_ochd_profile_display_mode', [ $this, 'handle_profile_mode_change' ], 10, 2 );
    }

    public function add_admin_menu() {
        add_options_page(
            'OCHandyDude Settings',
            'OCHandyDude',
            'manage_options',
            'ochandydude-settings',
            [ $this, 'render_settings_page' ]
        );
    }

    public function register_settings() {
        // Register Profile Display Mode settings
        register_setting( 'ochandydude_settings', 'ochd_profile_display_mode', [
            'type' => 'string',
            'default' => 'separate',
            'sanitize_callback' => [ $this, 'sanitize_profile_display_mode' ]
        ] );

        // Register Invoice Ninja settings
        register_setting( 'ochandydude_settings', 'ochd_invoice_ninja_api_url' );
        register_setting( 'ochandydude_settings', 'ochd_invoice_ninja_api_token' );

        // Register Easy!Appointments settings (using same option name as original plugin for compatibility)
        register_setting( 'ochandydude_settings', 'easyappointments_url', [
            'type' => 'string',
            'sanitize_callback' => [ $this, 'sanitize_easyappointments_url' ]
        ] );

        // Register additional security settings
        register_setting( 'ochandydude_settings', 'ochd_keycloak_account_url', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_url'
        ] );

        register_setting( 'ochandydude_settings', 'ochd_booking_cors_origin', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_url'
        ] );

        // Register debug logging setting
        register_setting( 'ochandydude_settings', 'ochd_enable_debug_logging', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ] );

        // Profile Display Mode Section
        add_settings_section(
            'ochd_profile_display_section',
            'Profile Display Mode',
            [ $this, 'render_profile_display_section' ],
            'ochandydude_settings'
        );

        add_settings_field(
            'ochd_profile_display_mode',
            'Display Mode',
            [ $this, 'render_profile_display_mode_field' ],
            'ochandydude_settings',
            'ochd_profile_display_section'
        );

        // Invoice Ninja Integration Section
        add_settings_section(
            'ochd_invoice_ninja_section',
            'Invoice Ninja Integration',
            [ $this, 'render_invoice_ninja_section' ],
            'ochandydude_settings'
        );

        add_settings_field(
            'ochd_invoice_ninja_api_url',
            'Invoice Ninja API URL',
            [ $this, 'render_api_url_field' ],
            'ochandydude_settings',
            'ochd_invoice_ninja_section'
        );

        add_settings_field(
            'ochd_invoice_ninja_api_token',
            'Invoice Ninja API Token',
            [ $this, 'render_api_token_field' ],
            'ochandydude_settings',
            'ochd_invoice_ninja_section'
        );

        // Easy!Appointments Integration Section
        add_settings_section(
            'ochd_easyappointments_section',
            'Easy!Appointments Integration',
            [ $this, 'render_easyappointments_section' ],
            'ochandydude_settings'
        );

        add_settings_field(
            'easyappointments_url',
            'Easy!Appointments URL',
            [ $this, 'render_easyappointments_url_field' ],
            'ochandydude_settings',
            'ochd_easyappointments_section'
        );

        // Security Configuration Section
        add_settings_section(
            'ochd_security_section',
            'Security Configuration',
            [ $this, 'render_security_section' ],
            'ochandydude_settings'
        );

        add_settings_field(
            'ochd_keycloak_account_url',
            'Keycloak Account URL',
            [ $this, 'render_keycloak_url_field' ],
            'ochandydude_settings',
            'ochd_security_section'
        );

        add_settings_field(
            'ochd_booking_cors_origin',
            'Booking CORS Origin',
            [ $this, 'render_booking_cors_field' ],
            'ochandydude_settings',
            'ochd_security_section'
        );

        // Debug & Troubleshooting Section
        add_settings_section(
            'ochd_debug_section',
            'Debug & Troubleshooting',
            [ $this, 'render_debug_section' ],
            'ochandydude_settings'
        );

        add_settings_field(
            'ochd_enable_debug_logging',
            'Enable Debug Logging',
            [ $this, 'render_debug_logging_field' ],
            'ochandydude_settings',
            'ochd_debug_section'
        );
    }

    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1>OCHandyDude Settings</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields( 'ochandydude_settings' );
                do_settings_sections( 'ochandydude_settings' );
                submit_button();
                ?>

                <div class="ochd-test-connection">
                    <h3>Test API Connection</h3>
                    <p>Test your Invoice Ninja API connection to ensure everything is working correctly.</p>
                    <button type="button" id="ochd-test-connection-btn" class="button button-secondary">Test Connection</button>
                    <div id="ochd-connection-result" style="margin-top: 10px;"></div>
                </div>

                <div class="ochd-test-connection">
                    <h3>Easy!Appointments Connection</h3>
                    <p>Test your Easy!Appointments installation connection and manage the booking form integration.</p>
                    <button type="button" id="ochd-easyappointments-connect-btn" class="button button-primary">Connect</button>
                    <button type="button" id="ochd-easyappointments-disconnect-btn" class="button button-secondary" style="display: none;">Disconnect</button>
                    <button type="button" id="ochd-easyappointments-verify-btn" class="button button-secondary">Verify Connection</button>
                    <div id="ochd-easyappointments-result" style="margin-top: 10px;"></div>
                </div>
            </form>
            
            <div class="ochd-settings-help">
                <h3>Setup Instructions</h3>
                <ol>
                    <li><strong>Invoice Ninja API URL:</strong> Enter your Invoice Ninja installation URL (e.g., https://billing.ochandydude.pro)</li>
                    <li><strong>API Token:</strong> Generate an API token in your Invoice Ninja admin panel under Settings > Account Management > API Tokens</li>
                    <li>Make sure your Invoice Ninja installation is accessible and the API token has the necessary permissions</li>
                    <li>Test the connection by visiting the "My Bookings" page on your website</li>
                </ol>
                
                <h3>Required Permissions</h3>
                <p>The API token should have the following permissions:</p>
                <ul>
                    <li>View Clients</li>
                    <li>View Projects</li>
                    <li>View Tasks</li>
                    <li>View Invoices</li>
                </ul>

                <h3>Easy!Appointments Setup</h3>
                <ol>
                    <li><strong>Easy!Appointments URL:</strong> Enter the public URL of your Easy!Appointments installation (e.g., https://yourdomain.com/easyappointments)</li>
                    <li>Click "Connect" to establish the connection and verify the installation</li>
                    <li>Use the existing <code>[ochandydude_booking_form]</code> shortcode in your pages - no changes needed!</li>
                    <li>The booking form will automatically use your configured Easy!Appointments installation</li>
                </ol>

                <h3>Migration from Easy!Appointments Plugin</h3>
                <p>If you're migrating from the standalone Easy!Appointments plugin:</p>
                <ul>
                    <li>Your existing URL configuration will be automatically preserved</li>
                    <li>No shortcode changes are required - everything continues to work as before</li>
                    <li>You can safely deactivate and remove the Easy!Appointments plugin after configuring this integration</li>
                </ul>
            </div>
        </div>
        
        <style>
        .ochd-settings-help {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-top: 30px;
        }
        .ochd-settings-help h3 {
            margin-top: 0;
            color: #0073aa;
        }
        .ochd-settings-help ol, .ochd-settings-help ul {
            padding-left: 20px;
        }
        .ochd-settings-help li {
            margin-bottom: 8px;
        }
        .ochd-test-connection {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }
        .ochd-connection-success {
            color: #46b450;
            font-weight: bold;
        }
        .ochd-connection-error {
            color: #dc3232;
            font-weight: bold;
        }
        </style>
        <?php
    }

    public function enqueue_admin_scripts( $hook ) {
        if ( $hook !== 'settings_page_ochandydude-settings' ) {
            return;
        }

        wp_enqueue_script( 'jquery' );

        // Enqueue admin testing script
        wp_enqueue_script(
            'ochd-admin-profile-mode-test',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/admin-profile-mode-test.js',
            [ 'jquery' ],
            '1.0',
            true
        );
        wp_add_inline_script( 'jquery', '
            jQuery(document).ready(function($) {
                // Invoice Ninja connection test
                $("#ochd-test-connection-btn").click(function() {
                    var $btn = $(this);
                    var $result = $("#ochd-connection-result");

                    $btn.prop("disabled", true).text("Testing...");
                    $result.html("");

                    $.post(ajaxurl, {
                        action: "ochd_test_invoice_ninja_connection",
                        nonce: "' . wp_create_nonce( 'ochd_test_connection' ) . '"
                    }, function(response) {
                        if (response.success) {
                            $result.html("<div class=\"ochd-connection-success\">✓ Connection successful!</div>");
                        } else {
                            $result.html("<div class=\"ochd-connection-error\">✗ Connection failed: " + response.data.message + "</div>");
                        }
                    }).fail(function() {
                        $result.html("<div class=\"ochd-connection-error\">✗ Connection test failed</div>");
                    }).always(function() {
                        $btn.prop("disabled", false).text("Test Connection");
                    });
                });

                // Profile mode preview
                $("#ochd-preview-mode-btn").click(function() {
                    var $btn = $(this);
                    var $result = $("#ochd-mode-preview-result");
                    var selectedMode = $("input[name=\"ochd_profile_display_mode\"]:checked").val();

                    if (!selectedMode) {
                        $result.html("<div class=\"ochd-connection-error\">Please select a display mode first.</div>");
                        return;
                    }

                    $btn.prop("disabled", true).text("Generating Preview...");
                    $result.html("");

                    var previewUrl = "";
                    if (selectedMode === "separate") {
                        previewUrl = "' . home_url('/my-profile/') . '";
                    } else {
                        previewUrl = "' . home_url('/my-account/') . '";
                    }

                    $result.html("<div class=\"ochd-connection-success\">✓ Preview URL: <a href=\"" + previewUrl + "\" target=\"_blank\">" + previewUrl + "</a></div>");
                    $btn.prop("disabled", false).text("Preview Selected Mode");
                });

                // Handle mode change with visual feedback
                $("input[name=\"ochd_profile_display_mode\"]").change(function() {
                    var selectedMode = $(this).val();
                    var $result = $("#ochd-mode-preview-result");

                    if (selectedMode === "unified") {
                        $result.html("<div style=\"background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; border-radius: 4px; margin-top: 10px;\"><strong>Note:</strong> When you save these settings, the unified profile page will be created automatically if it doesn\'t exist.</div>");
                    } else {
                        $result.html("");
                    }
                });

                // Easy!Appointments connection handlers
                $("#ochd-easyappointments-connect-btn").click(function() {
                    var $btn = $(this);
                    var $result = $("#ochd-easyappointments-result");
                    var url = $("input[name=\"easyappointments_url\"]").val();

                    if (!url) {
                        $result.html("<div class=\"ochd-connection-error\">Please enter an Easy!Appointments URL first.</div>");
                        return;
                    }

                    $btn.prop("disabled", true).text("Connecting...");
                    $result.html("");

                    $.post(ajaxurl, {
                        action: "ochd_easyappointments_connect",
                        url: url,
                        nonce: "' . wp_create_nonce( 'ochd_easyappointments' ) . '"
                    }, function(response) {
                        if (response.success) {
                            $result.html("<div class=\"ochd-connection-success\">✓ " + response.data.message + "</div>");
                            $("#ochd-easyappointments-disconnect-btn").show();
                        } else {
                            $result.html("<div class=\"ochd-connection-error\">✗ " + response.data.message + "</div>");
                        }
                    }).fail(function() {
                        $result.html("<div class=\"ochd-connection-error\">✗ Connection failed</div>");
                    }).always(function() {
                        $btn.prop("disabled", false).text("Connect");
                    });
                });

                $("#ochd-easyappointments-disconnect-btn").click(function() {
                    if (!confirm("Are you sure you want to disconnect Easy!Appointments?")) {
                        return;
                    }

                    var $btn = $(this);
                    var $result = $("#ochd-easyappointments-result");

                    $btn.prop("disabled", true).text("Disconnecting...");

                    $.post(ajaxurl, {
                        action: "ochd_easyappointments_disconnect",
                        nonce: "' . wp_create_nonce( 'ochd_easyappointments' ) . '"
                    }, function(response) {
                        if (response.success) {
                            $result.html("<div class=\"ochd-connection-success\">✓ " + response.data.message + "</div>");
                            $btn.hide();
                        } else {
                            $result.html("<div class=\"ochd-connection-error\">✗ " + response.data.message + "</div>");
                        }
                    }).fail(function() {
                        $result.html("<div class=\"ochd-connection-error\">✗ Disconnection failed</div>");
                    }).always(function() {
                        $btn.prop("disabled", false).text("Disconnect");
                    });
                });

                $("#ochd-easyappointments-verify-btn").click(function() {
                    var $btn = $(this);
                    var $result = $("#ochd-easyappointments-result");

                    $btn.prop("disabled", true).text("Verifying...");

                    $.post(ajaxurl, {
                        action: "ochd_easyappointments_verify",
                        nonce: "' . wp_create_nonce( 'ochd_easyappointments' ) . '"
                    }, function(response) {
                        if (response.success) {
                            $result.html("<div class=\"ochd-connection-success\">✓ " + response.data.message + "</div>");
                            $("#ochd-easyappointments-disconnect-btn").show();
                        } else {
                            $result.html("<div class=\"ochd-connection-error\">✗ " + response.data.message + "</div>");
                        }
                    }).fail(function() {
                        $result.html("<div class=\"ochd-connection-error\">✗ Verification failed</div>");
                    }).always(function() {
                        $btn.prop("disabled", false).text("Verify Connection");
                    });
                });

                // Show disconnect button if URL is already configured
                if ($("input[name=\"easyappointments_url\"]").val()) {
                    $("#ochd-easyappointments-disconnect-btn").show();
                }
            });
        ' );
    }

    public function ajax_test_connection() {
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_test_connection' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid nonce' ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [ 'message' => 'Insufficient permissions' ] );
        }

        $invoice_ninja = OCHD_Invoice_Ninja_Integration::get_instance();
        $result = $invoice_ninja->test_api_connection();

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( [ 'message' => $result->get_error_message() ] );
        }

        wp_send_json_success( [ 'message' => 'Connection successful' ] );
    }

    public function ajax_easyappointments_connect() {
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_easyappointments' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid nonce' ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [ 'message' => 'Insufficient permissions' ] );
        }

        $url = sanitize_url( $_POST['url'] ?? '' );
        if ( empty( $url ) ) {
            wp_send_json_error( [ 'message' => 'URL is required' ] );
        }

        // Validate URL format
        if ( ! filter_var( $url, FILTER_VALIDATE_URL ) ) {
            wp_send_json_error( [ 'message' => 'Invalid URL format' ] );
        }

        // Test the connection by making a simple request
        $response = wp_remote_get( $url, [
            'timeout' => 10,
            'sslverify' => false
        ] );

        if ( is_wp_error( $response ) ) {
            wp_send_json_error( [ 'message' => 'Connection failed: ' . $response->get_error_message() ] );
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        if ( $response_code !== 200 ) {
            wp_send_json_error( [ 'message' => 'Connection failed: HTTP ' . $response_code ] );
        }

        // Save the URL
        update_option( 'easyappointments_url', $url );

        wp_send_json_success( [ 'message' => 'Easy!Appointments connected successfully' ] );
    }

    public function ajax_easyappointments_disconnect() {
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_easyappointments' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid nonce' ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [ 'message' => 'Insufficient permissions' ] );
        }

        // Remove the URL
        delete_option( 'easyappointments_url' );

        wp_send_json_success( [ 'message' => 'Easy!Appointments disconnected successfully' ] );
    }

    public function ajax_easyappointments_verify() {
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_easyappointments' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid nonce' ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [ 'message' => 'Insufficient permissions' ] );
        }

        $url = get_option( 'easyappointments_url' );
        if ( empty( $url ) ) {
            wp_send_json_error( [ 'message' => 'No Easy!Appointments URL configured' ] );
        }

        // Test the connection
        $response = wp_remote_get( $url, [
            'timeout' => 10,
            'sslverify' => false
        ] );

        if ( is_wp_error( $response ) ) {
            wp_send_json_error( [ 'message' => 'Connection failed: ' . $response->get_error_message() ] );
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        if ( $response_code !== 200 ) {
            wp_send_json_error( [ 'message' => 'Connection failed: HTTP ' . $response_code ] );
        }

        wp_send_json_success( [ 'message' => 'Easy!Appointments connection verified successfully' ] );
    }

    public function sanitize_profile_display_mode( $value ) {
        return in_array( $value, [ 'separate', 'unified' ] ) ? $value : 'separate';
    }

    public function sanitize_easyappointments_url( $value ) {
        $value = sanitize_url( $value );

        // Validate URL format
        if ( ! empty( $value ) && ! filter_var( $value, FILTER_VALIDATE_URL ) ) {
            add_settings_error( 'easyappointments_url', 'invalid_url', 'Invalid Easy!Appointments URL format' );
            return get_option( 'easyappointments_url', '' );
        }

        return $value;
    }

    public function render_profile_display_section() {
        echo '<p>Choose how to display the user profile and bookings pages. You can switch between modes at any time.</p>';
    }

    public function render_profile_display_mode_field() {
        $current_mode = get_option( 'ochd_profile_display_mode', 'separate' );
        $unified_page_id = get_option( 'ochd_unified_profile_page_id' );
        $unified_page_exists = $unified_page_id && get_post( $unified_page_id );

        ?>
        <div class="ochd-profile-mode-options">
            <label class="ochd-mode-option">
                <input type="radio" name="ochd_profile_display_mode" value="separate" <?php checked( $current_mode, 'separate' ); ?> />
                <div class="ochd-mode-card">
                    <h4>🔗 Separate Pages Mode (Legacy)</h4>
                    <p>Uses individual "My Profile" and "My Bookings" pages with direct navigation.</p>
                    <ul>
                        <li>✓ Backward compatible</li>
                        <li>✓ Simple page structure</li>
                        <li>✓ Direct URL access</li>
                    </ul>
                </div>
            </label>

            <label class="ochd-mode-option">
                <input type="radio" name="ochd_profile_display_mode" value="unified" <?php checked( $current_mode, 'unified' ); ?> />
                <div class="ochd-mode-card">
                    <h4>🎠 Unified Profile Mode (New)</h4>
                    <p>Modern carousel-style interface combining both sections in one page.</p>
                    <ul>
                        <li>✓ Smooth carousel navigation</li>
                        <li>✓ Mobile swipe support</li>
                        <li>✓ Enhanced user experience</li>
                        <li>✓ Hash-based navigation</li>
                    </ul>
                    <?php if ( ! $unified_page_exists ): ?>
                        <div class="ochd-mode-notice">
                            <strong>Note:</strong> Unified profile page will be created automatically when this mode is selected.
                        </div>
                    <?php endif; ?>
                </div>
            </label>
        </div>

        <div class="ochd-mode-actions">
            <button type="button" id="ochd-preview-mode-btn" class="button button-secondary">Preview Selected Mode</button>
            <div id="ochd-mode-preview-result" style="margin-top: 10px;"></div>
        </div>

        <style>
        .ochd-profile-mode-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .ochd-mode-option {
            cursor: pointer;
            display: block;
        }

        .ochd-mode-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .ochd-mode-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fff;
            transition: all 0.3s ease;
            height: 100%;
        }

        .ochd-mode-option input[type="radio"]:checked + .ochd-mode-card {
            border-color: #0073aa;
            background: #f0f8ff;
            box-shadow: 0 2px 8px rgba(0, 115, 170, 0.2);
        }

        .ochd-mode-card:hover {
            border-color: #0073aa;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .ochd-mode-card h4 {
            margin: 0 0 10px 0;
            color: #0073aa;
            font-size: 16px;
        }

        .ochd-mode-card p {
            margin: 0 0 15px 0;
            color: #666;
        }

        .ochd-mode-card ul {
            margin: 0;
            padding-left: 20px;
        }

        .ochd-mode-card li {
            margin-bottom: 5px;
            color: #555;
        }

        .ochd-mode-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-top: 15px;
            font-size: 13px;
        }

        .ochd-mode-actions {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }

        @media (max-width: 768px) {
            .ochd-profile-mode-options {
                grid-template-columns: 1fr;
            }
        }
        </style>
        <?php
    }

    public function ajax_switch_profile_mode() {
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_switch_profile_mode' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid nonce' ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [ 'message' => 'Insufficient permissions' ] );
        }

        $new_mode = sanitize_text_field( $_POST['mode'] ?? '' );
        if ( ! in_array( $new_mode, [ 'separate', 'unified' ] ) ) {
            wp_send_json_error( [ 'message' => 'Invalid mode' ] );
        }

        // Handle mode switch
        $result = $this->switch_profile_mode( $new_mode );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( [ 'message' => $result->get_error_message() ] );
        }

        wp_send_json_success( [
            'message' => 'Profile mode switched successfully',
            'mode' => $new_mode,
            'page_url' => $result['page_url'] ?? ''
        ] );
    }

    public function handle_profile_mode_change( $old_value, $new_value ) {
        if ( $old_value !== $new_value ) {
            $this->switch_profile_mode( $new_value );
        }
    }

    public function switch_profile_mode( $mode ) {
        if ( $mode === 'unified' ) {
            return $this->create_unified_profile_page();
        } else {
            return $this->ensure_separate_pages_exist();
        }
    }

    public function create_unified_profile_page() {
        // Check if unified profile page already exists
        $existing_page_id = get_option( 'ochd_unified_profile_page_id' );
        if ( $existing_page_id && get_post( $existing_page_id ) ) {
            return [
                'page_id' => $existing_page_id,
                'page_url' => get_permalink( $existing_page_id ),
                'created' => false
            ];
        }

        // Create the unified profile page
        $page_data = [
            'post_title'     => 'My Account',
            'post_content'   => '[ochd_unified_profile]',
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'post_name'      => 'my-account',
            'post_author'    => 1,
            'comment_status' => 'closed',
            'ping_status'    => 'closed',
            'meta_input'     => [
                '_wp_page_template' => 'default',
                'ochd_page_type'    => 'unified_profile'
            ]
        ];

        $page_id = wp_insert_post( $page_data );

        if ( is_wp_error( $page_id ) ) {
            return new WP_Error( 'page_creation_failed', 'Failed to create unified profile page: ' . $page_id->get_error_message() );
        }

        // Store the page ID
        update_option( 'ochd_unified_profile_page_id', $page_id );

        // Update the profile display mode option
        update_option( 'ochd_use_unified_profile', true );

        return [
            'page_id' => $page_id,
            'page_url' => get_permalink( $page_id ),
            'created' => true
        ];
    }

    public function ensure_separate_pages_exist() {
        $profile_page_id = get_option( 'ochd_profile_page_id' );
        $bookings_page_id = get_option( 'ochd_bookings_page_id' );

        $results = [];

        // Check/create profile page
        if ( ! $profile_page_id || ! get_post( $profile_page_id ) ) {
            $profile_page_data = [
                'post_title'     => 'My Profile',
                'post_content'   => '[ochd_profile_content]',
                'post_status'    => 'publish',
                'post_type'      => 'page',
                'post_name'      => 'my-profile',
                'post_author'    => 1,
                'comment_status' => 'closed',
                'ping_status'    => 'closed',
                'meta_input'     => [
                    '_wp_page_template' => 'default',
                    'ochd_page_type'    => 'profile'
                ]
            ];

            $profile_page_id = wp_insert_post( $profile_page_data );
            if ( ! is_wp_error( $profile_page_id ) ) {
                update_option( 'ochd_profile_page_id', $profile_page_id );
                $results['profile_created'] = true;
            }
        }

        // Check/create bookings page
        if ( ! $bookings_page_id || ! get_post( $bookings_page_id ) ) {
            $bookings_page_data = [
                'post_title'     => 'My Bookings',
                'post_content'   => '[ochd_bookings_content]',
                'post_status'    => 'publish',
                'post_type'      => 'page',
                'post_name'      => 'my-bookings',
                'post_author'    => 1,
                'comment_status' => 'closed',
                'ping_status'    => 'closed',
                'meta_input'     => [
                    '_wp_page_template' => 'default',
                    'ochd_page_type'    => 'bookings'
                ]
            ];

            $bookings_page_id = wp_insert_post( $bookings_page_data );
            if ( ! is_wp_error( $bookings_page_id ) ) {
                update_option( 'ochd_bookings_page_id', $bookings_page_id );
                $results['bookings_created'] = true;
            }
        }

        // Update the profile display mode option
        update_option( 'ochd_use_unified_profile', false );

        $results['profile_page_url'] = get_permalink( get_option( 'ochd_profile_page_id' ) );
        $results['bookings_page_url'] = get_permalink( get_option( 'ochd_bookings_page_id' ) );

        return $results;
    }

    public function render_invoice_ninja_section() {
        echo '<p>Configure your Invoice Ninja API connection to display projects, tasks, and invoices in the "My Bookings" section.</p>';
    }

    public function render_api_url_field() {
        $value = get_option( 'ochd_invoice_ninja_api_url', 'https://billing.ochandydude.pro' );
        echo '<input type="url" name="ochd_invoice_ninja_api_url" value="' . esc_attr( $value ) . '" class="regular-text" placeholder="https://billing.ochandydude.pro" />';
        echo '<p class="description">The base URL of your Invoice Ninja installation (without trailing slash)</p>';
    }

    public function render_api_token_field() {
        $value = get_option( 'ochd_invoice_ninja_api_token', '' );
        echo '<input type="password" name="ochd_invoice_ninja_api_token" value="' . esc_attr( $value ) . '" class="regular-text" placeholder="Enter your API token" />';
        echo '<p class="description">Generate this token in Invoice Ninja under Settings > Account Management > API Tokens</p>';
    }

    public function render_easyappointments_section() {
        echo '<p>Configure your Easy!Appointments installation to enable booking form functionality. This replaces the need for the separate Easy!Appointments plugin.</p>';
    }

    public function render_easyappointments_url_field() {
        $value = get_option( 'easyappointments_url', '' );
        echo '<input type="url" name="easyappointments_url" value="' . esc_attr( $value ) . '" class="regular-text" placeholder="https://yourdomain.com/easyappointments" />';
        echo '<p class="description">The public URL of your Easy!Appointments installation (without trailing slash)</p>';

        if ( ! empty( $value ) ) {
            echo '<p class="description"><strong>Current Status:</strong> <span style="color: #46b450;">✓ Connected</span> | <a href="' . esc_url( $value ) . '" target="_blank">Open Easy!Appointments</a></p>';
        }
    }

    public function render_security_section() {
        echo '<p>Configure security-related URLs and origins for proper plugin operation. These settings were previously hardcoded and are now configurable for security.</p>';
    }

    public function render_keycloak_url_field() {
        $value = get_option( 'ochd_keycloak_account_url', '' );
        echo '<input type="url" name="ochd_keycloak_account_url" value="' . esc_attr( $value ) . '" class="regular-text" placeholder="https://auth.yourdomain.com/realms/yourealm/account/" />';
        echo '<p class="description">The Keycloak account management URL for SSO integration</p>';
    }

    public function render_booking_cors_field() {
        $value = get_option( 'ochd_booking_cors_origin', '' );
        echo '<input type="url" name="ochd_booking_cors_origin" value="' . esc_attr( $value ) . '" class="regular-text" placeholder="https://book.yourdomain.com" />';
        echo '<p class="description">The allowed CORS origin for booking form AJAX requests (optional)</p>';
    }

    public function render_debug_section() {
        echo '<p>Configure debug logging to troubleshoot booking form height issues and other integration problems. Debug logging provides detailed information about plugin operations.</p>';

        $wp_debug_status = defined( 'WP_DEBUG' ) && WP_DEBUG ? 'enabled' : 'disabled';
        echo '<p><strong>WordPress Debug Mode:</strong> <span style="color: ' . ( $wp_debug_status === 'enabled' ? '#46b450' : '#dc3232' ) . ';">' . ucfirst( $wp_debug_status ) . '</span></p>';

        if ( $wp_debug_status === 'disabled' ) {
            echo '<p class="description"><em>Note: With WP_DEBUG disabled, logs will only appear in browser console. Enable WP_DEBUG in wp-config.php for file logging.</em></p>';
        } else {
            echo '<p class="description"><em>WP_DEBUG is enabled. Logs will appear in both browser console and WordPress debug.log file.</em></p>';
        }
    }

    public function render_debug_logging_field() {
        $value = get_option( 'ochd_enable_debug_logging', false );
        echo '<label>';
        echo '<input type="checkbox" name="ochd_enable_debug_logging" value="1" ' . checked( 1, $value, false ) . ' />';
        echo ' Enable comprehensive logging for troubleshooting booking form and integration issues';
        echo '</label>';
        echo '<p class="description">When enabled, detailed logs will help diagnose booking form height truncation, API integration issues, and other plugin problems.</p>';

        if ( $value ) {
            echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-top: 10px; border-radius: 4px;">';
            echo '<strong>⚠️ Debug Logging Active</strong><br>';
            echo 'Detailed logging is currently enabled. Disable this setting in production for optimal performance.';
            echo '</div>';
        }
    }
}
