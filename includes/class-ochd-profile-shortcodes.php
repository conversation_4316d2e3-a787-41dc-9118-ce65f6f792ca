<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class OCHandyDude_Profile_Shortcodes {

    public function __construct() {
        add_shortcode( 'ochandydude_profile_menu', [ $this, 'render_profile_menu' ] );
        add_shortcode( 'ochd_profile_content', [ $this, 'render_profile_page_content' ] );
        add_shortcode( 'ochd_bookings_content', [ $this, 'render_bookings_page_content' ] );
        add_shortcode( 'ochd_unified_profile', [ $this, 'render_unified_profile_content' ] );
        add_shortcode( 'ochd_custom_booking_form', [ $this, 'ochd_custom_booking_form_shortcode' ] );
    }

    /**
     * Renders the profile menu.
     */
    public function render_profile_menu() {
        ob_start();
        $login_url = do_shortcode('[openid_connect_generic_auth_url]');
        ?>
        <div class="ochd-profile-menu ochd-unified-icon-wrapper">
            <div class="ochd-profile-icon" aria-label="User Profile">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div class="ochd-profile-dropdown">
                <?php if ( is_user_logged_in() ) : ?>
                    <?php
                    $current_user = wp_get_current_user();

                    // Get user's first and last name, with fallbacks
                    $first_name = get_user_meta( $current_user->ID, 'first_name', true );
                    $last_name = get_user_meta( $current_user->ID, 'last_name', true );

                    // Create display name from first and last name
                    $display_name = trim( $first_name . ' ' . $last_name );

                    // Fallback to display_name if first/last names are empty
                    if ( empty( $display_name ) ) {
                        $display_name = $current_user->display_name;
                    }

                    // Final fallback to email username if display_name is also empty or looks like UUID
                    if ( empty( $display_name ) || preg_match( '/^[a-f0-9\-]{36}$/i', $display_name ) ) {
                        $email_parts = explode( '@', $current_user->user_email );
                        $display_name = $email_parts[0];
                    }

                    // Get page URLs based on display mode
                    $display_mode = get_option('ochd_profile_display_mode', 'separate');
                    $use_unified_profile = ($display_mode === 'unified');

                    if ($use_unified_profile) {
                        // Use unified profile page with hash navigation
                        $unified_page_id = get_option('ochd_unified_profile_page_id');

                        if ($unified_page_id && get_post($unified_page_id)) {
                            $unified_page_url = get_permalink($unified_page_id);
                            $profile_page_url = $unified_page_url . '#profile';
                            $bookings_page_url = $unified_page_url . '#bookings';
                        } else {
                            // Fallback: create unified page if it doesn't exist
                            $admin_settings = OCHD_Admin_Settings::get_instance();
                            $result = $admin_settings->create_unified_profile_page();

                            if (!is_wp_error($result)) {
                                $unified_page_url = $result['page_url'];
                                $profile_page_url = $unified_page_url . '#profile';
                                $bookings_page_url = $unified_page_url . '#bookings';
                            } else {
                                // Final fallback to separate pages
                                $profile_page_url = home_url('/my-profile/');
                                $bookings_page_url = home_url('/my-bookings/');
                                $use_unified_profile = false;
                            }
                        }
                    } else {
                        // Use separate pages (legacy mode)
                        $profile_page_id = get_option('ochd_profile_page_id');
                        $bookings_page_id = get_option('ochd_bookings_page_id');

                        $profile_page_url = $profile_page_id ? get_permalink($profile_page_id) : home_url('/my-profile/');
                        $bookings_page_url = $bookings_page_id ? get_permalink($bookings_page_id) : home_url('/my-bookings/');

                        // Ensure URLs are valid
                        if (!$profile_page_url || $profile_page_url === home_url('/')) {
                            $profile_page_url = home_url('/my-profile/');
                        }
                        if (!$bookings_page_url || $bookings_page_url === home_url('/') ) {
                            $bookings_page_url = home_url('/my-bookings/');
                        }
                    }

                    $keycloak_account_url = apply_filters( 'ochd_keycloak_account_url', 'https://auth.ochandydude.pro/realms/ochandydude/account/' );
                    $logout_url = wp_logout_url( home_url() );
                    ?>
                    <div class="ochd-dropdown-header">
                        <strong><?php echo esc_html( $display_name ); ?></strong>
                        <small><?php echo esc_html( $current_user->user_email ); ?></small>
                    </div>
                    <a href="<?php echo esc_url( $profile_page_url ); ?>"
                       class="ochd-menu-link"
                       data-action="navigate"
                       <?php echo $use_unified_profile ? 'data-carousel-tab="profile"' : ''; ?>
                       <?php echo $use_unified_profile ? 'data-unified-mode="true"' : 'data-unified-mode="false"'; ?>>
                        <span class="ochd-menu-icon">👤</span>
                        <span class="ochd-menu-text">My Profile</span>
                    </a>
                    <a href="<?php echo esc_url( $bookings_page_url ); ?>"
                       class="ochd-menu-link"
                       data-action="navigate"
                       <?php echo $use_unified_profile ? 'data-carousel-tab="bookings"' : ''; ?>
                       <?php echo $use_unified_profile ? 'data-unified-mode="true"' : 'data-unified-mode="false"'; ?>>
                        <span class="ochd-menu-icon">📅</span>
                        <span class="ochd-menu-text">My Bookings</span>
                    </a>
                    <a href="<?php echo esc_url( $keycloak_account_url ); ?>" class="ochd-menu-link" data-action="external" target="_blank" rel="noopener noreferrer">
                        <span class="ochd-menu-icon">⚙️</span>
                        <span class="ochd-menu-text">Account Settings</span>
                    </a>
                    <div class="ochd-dropdown-footer">
                        <a href="<?php echo esc_url( $logout_url ); ?>" class="ochd-menu-link" data-action="logout">
                            <span class="ochd-menu-icon">🚪</span>
                            <span class="ochd-menu-text">Logout</span>
                        </a>
                    </div>
                <?php else : ?>
                    <div class="ochd-guest-menu">
                         <a href="<?php echo esc_url( $login_url ); ?>" class="ochd-guest-login-button ochd-menu-link" data-action="login">
                            <span class="ochd-menu-icon">🔑</span>
                            <span class="ochd-menu-text">Login / Register</span>
                         </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Renders the profile page content.
     */
    public function render_profile_page_content() {
        if ( ! is_user_logged_in() ) { 
            return '<p>Please log in to view your profile.</p>'; 
        }
        $user = wp_get_current_user();
        $user_id = $user->ID;
        $keycloak_account_url = apply_filters( 'ochd_keycloak_account_url', 'https://auth.ochandydude.pro/realms/ochandydude/account/' );

        ob_start();
        ?>
        <div class="ochd-profile-container">
            <h2>My Profile</h2>
            <div class="ochd-profile-card">
                <h3>Contact Information</h3>
                <p><strong>Name:</strong> <?php echo esc_html( get_user_meta( $user_id, 'first_name', true ) . ' ' . get_user_meta( $user_id, 'last_name', true ) ); ?></p>
                <p><strong>Email:</strong> <?php echo esc_html( $user->user_email ); ?></p>
                <p><strong>Phone:</strong> <?php echo esc_html( get_user_meta( $user_id, 'billing_phone', true ) ); ?></p>
            </div>
            <div class="ochd-profile-card">
                <h3>Billing Address</h3>
                <p>
                    <?php echo esc_html( get_user_meta( $user_id, 'billing_address_1', true ) ); ?><br>
                    <?php echo esc_html( get_user_meta( $user_id, 'billing_city', true ) . ', ' . get_user_meta( $user_id, 'billing_state', true ) . ' ' . get_user_meta( $user_id, 'billing_postcode', true ) ); ?><br>
                    <?php echo esc_html( get_user_meta( $user_id, 'billing_country', true ) ); ?>
                </p>
            </div>
            <p><a href="<?php echo esc_url( $keycloak_account_url ); ?>" target="_blank" rel="noopener noreferrer">Edit your profile information on our main account server.</a></p>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Renders the bookings page content.
     */
    public function render_bookings_page_content() {
        if ( ! is_user_logged_in() ) {
            return '<p>Please log in to view your bookings.</p>';
        }

        // Enqueue scripts for tabs and Invoice Ninja integration
        wp_enqueue_script(
            'ochandydude-profile-tabs-script',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/profile-tabs.js',
            [],
            '1.0',
            true
        );

        wp_enqueue_script(
            'ochandydude-invoice-ninja-integration',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/invoice-ninja-integration.js',
            [ 'jquery' ],
            '1.0',
            true
        );

        // Localize script with AJAX data
        wp_localize_script(
            'ochandydude-invoice-ninja-integration',
            'ochdInvoiceNinjaData',
            [
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'ochd_invoice_ninja_nonce' ),
            ]
        );

        ob_start();
        ?>
        <div class="ochd-bookings-container">
            <h2>My Bookings</h2>
            <div class="ochd-tabs">
                <button class="ochd-tab-link active" onclick="openOchdTab(event, 'upcoming')">Upcoming Bookings</button>
                <button class="ochd-tab-link" onclick="openOchdTab(event, 'history')">Booking History</button>
                <button class="ochd-tab-link" onclick="openOchdTab(event, 'invoices')">Invoices</button>
            </div>
            <div id="upcoming" class="ochd-tab-content" style="display:block;">
                <div class="ochd-loading-state">
                    <div class="ochd-loading-spinner"></div>
                    <p>Loading your upcoming bookings...</p>
                </div>
            </div>
            <div id="history" class="ochd-tab-content">
                <div class="ochd-loading-state">
                    <div class="ochd-loading-spinner"></div>
                    <p>Loading your booking history...</p>
                </div>
            </div>
            <div id="invoices" class="ochd-tab-content">
                <div class="ochd-loading-state">
                    <div class="ochd-loading-spinner"></div>
                    <p>Loading your invoices...</p>
                </div>
            </div>

            <!-- Invoice Ninja Client Portal Button -->
            <div class="ochd-client-portal-section">
                <p class="ochd-client-portal-description">
                    Access your client portal to manage invoices, make payments, and update your payment methods.
                </p>
                <?php echo do_shortcode( '[invoiceninja_client_portal label="Access Client Portal" sso="true"]' ); ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Renders the unified profile content with carousel tab navigation.
     */
    public function render_unified_profile_content() {
        if ( ! is_user_logged_in() ) {
            return '<p>Please log in to view your profile.</p>';
        }

        // Enqueue scripts for carousel tabs and Invoice Ninja integration
        wp_enqueue_script(
            'ochandydude-carousel-tabs-script',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/carousel-tabs.js',
            ['jquery'],
            '1.0',
            true
        );

        wp_enqueue_script(
            'ochandydude-invoice-ninja-integration',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/invoice-ninja-integration.js',
            [ 'jquery' ],
            '1.0',
            true
        );

        // Enqueue profile tabs script for internal booking tabs
        wp_enqueue_script(
            'ochandydude-profile-tabs-script',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/profile-tabs.js',
            [ 'jquery' ],
            '1.0',
            true
        );

        // Enqueue QA testing script (for development/testing)
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            wp_enqueue_script(
                'ochandydude-unified-profile-qa-test',
                OCHD_MASTER_PLUGIN_URL . 'assets/js/unified-profile-qa-test.js',
                [ 'jquery' ],
                '1.0',
                true
            );


        }

        // Localize script with AJAX data
        wp_localize_script(
            'ochandydude-invoice-ninja-integration',
            'ochdInvoiceNinjaData',
            [
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'ochd_invoice_ninja_nonce' ),
            ]
        );

        // Get user data for profile section
        $user = wp_get_current_user();
        $user_id = $user->ID;
        $keycloak_account_url = apply_filters( 'ochd_keycloak_account_url', get_option( 'ochd_keycloak_account_url', '' ) );

        ob_start();
        ?>
        <div class="ochd-unified-profile-container">
            <!-- Carousel Tab Navigation -->
            <div class="ochd-carousel-tabs">
                <div class="ochd-carousel-track">
                    <button class="ochd-carousel-tab active" data-tab="profile" aria-selected="true">
                        <span class="ochd-tab-icon">👤</span>
                        <span class="ochd-tab-text">My Profile</span>
                    </button>
                    <button class="ochd-carousel-tab" data-tab="bookings" aria-selected="false">
                        <span class="ochd-tab-icon">📅</span>
                        <span class="ochd-tab-text">My Bookings</span>
                    </button>
                </div>
                <div class="ochd-carousel-indicator"></div>
            </div>

            <!-- Profile Tab Content -->
            <div id="profile" class="ochd-carousel-content active">
                <div class="ochd-profile-container">
                    <div class="ochd-profile-card">
                        <h3>Contact Information</h3>
                        <p><strong>Name:</strong> <?php echo esc_html( get_user_meta( $user_id, 'first_name', true ) . ' ' . get_user_meta( $user_id, 'last_name', true ) ); ?></p>
                        <p><strong>Email:</strong> <?php echo esc_html( $user->user_email ); ?></p>
                        <p><strong>Phone:</strong> <?php echo esc_html( get_user_meta( $user_id, 'billing_phone', true ) ); ?></p>
                    </div>
                    <div class="ochd-profile-card">
                        <h3>Billing Address</h3>
                        <p>
                            <?php echo esc_html( get_user_meta( $user_id, 'billing_address_1', true ) ); ?><br>
                            <?php echo esc_html( get_user_meta( $user_id, 'billing_city', true ) . ', ' . get_user_meta( $user_id, 'billing_state', true ) . ' ' . get_user_meta( $user_id, 'billing_postcode', true ) ); ?><br>
                            <?php echo esc_html( get_user_meta( $user_id, 'billing_country', true ) ); ?>
                        </p>
                    </div>
                    <div class="ochd-profile-actions">
                        <a href="<?php echo esc_url( $keycloak_account_url ); ?>" target="_blank" rel="noopener noreferrer" class="ochd-profile-edit-button">
                            <span class="ochd-button-icon">⚙️</span>
                            <span class="ochd-button-text">Edit Profile Information</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Bookings Tab Content -->
            <div id="bookings" class="ochd-carousel-content">
                <div class="ochd-bookings-container">
                    <div class="ochd-tabs">
                        <button class="ochd-tab-link active" onclick="openOchdTab(event, 'upcoming')">Upcoming Bookings</button>
                        <button class="ochd-tab-link" onclick="openOchdTab(event, 'history')">Booking History</button>
                        <button class="ochd-tab-link" onclick="openOchdTab(event, 'invoices')">Invoices</button>
                    </div>
                    <div id="upcoming" class="ochd-tab-content" style="display:block;">
                        <div class="ochd-loading-state">
                            <div class="ochd-loading-spinner"></div>
                            <p>Loading your upcoming bookings...</p>
                        </div>
                    </div>
                    <div id="history" class="ochd-tab-content">
                        <div class="ochd-loading-state">
                            <div class="ochd-loading-spinner"></div>
                            <p>Loading your booking history...</p>
                        </div>
                    </div>
                    <div id="invoices" class="ochd-tab-content">
                        <div class="ochd-loading-state">
                            <div class="ochd-loading-spinner"></div>
                            <p>Loading your invoices...</p>
                        </div>
                    </div>

                    <!-- Custom Styled Invoice Ninja Client Portal Button -->
                    <div class="ochd-client-portal-section">
                        <div class="ochd-profile-actions">
                            <?php echo $this->render_custom_client_portal_button(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Renders a custom styled client portal button that matches the profile edit button
     */
    private function render_custom_client_portal_button() {
        if ( ! is_user_logged_in() ) {
            return '';
        }

        $user = wp_get_current_user();

        // Check if Invoice Ninja is configured
        if ( ! class_exists( '\InvoiceNinja\Api\ClientApi' ) ) {
            return '<p>Invoice Ninja integration not available.</p>';
        }

        try {
            // Get the client portal URL using Invoice Ninja's logic
            $client = \InvoiceNinja\Api\ClientApi::find( $user->user_email );

            if ( ! $client || empty( $client->contacts ) ) {
                return '<p>No client portal access available.</p>';
            }

            foreach ( $client->contacts as $contact ) {
                if ( $contact->email == $user->user_email ) {
                    $url = get_option( 'invoiceninja_api_url' );
                    $url = rtrim( $url, '/' );
                    $url = rtrim( $url, 'api/v1' );
                    $url = rtrim( $url, '/' );

                    if ( ! $url ) {
                        $url = 'https://invoicing.co';
                    }

                    $url .= '/client/key_login/' . $contact->contact_key;
                    $url .= '?client_hash=' . $client->client_hash; // Enable SSO

                    // Return our custom styled button
                    return sprintf(
                        '<a href="%s" target="_blank" rel="noopener noreferrer" class="ochd-profile-edit-button ochd-custom-client-portal-button">
                            <span class="ochd-button-icon">💳</span>
                            <span class="ochd-button-text">Access Billing Portal</span>
                        </a>',
                        esc_url( $url )
                    );
                }
            }
        } catch ( Exception $e ) {
            error_log( 'OCHandyDude: Error generating client portal button: ' . $e->getMessage() );
            return '<p>Client portal temporarily unavailable.</p>';
        }

        return '<p>Client portal access not configured.</p>';
    }

    /**
     * Custom Easy!Appointments booking form implementation
     * Replaces the third-party plugin with our own controlled iframe
     */
    public function ochd_custom_booking_form_shortcode( $atts ) {
        // Get the Easy!Appointments URL from the plugin's option
        $booking_url = get_option( 'easyappointments_url' );

        if ( empty( $booking_url ) ) {
            return '<div class="ochd-booking-error">
                <p>Booking system is not configured. Please contact support.</p>
            </div>';
        }

        // Parse shortcode attributes
        $attributes = shortcode_atts( [
            'provider' => '',
            'service' => '',
            'width' => '100%',
            'height' => 'auto',
            'style' => ''
        ], $atts );

        // Build query parameters
        $query_data = [];
        if ( ! empty( $attributes['provider'] ) ) {
            $query_data['provider'] = $attributes['provider'];
        }
        if ( ! empty( $attributes['service'] ) ) {
            $query_data['service'] = $attributes['service'];
        }

        // Add query parameters to URL
        if ( ! empty( $query_data ) ) {
            if ( strpos( $booking_url, '?' ) === false ) {
                $booking_url .= '?';
            } else {
                $booking_url .= '&';
            }
            $booking_url .= http_build_query( $query_data );
        }

        // Enqueue our custom booking form styles and scripts
        wp_enqueue_style(
            'ochd-custom-booking-form',
            OCHD_MASTER_PLUGIN_URL . 'assets/css/custom-booking-form.css',
            [],
            '1.0'
        );

        wp_enqueue_script(
            'ochd-custom-booking-form',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/custom-booking-form.js',
            [ 'jquery' ],
            '1.0',
            true
        );

        // Generate unique ID for this booking form instance
        $form_id = 'ochd-booking-form-' . uniqid();

        // Return our custom booking form HTML
        ob_start();
        ?>
        <div class="ochd-custom-booking-container" id="<?php echo esc_attr( $form_id ); ?>">
            <div class="ochd-custom-booking-wrapper">
                <iframe
                    class="ochd-custom-booking-iframe"
                    src="<?php echo esc_url( $booking_url ); ?>"
                    width="<?php echo esc_attr( $attributes['width'] ); ?>"
                    style="<?php echo esc_attr( $attributes['style'] ); ?>"
                    frameborder="0"
                    scrolling="no"
                    data-form-id="<?php echo esc_attr( $form_id ); ?>"
                >
                    <p>Your browser does not support iframes. Please <a href="<?php echo esc_url( $booking_url ); ?>" target="_blank">click here</a> to access the booking form.</p>
                </iframe>
            </div>
            <div class="ochd-booking-loading" style="display: none;">
                <div class="ochd-loading-spinner"></div>
                <p>Loading booking form...</p>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
