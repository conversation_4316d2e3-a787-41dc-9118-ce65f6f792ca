<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class OCHandyDude_Profile_Assets {

    public function __construct() {
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_assets' ] );
        add_action( 'init', [ $this, 'ensure_pages_exist' ] );
    }

    /**
     * Ensure profile and bookings pages exist
     */
    public function ensure_pages_exist() {
        // Check if pages exist, create if they don't
        $profile_page_id = get_option('ochd_profile_page_id');
        $bookings_page_id = get_option('ochd_bookings_page_id');

        if (!$profile_page_id || !get_post($profile_page_id)) {
            OCHD_Plugin_Activation::create_plugin_pages();
        }
    }

    /**
     * Enqueues CSS and JS files and localizes script data.
     */
    public function enqueue_assets() {
        // Styles for profile menu
        wp_enqueue_style(
            'ochandydude-profile-style',
            OCHD_MASTER_PLUGIN_URL . 'assets/css/profile-menu.css',
            [],
            '4.3' // Mobile UI fixes and visual consistency enhancements
        );

        // Scripts for profile menu
        wp_enqueue_script(
            'ochandydude-profile-menu-script',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/profile-menu.js',
            [],
            '1.4', // Critical fix for menu link navigation
            true
        );

        // Navigation handler for menu links
        wp_enqueue_script(
            'ochandydude-profile-navigation-script',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/profile-menu-navigation.js',
            ['ochandydude-profile-menu-script'],
            '1.0', // New navigation handler
            true
        );

        // Localization: Pass data from PHP to the profile-menu.js script
        $profile_data = [
            'isLoggedIn' => is_user_logged_in(),
            'loginUrl'   => esc_url(do_shortcode('[openid_connect_generic_auth_url]')),
            'profileUrl' => is_user_logged_in() ? esc_url(get_permalink(get_option('ochd_profile_page_id'))) : ''
        ];
        wp_localize_script( 'ochandydude-profile-menu-script', 'ochdProfileData', $profile_data );
    }
}