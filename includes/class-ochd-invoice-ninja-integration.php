<?php

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_Invoice_Ninja_Integration {

    private static $instance = null;
    private $api_base_url;
    private $api_token;
    private $cache_duration = 60; // 1 minute for faster updates

    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->api_base_url = get_option( 'ochd_invoice_ninja_api_url', '' );
        $this->api_token = get_option( 'ochd_invoice_ninja_api_token', '' );

        // Initialize hooks
        add_action( 'wp_ajax_ochd_get_invoice_ninja_data', [ $this, 'ajax_get_invoice_ninja_data' ] );
        add_action( 'wp_ajax_nopriv_ochd_get_invoice_ninja_data', [ $this, 'ajax_get_invoice_ninja_data' ] );
        add_action( 'wp_ajax_ochd_clear_invoice_ninja_cache', [ $this, 'ajax_clear_cache' ] );

        // Clear cache when settings are updated
        add_action( 'update_option_ochd_invoice_ninja_api_url', [ $this, 'clear_all_cache' ] );
        add_action( 'update_option_ochd_invoice_ninja_api_token', [ $this, 'clear_all_cache' ] );

        // Initialize status mappings
        $this->init_status_mappings();
    }

    /**
     * Initialize status mappings from Invoice Ninja API
     */
    private function init_status_mappings() {
        // Try to get cached status mappings
        $cached_statuses = get_transient( 'ochd_invoice_ninja_status_mappings' );
        if ( $cached_statuses !== false ) {
            return;
        }

        // Fetch status mappings from API in background (best-effort)
        add_action( 'wp_loaded', [ $this, 'fetch_status_mappings' ] );
    }

    /**
     * Make API request to Invoice Ninja
     */
    private function make_api_request( $endpoint, $params = [] ) {
        if ( empty( $this->api_token ) ) {
            return new WP_Error( 'no_api_token', 'Invoice Ninja API token not configured' );
        }

        $url = trailingslashit( $this->api_base_url ) . 'api/v1/' . ltrim( $endpoint, '/' );

        if ( ! empty( $params ) ) {
            $url = add_query_arg( $params, $url );
        }

        $args = [
            'headers' => [
                'X-API-TOKEN' => $this->api_token,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'timeout' => 30,
        ];

        $response = wp_remote_get( $url, $args );

        if ( is_wp_error( $response ) ) {
            error_log( 'Invoice Ninja API Error: ' . $response->get_error_message() );
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        $body = wp_remote_retrieve_body( $response );

        if ( $response_code !== 200 ) {
            error_log( "Invoice Ninja API Error: HTTP {$response_code} - {$body}" );
            return new WP_Error( 'api_error', "API request failed with status {$response_code}" );
        }

        $data = json_decode( $body, true );

        if ( json_last_error() !== JSON_ERROR_NONE ) {
            error_log( 'Invoice Ninja API Error: Invalid JSON response' );
            return new WP_Error( 'invalid_json', 'Invalid JSON response from API' );
        }

        return $data;
    }

    /**
     * Get client ID for current user
     */
    private function get_client_id_for_user( $user_id ) {
        $cache_key = "ochd_invoice_ninja_client_id_{$user_id}";
        $client_id = get_transient( $cache_key );

        if ( $client_id !== false ) {
            return $client_id;
        }

        $user = get_user_by( 'ID', $user_id );
        if ( ! $user ) {
            error_log( "Invoice Ninja: User {$user_id} not found in WordPress" );
            return false;
        }

        error_log( "Invoice Ninja: Looking for client with id_number: {$user->user_login} (user_id: {$user_id})" );

        // Search for client by id_number (WordPress username = Keycloak UUID)
        // As per API docs: /api/v1/clients?id_number={wordpress_username}
        $response = $this->make_api_request( 'clients', [
            'id_number' => $user->user_login,
            'per_page' => 1
        ] );

        if ( is_wp_error( $response ) ) {
            error_log( "Invoice Ninja API Error for user {$user_id}: " . $response->get_error_message() );
            return false;
        }

        error_log( "Invoice Ninja: Client search response: " . print_r( $response, true ) );

        $client_id = false;
        if ( ! empty( $response['data'] ) && is_array( $response['data'] ) ) {
            $client = $response['data'][0];
            $client_id = $client['id'] ?? false;
            error_log( "Invoice Ninja: Found client ID {$client_id} for user {$user->user_login}" );
            error_log( "Invoice Ninja: Client data: " . print_r( $client, true ) );
        } else {
            error_log( "Invoice Ninja: No client found for user {$user->user_login} (id_number search)" );

            // Try alternative search by email as fallback
            error_log( "Invoice Ninja: Trying fallback search by email: {$user->user_email}" );
            $email_response = $this->make_api_request( 'clients', [
                'email' => $user->user_email,
                'per_page' => 1
            ] );

            if ( ! is_wp_error( $email_response ) && ! empty( $email_response['data'] ) ) {
                $client = $email_response['data'][0];
                $client_id = $client['id'] ?? false;
                error_log( "Invoice Ninja: Found client ID {$client_id} via email fallback for user {$user->user_email}" );
            }
        }

        // Cache briefly to reflect updates quickly
        set_transient( $cache_key, $client_id, 300 ); // 5 minutes

        return $client_id;
    }

    /**
     * Get projects for user
     */
    public function get_user_projects( $user_id, $status = 'active' ) {
        $client_id = $this->get_client_id_for_user( $user_id );
        if ( ! $client_id ) {
            return [];
        }

        $cache_key = "ochd_invoice_ninja_projects_{$user_id}_{$status}";
        $cached_data = get_transient( $cache_key );

        if ( $cached_data !== false ) {
            return $cached_data;
        }

        $params = [
            'client_id' => $client_id,
            'per_page' => 100,
            'include' => 'tasks' // Include tasks in project response
        ];

        // Note: Removing status filtering from API request as the field name is unclear
        // We'll filter the results in PHP after getting the response
        error_log( "Invoice Ninja: Fetching projects with params: " . print_r( $params, true ) );

        $response = $this->make_api_request( 'projects', $params );

        if ( is_wp_error( $response ) ) {
            error_log( 'Failed to fetch projects: ' . $response->get_error_message() );
            return [];
        }

        $projects = $response['data'] ?? [];
        error_log( "Invoice Ninja: Retrieved " . count( $projects ) . " projects for status: {$status}" );

        // Debug: Log the structure of the first project to understand the API response
        if ( ! empty( $projects ) ) {
            error_log( "Invoice Ninja: Sample project structure: " . print_r( $projects[0], true ) );
        }

        // Get tasks for each project if not already included
        foreach ( $projects as &$project ) {
            if ( empty( $project['tasks'] ) ) {
                $project['tasks'] = $this->get_project_tasks( $project['id'] );
            }

            // Ensure we have proper date fields
            $project['created_at'] = $project['created_at'] ?? null;
            $project['updated_at'] = $project['updated_at'] ?? null;
            $project['due_date'] = $project['due_date'] ?? null;

            // Add human-readable status name for frontend (projects may use project_status_id)
            if ( ! empty( $project['project_status_id'] ) ) {
                $project['status_name'] = $this->get_status_name_from_id( $project['project_status_id'], 'project' );
            } elseif ( ! empty( $project['status_id'] ) ) { // fallback if API uses status_id
                $project['status_name'] = $this->get_status_name_from_id( $project['status_id'], 'project' );
            }

            // Process tasks within the project
            if ( ! empty( $project['tasks'] ) ) {
                foreach ( $project['tasks'] as &$task ) {
                    if ( ! empty( $task['task_status_id'] ) ) {
                        $task['status_name'] = $this->get_status_name_from_id( $task['task_status_id'], 'task' );
                    } elseif ( ! empty( $task['status_id'] ) ) { // fallback
                        $task['status_name'] = $this->get_status_name_from_id( $task['status_id'], 'task' );
                    }
                }
            }
        }

        // Filter projects by status in PHP since API filtering isn't working correctly
        $filtered_projects = $this->filter_projects_by_status( $projects, $status );

        error_log( "Invoice Ninja: Filtered " . count( $filtered_projects ) . " projects from " . count( $projects ) . " total for status: {$status}" );

        set_transient( $cache_key, $filtered_projects, $this->cache_duration );

        return $filtered_projects;
    }

    /**
     * Get tasks for a project
     */
    private function get_project_tasks( $project_id ) {
        $response = $this->make_api_request( 'tasks', [
            'project_id' => $project_id,
            'per_page' => 100
        ] );

        if ( is_wp_error( $response ) ) {
            error_log( "Invoice Ninja: Failed to fetch tasks for project {$project_id}: " . $response->get_error_message() );
            return [];
        }

        $tasks = $response['data'] ?? [];
        error_log( "Invoice Ninja: Retrieved " . count( $tasks ) . " tasks for project {$project_id}" );

        // Resolve human-readable status for each task via task_status_id
        foreach ( $tasks as &$task ) {
            if ( ! empty( $task['task_status_id'] ) ) {
                $task['status_name'] = $this->get_status_name_from_id( $task['task_status_id'], 'task' );
            }
        }

        return $tasks;
    }

    /**
     * Get ALL tasks (including those attached to projects) for user
     */
    public function get_user_all_tasks( $user_id, $status = 'active' ) {
        $client_id = $this->get_client_id_for_user( $user_id );
        if ( ! $client_id ) {
            return [];
        }

        $cache_key = "ochd_invoice_ninja_all_tasks_{$user_id}_{$status}";
        $cached = get_transient( $cache_key );
        if ( $cached !== false ) {
            return $cached;
        }

        $response = $this->make_api_request( 'tasks', [
            'client_id' => $client_id,
            'per_page'  => 100
        ] );

        if ( is_wp_error( $response ) ) {
            error_log( 'Failed to fetch tasks: ' . $response->get_error_message() );
            return [];
        }

        $tasks = $response['data'] ?? [];
        error_log( "Invoice Ninja: Retrieved " . count( $tasks ) . " tasks for status: {$status}" );

        foreach ( $tasks as &$task ) {
            $task['created_at'] = $task['created_at'] ?? null;
            $task['updated_at'] = $task['updated_at'] ?? null;
            $task['time_log']   = $task['time_log']   ?? null;

            // Add start date fields for proper time display
            $task['start_date'] = $task['date'] ?? $task['calculated_start_date'] ?? null;
            $task['task_date'] = $task['date'] ?? null;
            $task['calculated_start_date'] = $task['calculated_start_date'] ?? null;

            if ( ! empty( $task['task_status_id'] ) ) {
                $task['status_name'] = $this->get_status_name_from_id( $task['task_status_id'], 'task' );
            } elseif ( ! empty( $task['status_id'] ) ) {
                $task['status_name'] = $this->get_status_name_from_id( $task['status_id'], 'task' );
            }
        }

        // Filter tasks by status only (do not filter by project association)
        $filtered_tasks = $this->filter_tasks_by_status_and_project( $tasks, $status, 'all' );
        error_log( "Invoice Ninja: Filtered " . count( $filtered_tasks ) . " tasks from " . count( $tasks ) . " total for status: {$status}" );

        // Sort tasks by start date (most recent first), then by status, then by created date
        $sorted_tasks = $this->sort_tasks( $filtered_tasks );

        set_transient( $cache_key, $sorted_tasks, $this->cache_duration );
        return $sorted_tasks;
    }

    /**
     * Get ALL tasks without status filtering (for booking history)
     */
    public function get_user_all_tasks_unfiltered( $user_id ) {
        $client_id = $this->get_client_id_for_user( $user_id );
        if ( ! $client_id ) {
            return [];
        }

        $cache_key = "ochd_invoice_ninja_all_tasks_unfiltered_{$user_id}";
        $cached = get_transient( $cache_key );
        if ( $cached !== false ) {
            return $cached;
        }

        $request_params = [
            'client_id' => $client_id,
            'per_page'  => 100,
            'include_deleted' => 'true'  // Include deleted tasks to see all data
        ];

        error_log( "Invoice Ninja: Making tasks API request with params: " . print_r( $request_params, true ) );

        $response = $this->make_api_request( 'tasks', $request_params );

        if ( is_wp_error( $response ) ) {
            error_log( 'Failed to fetch unfiltered tasks: ' . $response->get_error_message() );
            return [];
        }

        $tasks = $response['data'] ?? [];
        error_log( "Invoice Ninja: Retrieved " . count( $tasks ) . " unfiltered tasks for client {$client_id}" );

        // Log pagination info if available
        if ( isset( $response['meta']['pagination'] ) ) {
            $pagination = $response['meta']['pagination'];
            error_log( "Invoice Ninja: Pagination - total: {$pagination['total']}, current_page: {$pagination['current_page']}, total_pages: {$pagination['total_pages']}" );
        }

        // Log full API response for debugging (first 2 tasks only to avoid log spam)
        if ( ! empty( $tasks ) ) {
            error_log( "Invoice Ninja: Full task API response sample: " . print_r( array_slice( $tasks, 0, 2 ), true ) );
        }

        foreach ( $tasks as &$task ) {
            $task['created_at'] = $task['created_at'] ?? null;
            $task['updated_at'] = $task['updated_at'] ?? null;
            $task['time_log']   = $task['time_log']   ?? null;

            // Add start date fields for proper time display
            $task['start_date'] = $task['date'] ?? $task['calculated_start_date'] ?? null;
            $task['task_date'] = $task['date'] ?? null;
            $task['calculated_start_date'] = $task['calculated_start_date'] ?? null;

            // Check for invoice status first (overrides task status)
            if ( ! empty( $task['invoice_id'] ) ) {
                $invoice_status = $this->get_invoice_status( $task['invoice_id'] );
                if ( $invoice_status ) {
                    $task['status_name'] = $invoice_status;
                    $task['is_invoiced'] = true;
                    error_log( "Invoice Ninja: Task {$task['id']} (#{$task['number']}) has invoice {$task['invoice_id']} with status '{$invoice_status}'" );
                }
            }

            // If no invoice status, use task status
            if ( empty( $task['status_name'] ) ) {
                if ( ! empty( $task['task_status_id'] ) ) {
                    $task['status_name'] = $this->get_status_name_from_id( $task['task_status_id'], 'task' );
                } elseif ( ! empty( $task['status_id'] ) ) {
                    $task['status_name'] = $this->get_status_name_from_id( $task['status_id'], 'task' );
                }
            }

            $is_deleted_info = isset( $task['is_deleted'] ) ? ( $task['is_deleted'] ? 'true' : 'false' ) : 'not_set';
            $archived_info = isset( $task['archived_at'] ) ? $task['archived_at'] : 'not_set';
            $invoice_info = ! empty( $task['invoice_id'] ) ? $task['invoice_id'] : 'none';
            error_log( "Invoice Ninja: Unfiltered task {$task['id']} (#{$task['number']}): status_name='{$task['status_name']}', invoice_id={$invoice_info}, is_deleted={$is_deleted_info}, archived_at={$archived_info}" );
        }

        // Sort tasks but don't filter by status
        $sorted_tasks = $this->sort_tasks( $tasks );

        set_transient( $cache_key, $sorted_tasks, $this->cache_duration );
        return $sorted_tasks;
    }

    /**
     * Sort tasks by priority: start date, status, created date
     */
    private function sort_tasks( $tasks ) {
        if ( empty( $tasks ) ) {
            return $tasks;
        }

        usort( $tasks, function( $a, $b ) {
            // Priority 1: Start date (most recent first)
            $start_a = $a['start_date'] ?? $a['date'] ?? $a['calculated_start_date'] ?? 0;
            $start_b = $b['start_date'] ?? $b['date'] ?? $b['calculated_start_date'] ?? 0;

            if ( $start_a && $start_b ) {
                $start_compare = strtotime( $start_b ) - strtotime( $start_a );
                if ( $start_compare !== 0 ) {
                    return $start_compare;
                }
            } elseif ( $start_a && ! $start_b ) {
                return -1; // Tasks with start dates come first
            } elseif ( ! $start_a && $start_b ) {
                return 1;
            }

            // Priority 2: Status priority (active statuses first)
            $status_priority_a = $this->get_status_priority( $a['status_name'] ?? '' );
            $status_priority_b = $this->get_status_priority( $b['status_name'] ?? '' );
            $status_compare = $status_priority_a - $status_priority_b;
            if ( $status_compare !== 0 ) {
                return $status_compare;
            }

            // Priority 3: Created date (most recent first)
            $created_a = $a['created_at'] ?? 0;
            $created_b = $b['created_at'] ?? 0;
            return $created_b - $created_a;
        } );

        return $tasks;
    }

    /**
     * Get status priority for sorting (lower number = higher priority)
     */
    private function get_status_priority( $status_name ) {
        $status_lower = strtolower( trim( $status_name ) );

        $priorities = [
            'in progress' => 1,
            'running' => 1,
            'ready to go' => 2,
            'ready to do' => 2,
            'active' => 3,
            'pending' => 4,
            'backlog' => 5,
            'invoiced' => 6,
            'done' => 7,
            'paid' => 8,
            'completed' => 9,
            'cancelled' => 10,
            'deleted' => 11
        ];

        return $priorities[ $status_lower ] ?? 99;
    }

    /**
     * Get standalone tasks (no parent project) for user
     */
    public function get_user_standalone_tasks( $user_id, $status = 'active' ) {
        $client_id = $this->get_client_id_for_user( $user_id );
        if ( ! $client_id ) {
            return [];
        }

        $cache_key = "ochd_invoice_ninja_standalone_tasks_{$user_id}_{$status}";
        $cached_data = get_transient( $cache_key );

        if ( $cached_data !== false ) {
            return $cached_data;
        }

        $params = [
            'client_id' => $client_id,
            'per_page' => 100
            // Note: Removing project_id filter for now to get all tasks, we'll filter in PHP
        ];

        // Note: Removing status filtering from API request as the field name is unclear
        // We'll filter the results in PHP after getting the response
        error_log( "Invoice Ninja: Fetching standalone tasks with params: " . print_r( $params, true ) );

        $response = $this->make_api_request( 'tasks', $params );

        if ( is_wp_error( $response ) ) {
            error_log( 'Failed to fetch standalone tasks: ' . $response->get_error_message() );
            return [];
        }

        $tasks = $response['data'] ?? [];
        error_log( "Invoice Ninja: Retrieved " . count( $tasks ) . " standalone tasks for status: {$status}" );

        // Debug: Log the structure of the first task to understand the API response
        if ( ! empty( $tasks ) ) {
            error_log( "Invoice Ninja: Sample task structure: " . print_r( $tasks[0], true ) );
        }

        // Ensure we have proper date fields for tasks
        foreach ( $tasks as &$task ) {
            $task['created_at'] = $task['created_at'] ?? null;
            $task['updated_at'] = $task['updated_at'] ?? null;
            $task['time_log'] = $task['time_log'] ?? null;

            // Add start date fields for proper time display
            $task['start_date'] = $task['date'] ?? $task['calculated_start_date'] ?? null;
            $task['task_date'] = $task['date'] ?? null;
            $task['calculated_start_date'] = $task['calculated_start_date'] ?? null;

            // Add human-readable status name for frontend via task_status_id
            if ( ! empty( $task['task_status_id'] ) ) {
                $task['status_name'] = $this->get_status_name_from_id( $task['task_status_id'], 'task' );
            } elseif ( ! empty( $task['status_id'] ) ) { // fallback
                $task['status_name'] = $this->get_status_name_from_id( $task['status_id'], 'task' );
            }
        }

        // Filter tasks by status and project association in PHP
        $filtered_tasks = $this->filter_tasks_by_status_and_project( $tasks, $status, 'standalone' );

        error_log( "Invoice Ninja: Filtered " . count( $filtered_tasks ) . " standalone tasks from " . count( $tasks ) . " total for status: {$status}" );

        // Sort tasks by start date and status priority
        $sorted_tasks = $this->sort_tasks( $filtered_tasks );

        set_transient( $cache_key, $sorted_tasks, $this->cache_duration );

        return $sorted_tasks;
    }

    /**
     * Get invoices for user
     */
    public function get_user_invoices( $user_id ) {
        $client_id = $this->get_client_id_for_user( $user_id );
        if ( ! $client_id ) {
            return [];
        }

        $cache_key = "ochd_invoice_ninja_invoices_{$user_id}";
        $cached_data = get_transient( $cache_key );

        if ( $cached_data !== false ) {
            return $cached_data;
        }

        $response = $this->make_api_request( 'invoices', [
            'client_id' => $client_id,
            'per_page' => 100,
            'sort' => 'status_id|asc,created_at|desc' // Unpaid first, then by date
        ] );

        if ( is_wp_error( $response ) ) {
            error_log( 'Failed to fetch invoices: ' . $response->get_error_message() );
            return [];
        }

        $invoices = $response['data'] ?? [];
        set_transient( $cache_key, $invoices, $this->cache_duration );

        return $invoices;
    }

    /**
     * AJAX handler for getting Invoice Ninja data
     */
    public function ajax_get_invoice_ninja_data() {
        // Verify nonce
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_invoice_ninja_nonce' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid nonce' ] );
        }

        // Check if user is logged in
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( [ 'message' => 'User not logged in' ] );
        }

        $data_type = sanitize_text_field( $_POST['data_type'] ?? '' );
        $user_id = get_current_user_id();

        switch ( $data_type ) {
            case 'upcoming_bookings':
                error_log( "Invoice Ninja: UPCOMING BOOKINGS REQUEST RECEIVED for user {$user_id}" );

                // Clear cache to ensure fresh data with invoice status
                $this->clear_user_cache( $user_id );

                // Get ALL tasks with invoice status detection, then filter for active ones
                $all_tasks = $this->get_user_all_tasks_unfiltered( $user_id );
                error_log( "Invoice Ninja: Upcoming bookings - retrieved " . count( $all_tasks ) . " total tasks" );

                // Filter for active/upcoming tasks (exclude completed and cancelled)
                $active_tasks = [];
                foreach ( $all_tasks as $task ) {
                    $is_deleted = ! empty( $task['is_deleted'] ) || ( ! empty( $task['archived_at'] ) && $task['archived_at'] != 0 );
                    if ( $is_deleted ) {
                        error_log( "Invoice Ninja: Upcoming bookings - excluding deleted task {$task['id']} (#{$task['number']})" );
                        continue;
                    }

                    $status_name = $task['status_name'] ?? 'unknown';
                    $status_category = $this->categorize_status( $status_name );

                    if ( $status_category === 'upcoming' ) {
                        $active_tasks[] = $task;
                        error_log( "Invoice Ninja: Upcoming bookings - including task {$task['id']} (#{$task['number']}) with status '{$status_name}' (category: {$status_category})" );
                    } else {
                        error_log( "Invoice Ninja: Upcoming bookings - excluding {$status_category} task {$task['id']} (#{$task['number']}) with status '{$status_name}'" );

                        // Special logging for invoice-based tasks
                        if ( ! empty( $task['invoice_id'] ) ) {
                            error_log( "Invoice Ninja: Upcoming bookings - INVOICE-BASED TASK {$task['id']} with invoice {$task['invoice_id']} excluded from upcoming (status: {$status_name}, category: {$status_category})" );
                        }
                    }
                }

                error_log( "Invoice Ninja: Upcoming bookings - filtered to " . count( $active_tasks ) . " active tasks" );

                $categorized_data = $this->categorize_upcoming( [], $active_tasks ); // pass empty projects
                wp_send_json_success( $categorized_data );
                break;

            case 'booking_history':
                error_log( "Invoice Ninja: BOOKING HISTORY REQUEST RECEIVED for user {$user_id}" );

                // Clear cache to ensure fresh data with invoice status
                $this->clear_user_cache( $user_id );

                // Get ALL tasks and let categorize_booking_history filter them properly
                $all_tasks = $this->get_user_all_tasks_unfiltered( $user_id );
                error_log( "Invoice Ninja: Booking history - retrieved " . count( $all_tasks ) . " total tasks" );

                $categorized_data = $this->categorize_booking_history( [], $all_tasks ); // pass empty projects
                error_log( "Invoice Ninja: Booking history categorized - completed: " . count( $categorized_data['completed']['tasks'] ) . ", cancelled: " . count( $categorized_data['cancelled']['tasks'] ) . ", all: " . count( $categorized_data['all']['tasks'] ) );

                // Debug: Log which tasks ended up in completed category
                if ( ! empty( $categorized_data['completed']['tasks'] ) ) {
                    $completed_task_info = [];
                    foreach ( $categorized_data['completed']['tasks'] as $task ) {
                        $completed_task_info[] = "#{$task['number']} ({$task['id']}) status='{$task['status_name']}'";
                    }
                    error_log( "Invoice Ninja: Completed tasks: " . implode( ', ', $completed_task_info ) );
                }

                wp_send_json_success( $categorized_data );
                break;

            case 'invoices':
                $invoices = $this->get_user_invoices( $user_id );
                wp_send_json_success( [ 'invoices' => $invoices ] );
                break;

            case 'clear_cache':
                $this->clear_user_cache( $user_id );
                wp_send_json_success( [ 'message' => 'Cache cleared' ] );
                break;

            case 'debug_booking_history':
                // Force clear cache and debug booking history
                $this->clear_user_cache( $user_id );
                delete_transient( 'ochd_invoice_ninja_status_mappings' );

                // Force fetch status mappings
                $this->fetch_status_mappings();

                // Get all tasks
                $all_tasks = $this->get_user_all_tasks_unfiltered( $user_id );
                error_log( "Invoice Ninja: Debug - retrieved " . count( $all_tasks ) . " total tasks" );

                // Show detailed task info
                foreach ( $all_tasks as $task ) {
                    $is_deleted_info = isset( $task['is_deleted'] ) ? ( $task['is_deleted'] ? 'true' : 'false' ) : 'not_set';
                    $status_info = $task['status_name'] ?? 'no_status';
                    error_log( "Invoice Ninja: Debug task {$task['id']}: status='{$status_info}', is_deleted={$is_deleted_info}" );
                }

                // Categorize
                $categorized_data = $this->categorize_booking_history( [], $all_tasks );

                wp_send_json_success( [
                    'total_tasks' => count( $all_tasks ),
                    'completed_tasks' => count( $categorized_data['completed']['tasks'] ),
                    'cancelled_tasks' => count( $categorized_data['cancelled']['tasks'] ),
                    'all_tasks' => count( $categorized_data['all']['tasks'] ),
                    'sample_tasks' => array_slice( $all_tasks, 0, 3 ) // First 3 tasks for debugging
                ] );
                break;

            case 'debug_all_tasks':
                // Debug endpoint to see ALL tasks for a user with full details
                $this->clear_user_cache( $user_id );
                delete_transient( 'ochd_invoice_ninja_status_mappings' );

                // Force fetch status mappings
                $this->fetch_status_mappings();

                // Get all tasks with full details
                $all_tasks = $this->get_user_all_tasks_unfiltered( $user_id );

                $debug_info = [];
                foreach ( $all_tasks as $task ) {
                    $debug_info[] = [
                        'id' => $task['id'],
                        'number' => $task['number'] ?? 'no_number',
                        'description' => $task['description'] ?? 'no_description',
                        'status_id' => $task['status_id'] ?? $task['task_status_id'] ?? 'no_status_id',
                        'status_name' => $task['status_name'] ?? 'no_status_name',
                        'is_deleted' => $task['is_deleted'] ?? 'not_set',
                        'archived_at' => $task['archived_at'] ?? 'not_set',
                        'created_at' => $task['created_at'] ?? 'not_set',
                        'time_log' => $task['time_log'] ?? 'not_set'
                    ];
                }

                wp_send_json_success( [
                    'user_id' => $user_id,
                    'total_tasks' => count( $all_tasks ),
                    'tasks' => $debug_info
                ] );
                break;

            case 'test_invoice_status':
                // Test invoice status detection for a specific invoice
                $invoice_id = $_POST['invoice_id'] ?? 'Opnel5aKBz'; // Default to the known invoice

                // Clear cache first
                delete_transient( "ochd_in_invoice_status_{$invoice_id}" );

                $invoice_status = $this->get_invoice_status( $invoice_id );

                wp_send_json_success( [
                    'invoice_id' => $invoice_id,
                    'detected_status' => $invoice_status,
                    'message' => $invoice_status ? "Successfully detected status: {$invoice_status}" : "Failed to detect invoice status"
                ] );
                break;

            default:
                wp_send_json_error( [ 'message' => 'Invalid data type' ] );
        }
    }

    /**
     * Clear cache for user
     */
    public function clear_user_cache( $user_id ) {
        $cache_keys = [
            "ochd_invoice_ninja_client_id_{$user_id}",
            "ochd_invoice_ninja_projects_{$user_id}_active",
            "ochd_invoice_ninja_projects_{$user_id}_completed",
            "ochd_invoice_ninja_standalone_tasks_{$user_id}_active",
            "ochd_invoice_ninja_standalone_tasks_{$user_id}_completed",
            "ochd_invoice_ninja_all_tasks_{$user_id}_active",
            "ochd_invoice_ninja_all_tasks_{$user_id}_completed",
            "ochd_invoice_ninja_all_tasks_unfiltered_{$user_id}",
            "ochd_invoice_ninja_invoices_{$user_id}"
        ];

        foreach ( $cache_keys as $key ) {
            delete_transient( $key );
        }

        // Also clear status mappings cache
        delete_transient( 'ochd_invoice_ninja_status_mappings' );

        // Clear invoice status cache (wildcard delete)
        global $wpdb;
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_ochd_in_invoice_status_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_ochd_in_invoice_status_%'" );

        error_log( "Invoice Ninja: Cleared cache for user {$user_id}" );
    }

    /**
     * Add method to manually refresh user data (for debugging)
     */
    public function refresh_user_data( $user_id ) {
        $this->clear_user_cache( $user_id );

        // Force refresh client ID
        $client_id = $this->get_client_id_for_user( $user_id );

        if ( $client_id ) {
            // Pre-load data (tasks only)
            $this->get_user_all_tasks( $user_id, 'active' );
            $this->get_user_all_tasks( $user_id, 'completed' );
            $this->get_user_all_tasks_unfiltered( $user_id );
            $this->get_user_invoices( $user_id );

            error_log( "Invoice Ninja: Refreshed task data for user {$user_id}, client {$client_id}" );
        } else {
            error_log( "Invoice Ninja: Could not refresh data - no client found for user {$user_id}" );
        }

        return $client_id;
    }

    /**
     * Format currency amount
     */
    public function format_currency( $amount, $currency_code = 'USD' ) {
        // Invoice Ninja v5 stores amounts as actual dollar values, not cents
        $formatted = number_format( (float) $amount, 2 );
        return $currency_code . ' ' . $formatted;
    }

    /**
     * Fetch status mappings from Invoice Ninja API
     */
    public function fetch_status_mappings() {
        // Fetch task statuses
        $task_statuses_response = $this->make_api_request( 'task_statuses' );
        $project_statuses_response = $this->make_api_request( 'project_statuses' );

        $status_mappings = [
            'task_statuses' => [],
            'project_statuses' => []
        ];

        if ( ! is_wp_error( $task_statuses_response ) && ! empty( $task_statuses_response['data'] ) ) {
            foreach ( $task_statuses_response['data'] as $status ) {
                $status_mappings['task_statuses'][ $status['id'] ] = $status['name'];
            }
            error_log( 'Invoice Ninja: Task statuses: ' . print_r( $status_mappings['task_statuses'], true ) );
        } else {
            error_log( 'Invoice Ninja: Failed to fetch task statuses: ' . print_r( $task_statuses_response, true ) );
        }

        if ( ! is_wp_error( $project_statuses_response ) && ! empty( $project_statuses_response['data'] ) ) {
            foreach ( $project_statuses_response['data'] as $status ) {
                $status_mappings['project_statuses'][ $status['id'] ] = $status['name'];
            }
            error_log( 'Invoice Ninja: Project statuses: ' . print_r( $status_mappings['project_statuses'], true ) );
        } else {
            error_log( 'Invoice Ninja: Failed to fetch project statuses: ' . print_r( $project_statuses_response, true ) );
        }

        // Cache status mappings briefly to allow fast changes
        set_transient( 'ochd_invoice_ninja_status_mappings', $status_mappings, 600 ); // 10 minutes

        return $status_mappings;
    }

    /**
     * Ensure status mappings are loaded
     */
    private function ensure_status_mappings() {
        $status_mappings = get_transient( 'ochd_invoice_ninja_status_mappings' );
        if ( $status_mappings === false ) {
            error_log( "Invoice Ninja: Status mappings not cached, fetching now..." );
            $this->fetch_status_mappings();
        }
    }

    /**
     * Get status name from status ID
     */
    private function get_status_name_from_id( $status_id, $type = 'task' ) {
        // Ensure status mappings are loaded
        $this->ensure_status_mappings();

        // Try cache first (transient of resolved names)
        $cache_key = "ochd_in_status_name_{$type}_{$status_id}";
        $cached = get_transient( $cache_key );
        if ( $cached !== false ) {
            return $cached;
        }

        // Try global mappings cache
        $status_mappings = get_transient( 'ochd_invoice_ninja_status_mappings' );
        $status_key = $type === 'project' ? 'project_statuses' : 'task_statuses';
        if ( $status_mappings && isset( $status_mappings[ $status_key ][ $status_id ] ) ) {
            set_transient( $cache_key, $status_mappings[ $status_key ][ $status_id ], 600 );
            return $status_mappings[ $status_key ][ $status_id ];
        }

        // Fetch single status from API as per documentation
        $endpoint = $type === 'project' ? "project_statuses/{$status_id}" : "task_statuses/{$status_id}";
        $resp = $this->make_api_request( $endpoint );
        if ( ! is_wp_error( $resp ) && ! empty( $resp['name'] ) ) {
            set_transient( $cache_key, $resp['name'], 600 );
            error_log( "Invoice Ninja: Resolved {$type} status {$status_id} -> '{$resp['name']}'" );
            return $resp['name'];
        } else {
            error_log( "Invoice Ninja: Failed to resolve {$type} status {$status_id}, API response: " . print_r( $resp, true ) );
        }

        // Fallback: return status_id as-is
        error_log( "Invoice Ninja: Using fallback for {$type} status {$status_id}" );
        return $status_id;
    }

    /**
     * Get invoice status from invoice ID
     */
    private function get_invoice_status( $invoice_id ) {
        if ( empty( $invoice_id ) ) {
            return null;
        }

        // Try cache first
        $cache_key = "ochd_in_invoice_status_{$invoice_id}";
        $cached = get_transient( $cache_key );
        if ( $cached !== false ) {
            return $cached;
        }

        // Fetch invoice from API
        $response = $this->make_api_request( "invoices/{$invoice_id}" );
        if ( is_wp_error( $response ) ) {
            error_log( "Invoice Ninja: Failed to fetch invoice {$invoice_id}: " . $response->get_error_message() );
            return null;
        }

        // Check if response has data structure
        $invoice = isset( $response['data'] ) ? $response['data'] : $response;

        if ( empty( $invoice['status_id'] ) ) {
            error_log( "Invoice Ninja: Invoice {$invoice_id} has no status_id: " . print_r( $invoice, true ) );
            return null;
        }
        $status_name = null;

        // Determine status based on invoice data
        if ( ! empty( $invoice['status_id'] ) ) {
            // Map Invoice Ninja status IDs to readable names
            $status_map = [
                '1' => 'Draft',
                '2' => 'Sent',
                '3' => 'Viewed',
                '4' => 'Approved',
                '5' => 'Partial',
                '6' => 'Paid'
            ];

            $status_name = $status_map[ $invoice['status_id'] ] ?? 'Unknown';
        }

        // Check if overdue
        if ( ! empty( $invoice['due_date'] ) && $status_name !== 'Paid' ) {
            $due_date = strtotime( $invoice['due_date'] );
            if ( $due_date && $due_date < time() ) {
                $status_name = 'Overdue';
            }
        }

        // Map to our status names
        $final_status = $this->map_invoice_status_to_task_status( $status_name );

        // Cache for 5 minutes
        set_transient( $cache_key, $final_status, 300 );

        error_log( "Invoice Ninja: Invoice {$invoice_id} has status_id '{$invoice['status_id']}' -> '{$status_name}' -> '{$final_status}'" );
        error_log( "Invoice Ninja: Invoice {$invoice_id} due_date: " . ($invoice['due_date'] ?? 'none') . ", current_time: " . date('Y-m-d') );

        return $final_status;
    }

    /**
     * Map invoice status to task status
     */
    private function map_invoice_status_to_task_status( $invoice_status ) {
        $status_map = [
            'Draft' => 'Pending Payment',
            'Sent' => 'Pending Payment',
            'Viewed' => 'Pending Payment',
            'Approved' => 'Pending Payment',
            'Partial' => 'Pending Payment',
            'Paid' => 'Paid',
            'Overdue' => 'Overdue'
        ];

        return $status_map[ $invoice_status ] ?? 'Invoiced';
    }

    /**
     * Categorize status for filtering
     */
    private function categorize_status( $status_name ) {
        $status_lower = strtolower( trim( $status_name ) );

        // Upcoming/Active statuses
        if ( in_array( $status_lower, [ 'ready to go', 'ready to do', 'in progress', 'running', 'pending', 'active', 'open', 'started' ] ) ) {
            return 'upcoming';
        }

        // Cancelled statuses
        if ( in_array( $status_lower, [ 'deleted', 'canceled', 'cancelled', 'rejected', 'abandoned' ] ) ) {
            return 'cancelled';
        }

        // Completed statuses (including invoice-based statuses)
        if ( in_array( $status_lower, [ 'done', 'invoiced', 'paid', 'completed', 'finished', 'closed', 'pending payment', 'overdue' ] ) ) {
            return 'completed';
        }

        // Default to upcoming for unknown statuses
        error_log( "Invoice Ninja: Unknown status '{$status_name}' defaulting to 'upcoming'" );
        return 'upcoming';
    }

    /**
     * Filter projects by status
     */
    private function filter_projects_by_status( $projects, $requested_status ) {
        $filtered = [];

        foreach ( $projects as $project ) {
            // For projects, check if they have a status_id or use is_deleted/archived_at
            $should_include = false;

            if ( $requested_status === 'active' ) {
                // Include projects that are not deleted and not archived
                $should_include = ! $project['is_deleted'] && ( empty( $project['archived_at'] ) || $project['archived_at'] == 0 );
            } elseif ( $requested_status === 'completed' ) {
                // Include projects that are deleted or archived
                $should_include = $project['is_deleted'] || ( ! empty( $project['archived_at'] ) && $project['archived_at'] != 0 );
            }

            if ( $should_include ) {
                // Also filter tasks within the project
                if ( ! empty( $project['tasks'] ) ) {
                    $project['tasks'] = $this->filter_tasks_by_status_and_project( $project['tasks'], $requested_status, 'project' );
                }
                $filtered[] = $project;
            }
        }

        return $filtered;
    }

    /**
     * Filter tasks by status and project association
     */
    private function filter_tasks_by_status_and_project( $tasks, $requested_status, $project_filter = 'all' ) {
        $filtered = [];
        $debug_info = [];

        foreach ( $tasks as $task ) {
            // Filter by project association
            if ( $project_filter === 'standalone' && ! empty( $task['project_id'] ) ) {
                $debug_info[] = "Skipped task {$task['id']} - has project_id";
                continue; // Skip tasks that belong to projects
            }

            $should_include = false;
            $task_status_name = '';

            // FIRST: Check if task is deleted (this overrides status-based filtering)
            if ( ! empty( $task['is_deleted'] ) || ( ! empty( $task['archived_at'] ) && $task['archived_at'] != 0 ) ) {
                // Deleted tasks should only appear in completed requests
                if ( $requested_status === 'completed' ) {
                    $should_include = true;
                }
                $debug_info[] = "Task {$task['id']}: is_deleted/archived, requested='{$requested_status}', include=" . ($should_include ? 'YES' : 'NO');
            } elseif ( ! empty( $task['task_status_id'] ) || ! empty( $task['status_id'] ) ) {
                // Get the actual status name from the task_status_id (preferred) or status_id (fallback)
                $lookup_id = ! empty( $task['task_status_id'] ) ? $task['task_status_id'] : $task['status_id'];
                $task_status_name = $this->get_status_name_from_id( $lookup_id, 'task' );
                $status_category = $this->categorize_status( $task_status_name );

                if ( $requested_status === 'active' && $status_category === 'upcoming' ) {
                    $should_include = true;
                } elseif ( $requested_status === 'completed' && in_array( $status_category, [ 'completed', 'cancelled' ] ) ) {
                    $should_include = true;
                }

                $debug_info[] = "Task {$task['id']}: status='{$task_status_name}', category='{$status_category}', requested='{$requested_status}', include=" . ($should_include ? 'YES' : 'NO');
            } else {
                // Fallback: use is_deleted and archived_at
                if ( $requested_status === 'active' ) {
                    $should_include = ! $task['is_deleted'] && ( empty( $task['archived_at'] ) || $task['archived_at'] == 0 );
                } elseif ( $requested_status === 'completed' ) {
                    $should_include = $task['is_deleted'] || ( ! empty( $task['archived_at'] ) && $task['archived_at'] != 0 );
                }

                $debug_info[] = "Task {$task['id']}: no status_id, using fallback, include=" . ($should_include ? 'YES' : 'NO');
            }

            if ( $should_include ) {
                $filtered[] = $task;
            }
        }

        // Log debug information
        error_log( "Invoice Ninja: Task filtering for {$requested_status} ({$project_filter}): " . implode( '; ', $debug_info ) );

        return $filtered;
    }

    /**
     * Determine project status from various possible fields
     */
    private function determine_project_status( $project ) {
        // Try different possible status field names
        $possible_fields = [ 'status', 'status_id', 'state', 'project_status', 'is_completed', 'archived' ];

        foreach ( $possible_fields as $field ) {
            if ( isset( $project[$field] ) ) {
                error_log( "Invoice Ninja: Project {$project['id']} has {$field}: " . print_r( $project[$field], true ) );
                return $project[$field];
            }
        }

        // Fallback: assume active if no status field found
        error_log( "Invoice Ninja: No status field found for project {$project['id']}, assuming active" );
        return 'active';
    }

    /**
     * Determine task status from various possible fields
     */
    private function determine_task_status( $task ) {
        // Try different possible status field names
        $possible_fields = [ 'status', 'status_id', 'state', 'task_status', 'is_completed', 'archived' ];

        foreach ( $possible_fields as $field ) {
            if ( isset( $task[$field] ) ) {
                error_log( "Invoice Ninja: Task {$task['id']} has {$field}: " . print_r( $task[$field], true ) );
                return $task[$field];
            }
        }

        // Fallback: assume active if no status field found
        error_log( "Invoice Ninja: No status field found for task {$task['id']}, assuming active" );
        return 'active';
    }

    /**
     * Check if a status matches the requested filter
     */
    private function status_matches_filter( $status, $requested_status ) {
        // Convert status to a normalized form
        $normalized_status = $this->normalize_status( $status );

        if ( $requested_status === 'active' ) {
            return in_array( $normalized_status, [ 'active', 'in_progress', 'pending', 'draft' ] );
        } elseif ( $requested_status === 'completed' ) {
            return in_array( $normalized_status, [ 'completed', 'finished', 'done', 'cancelled', 'archived' ] );
        }

        return true; // Default: include all
    }

    /**
     * Normalize status values to standard terms
     */
    private function normalize_status( $status ) {
        if ( is_numeric( $status ) ) {
            // Handle numeric status codes
            $status = (int) $status;
            switch ( $status ) {
                case 1: return 'draft';
                case 2: return 'active';
                case 3: return 'in_progress';
                case 4: return 'completed';
                case 5: return 'cancelled';
                case 6: return 'paused';
                default: return 'active';
            }
        }

        if ( is_string( $status ) ) {
            $status = strtolower( trim( $status ) );
            // Map various string statuses to normalized terms
            $status_map = [
                'active' => 'active',
                'in progress' => 'in_progress',
                'in_progress' => 'in_progress',
                'pending' => 'active',
                'open' => 'active',
                'started' => 'in_progress',
                'completed' => 'completed',
                'complete' => 'completed',
                'finished' => 'completed',
                'done' => 'completed',
                'cancelled' => 'cancelled',
                'canceled' => 'cancelled',
                'archived' => 'archived',
                'paused' => 'paused',
                'draft' => 'draft'
            ];

            return $status_map[$status] ?? 'active';
        }

        // Handle boolean values
        if ( is_bool( $status ) ) {
            return $status ? 'completed' : 'active';
        }

        return 'active'; // Default fallback
    }

    /**
     * Categorize booking history into subcategories
     */
    private function categorize_upcoming( $projects, $tasks ) {
        $categorized = [
            'all' => [
                'projects' => $projects,
                'tasks' => $tasks
            ],
            'confirmed' => [
                'projects' => [],
                'tasks' => []
            ],
            'unconfirmed' => [
                'projects' => [],
                'tasks' => []
            ]
        ];

        // Confirmed statuses: In progress, Running, Ready to go, Ready to do
        $confirmed = ['in progress','running','ready to go','ready to do'];
        $unconfirmed = ['backlog'];

        // Projects
        foreach ($projects as $project) {
            $status = strtolower(trim($project['status_name'] ?? ''));
            if (in_array($status, $confirmed, true)) {
                $categorized['confirmed']['projects'][] = $project;
            } elseif (in_array($status, $unconfirmed, true)) {
                $categorized['unconfirmed']['projects'][] = $project;
            }
        }

        // Tasks
        foreach ($tasks as $task) {
            $status = strtolower(trim($task['status_name'] ?? ''));
            if (in_array($status, $confirmed, true)) {
                $categorized['confirmed']['tasks'][] = $task;
            } elseif (in_array($status, $unconfirmed, true)) {
                $categorized['unconfirmed']['tasks'][] = $task;
            }
        }

        return $categorized;
    }

    private function categorize_booking_history( $projects, $tasks ) {
        $categorized = [
            'all' => [
                'projects' => [],
                'tasks' => []
            ],
            'completed' => [
                'projects' => [],
                'tasks' => []
            ],
            'cancelled' => [
                'projects' => [],
                'tasks' => []
            ]
        ];

        error_log( "Invoice Ninja: categorize_booking_history called with " . count( $tasks ) . " tasks" );

        // Debug: Show first task structure to understand available fields
        if ( ! empty( $tasks ) ) {
            $first_task = $tasks[0];
            $task_fields = array_keys( $first_task );
            error_log( "Invoice Ninja: First task fields: " . implode( ', ', $task_fields ) );
            error_log( "Invoice Ninja: First task sample data: " . print_r( array_slice( $first_task, 0, 10, true ), true ) );
        }

        // Filter and categorize tasks based on their status
        foreach ( $tasks as $task ) {
            $is_history_task = false;
            $is_cancelled = false;

            // First, check if task is deleted (this overrides status-based categorization)
            if ( ! empty( $task['is_deleted'] ) || ( ! empty( $task['archived_at'] ) && $task['archived_at'] != 0 ) ) {
                $is_history_task = true;
                $is_cancelled = true;
                error_log( "Invoice Ninja: Booking history task {$task['id']}: is_deleted=" . ($task['is_deleted'] ? 'true' : 'false') . ", archived_at=" . ($task['archived_at'] ?? 'null') . " -> cancelled" );
            } else {
                // Check status-based categorization for non-deleted tasks
                // Use existing status_name if available (includes invoice status override), otherwise resolve from ID
                if ( ! empty( $task['status_name'] ) ) {
                    $status_name = $task['status_name'];
                    error_log( "Invoice Ninja: Booking history task {$task['id']}: using existing status_name='{$status_name}'" );
                } elseif ( ! empty( $task['task_status_id'] ) || ! empty( $task['status_id'] ) ) {
                    $lookup_id = ! empty( $task['task_status_id'] ) ? $task['task_status_id'] : $task['status_id'];
                    $status_name = $this->get_status_name_from_id( $lookup_id, 'task' );
                    error_log( "Invoice Ninja: Booking history task {$task['id']}: resolved status_name='{$status_name}' from ID" );
                } else {
                    error_log( "Invoice Ninja: Booking history task {$task['id']}: no status available -> excluded" );
                    continue;
                }

                $status_category = $this->categorize_status( $status_name );
                error_log( "Invoice Ninja: Booking history task {$task['id']}: status='{$status_name}', category='{$status_category}'" );

                // Include completed and cancelled tasks based on status category
                if ( $status_category === 'completed' || $status_category === 'cancelled' ) {
                    $is_history_task = true;
                    $is_cancelled = ( $status_category === 'cancelled' );

                    // Special logging for invoice-based tasks
                    $status_lower = strtolower( $status_name );
                    if ( in_array( $status_lower, [ 'invoiced', 'pending payment', 'overdue', 'paid' ] ) ) {
                        error_log( "Invoice Ninja: FOUND INVOICE-BASED TASK {$task['id']} with status '{$status_name}' - categorizing as completed" );
                    }
                } else {
                    // Also check for specific status names that should be in history
                    $status_lower = strtolower( trim( $status_name ) );
                    $history_statuses = [ 'done', 'invoiced', 'paid', 'completed', 'finished', 'closed', 'deleted', 'canceled', 'cancelled', 'rejected', 'abandoned', 'pending payment', 'overdue' ];
                    if ( in_array( $status_lower, $history_statuses ) ) {
                        $is_history_task = true;
                        $is_cancelled = in_array( $status_lower, [ 'deleted', 'canceled', 'cancelled', 'rejected', 'abandoned' ] );
                        error_log( "Invoice Ninja: Booking history task {$task['id']}: included by direct status match '{$status_name}'" );
                    }
                }
            }

            // Add to appropriate categories
            if ( $is_history_task ) {
                $categorized['all']['tasks'][] = $task;

                if ( $is_cancelled ) {
                    $categorized['cancelled']['tasks'][] = $task;
                } else {
                    $categorized['completed']['tasks'][] = $task;
                }
            }
        }

        // Projects are ignored for now (empty array passed)
        $categorized['all']['projects'] = $projects;
        $categorized['completed']['projects'] = $projects;
        $categorized['cancelled']['projects'] = $projects;

        return $categorized;
    }



    /**
     * Get status label for projects/tasks
     */
    public function get_status_label( $status_id ) {
        // Convert to integer to handle string status IDs
        $status_id = (int) $status_id;

        $statuses = [
            1 => 'Draft',
            2 => 'Active',
            3 => 'In Progress',
            4 => 'Completed',
            5 => 'Cancelled',
            6 => 'Paused',
            -1 => 'Archived',
            -2 => 'Deleted'
        ];

        $label = $statuses[ $status_id ] ?? 'Unknown';

        // Log for debugging
        if ( $label === 'Unknown' ) {
            error_log( "Invoice Ninja: Unknown status ID: {$status_id} (type: " . gettype( $status_id ) . ")" );
        }

        return $label;
    }

    /**
     * Get invoice status label (user-friendly)
     */
    public function get_invoice_status_label( $status_id ) {
        $statuses = [
            1 => 'Draft',
            2 => 'Unpaid', // More user-friendly than "Sent"
            3 => 'Unpaid', // More user-friendly than "Viewed"
            4 => 'Unpaid', // More user-friendly than "Approved"
            5 => 'Partially Paid',
            6 => 'Paid',
            -1 => 'Cancelled',
            -2 => 'Reversed'
        ];

        return $statuses[ $status_id ] ?? 'Unknown';
    }

    /**
     * Get detailed invoice status label (technical)
     */
    public function get_invoice_status_detailed( $status_id ) {
        $statuses = [
            1 => 'Draft',
            2 => 'Sent',
            3 => 'Viewed',
            4 => 'Approved',
            5 => 'Partial Payment',
            6 => 'Paid in Full',
            -1 => 'Cancelled',
            -2 => 'Reversed'
        ];

        return $statuses[ $status_id ] ?? 'Unknown';
    }

    /**
     * Check if invoice is overdue
     */
    public function is_invoice_overdue( $invoice ) {
        if ( $invoice['status_id'] == 6 ) { // Already paid
            return false;
        }

        if ( empty( $invoice['due_date'] ) ) {
            return false;
        }

        $due_date = strtotime( $invoice['due_date'] );
        $today = strtotime( 'today' );

        return $due_date < $today;
    }

    /**
     * AJAX handler for clearing cache
     */
    public function ajax_clear_cache() {
        // Verify nonce and permissions
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_invoice_ninja_nonce' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid nonce' ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [ 'message' => 'Insufficient permissions' ] );
        }

        $this->clear_all_cache();
        wp_send_json_success( [ 'message' => 'Cache cleared successfully' ] );
    }

    /**
     * Clear all cached data
     */
    public function clear_all_cache() {
        global $wpdb;

        // Delete all Invoice Ninja related transients
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_ochd_invoice_ninja_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_ochd_invoice_ninja_%'" );

        error_log( 'OCHandyDude: Invoice Ninja cache cleared' );
    }

    /**
     * Test API connection
     */
    public function test_api_connection() {
        if ( empty( $this->api_token ) ) {
            return new WP_Error( 'no_api_token', 'API token not configured' );
        }

        $response = $this->make_api_request( 'ping' );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return true;
    }
}
