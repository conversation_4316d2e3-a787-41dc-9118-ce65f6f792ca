<?php

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_Smart_Booking {

    private static $instance = null;

    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_shortcode( 'ochandydude_booking_form', [ $this, 'render_wrapper_shortcode' ] );
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_assets' ] );
        add_action( 'wp_ajax_nopriv_ochd_check_user_exists', [ $this, 'ajax_check_user_exists' ] );
        add_action( 'wp_ajax_ochd_check_user_exists', [ $this, 'ajax_check_user_exists' ] );
    }

    private function get_clean_user_meta($user_id, $key) {
        $meta_value = get_user_meta($user_id, $key, true);
        if (is_array($meta_value)) {
            return !empty($meta_value) ? (string) $meta_value[0] : '';
        }
        return (string) $meta_value;
    }

    public function enqueue_assets() {
        global $post;
        if ( is_front_page() || ( is_a( $post, 'WP_Post' ) && has_shortcode( $post->post_content, 'ochandydude_booking_form' ) ) ) {
            wp_enqueue_style( 'ochd-booking-form-style', OCHD_MASTER_PLUGIN_URL . 'assets/css/booking-form.css', [], '2.0' );
            wp_enqueue_script('ochd-booking-wrapper-script', OCHD_MASTER_PLUGIN_URL . 'assets/js/booking-form.js', [], '21.1', true);
            $data_for_js = [
                'isLoggedIn' => is_user_logged_in(),
                'ajax_url'   => admin_url( 'admin-ajax.php' ),
                'login_url'  => do_shortcode('[openid_connect_generic_auth_url]'),
                'nonce'      => wp_create_nonce('wp_ajax'), 
            ];
            if ( is_user_logged_in() ) {
                $user = wp_get_current_user();
                $data_for_js['userData'] = [
                    'first_name' => $user->first_name,
                    'last_name'  => $user->last_name,
                    'email'      => $user->user_email,
                    'phone'      => $this->get_clean_user_meta( $user->ID, 'billing_phone' ),
                    'address'    => $this->get_clean_user_meta( $user->ID, 'billing_address_1' ),
                    'city'       => $this->get_clean_user_meta( $user->ID, 'billing_city' ),
                    'zip'        => $this->get_clean_user_meta( $user->ID, 'billing_postcode' ),
                ];
            }
            wp_localize_script( 'ochd-booking-wrapper-script', 'ochdBookingData', $data_for_js );

            // Debug log the auto-fill data
            $debug_logger = OCHD_Debug_Logger::get_instance();
            if ( $debug_logger->is_enabled() ) {
                $debug_logger->info( 'Auto-fill data localized for booking form', 'BookingForm-PHP', [
                    'isLoggedIn' => $data_for_js['isLoggedIn'],
                    'hasUserData' => isset( $data_for_js['userData'] ),
                    'userData' => $data_for_js['userData'] ?? 'not available'
                ] );
            }

            // Load debug test script if debug logging is enabled
            $debug_logger = OCHD_Debug_Logger::get_instance();
            if ( $debug_logger->is_enabled() ) {
                // Use simple layout optimizer and dynamic height monitor
                wp_enqueue_script(
                    'ochd-simple-layout-optimizer',
                    OCHD_MASTER_PLUGIN_URL . 'assets/js/simple-layout-optimizer.js',
                    [ 'jquery', 'ochd-debug-ajax' ],
                    '1.0',
                    true
                );

                wp_enqueue_script(
                    'ochd-dynamic-height-monitor',
                    OCHD_MASTER_PLUGIN_URL . 'assets/js/dynamic-height-monitor.js',
                    [ 'jquery', 'ochd-debug-ajax' ],
                    '1.0',
                    true
                );

                $debug_logger->info( 'Debug test script enqueued for booking form', 'BookingForm-PHP', [
                    'script_url' => OCHD_MASTER_PLUGIN_URL . 'assets/js/debug-logging-test.js',
                    'debug_config' => $debug_logger->get_config()
                ] );
            }
        }
    }

    public function render_wrapper_shortcode( $atts = [] ) {
        // Store attributes globally so the iframe method can access them
        global $ochd_booking_shortcode_atts;
        $ochd_booking_shortcode_atts = shortcode_atts( [
            'provider' => '',
            'service' => '',
            'width' => '100%',
            'height' => 'auto'
        ], $atts );
        ob_start(); ?>
        <div class="ochd-booking-container" style="position: relative; line-height: 0;">
            <div class="ochd-booking-form-wrapper">
                <?php echo $this->render_easyappointments_iframe(); ?>
            </div>
            <?php if ( ! is_user_logged_in() ) : ?>
                <div id="ochd-guest-overlay" class="ochd-guest-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 10; display: flex; align-items: center; justify-content: center; background-color: rgba(30, 30, 30, 0.75); backdrop-filter: blur(8px); color: #eee;">
                    <div style="text-align: center; padding: 20px; max-width: 90%; width: 100%; max-width: 500px;">
                        <h3 style="margin-bottom: 15px; font-size: 24px;">Welcome!</h3>
                        <p style="margin-bottom: 25px; font-size: 16px; line-height: 1.4;">Log in for a faster booking experience or continue as a guest.</p>
                        <div style="display: flex; flex-direction: column; gap: 15px; align-items: center;">
                            <a href="<?php echo esc_url(do_shortcode('[openid_connect_generic_auth_url]')); ?>" class="ochd-button ochd-register-button">Login / Register</a>
                            <button id="ochd-continue-as-guest" class="ochd-button ochd-guest-button">Continue as Guest</button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <style>
            .ochd-booking-form-wrapper {
                /* Мягкие отступы внутри контейнера для формы */
                padding-left: 8px;
                padding-right: 8px;
                background-color: transparent;
                border-radius: 88px;
            }

            /* Исправление: убираем вертикальные отступы у формы, чтобы избежать лишних wrapper */
            .ochd-booking-form-wrapper > * {
                margin-top: 0 !important;
                margin-bottom: 0 !important;
            }

            .ochd-booking-form-wrapper iframe.easyappointments-iframe {
                display: block;
                border: none;
                width: 100%;
                border-radius: 80px;
            }
            #ochd-guest-overlay {
                 border-radius: 88px;
            }
            .ochd-button {
                display: inline-block;
                padding: 15px 30px;
                border-radius: 6px;
                text-decoration: none !important;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                border: 2px solid transparent;
                transition: all 0.3s ease;
                width: 100% !important;
                max-width: 280px !important;
                min-width: 280px;
                text-align: center;
                box-sizing: border-box;
                line-height: 1.2;
                white-space: nowrap;
            }
            .ochd-register-button {
                background-color: #007bff !important;
                color: white !important;
                border-color: #007bff !important;
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
            }
            .ochd-register-button:hover {
                background-color: #0056b3 !important;
                border-color: #0056b3 !important;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
                text-decoration: none !important;
                color: white !important;
            }
            .ochd-guest-button {
                background-color: transparent !important;
                border-color: #6c757d !important;
                color: #eee !important;
            }
            .ochd-guest-button:hover {
                background-color: rgba(255,255,255,0.1) !important;
                border-color: #adb5bd !important;
                transform: translateY(-1px);
                color: #eee !important;
            }

            /* Mobile responsive styling with viewport height fix */
            @media (max-width: 768px) {
                .ochd-booking-container {
                    max-height: 100vh;
                    overflow: hidden;
                }

                #ochd-guest-overlay {
                    height: calc(100vh - 120px) !important;
                    max-height: calc(100vh - 120px) !important;
                    overflow: hidden;
                }

                #ochd-guest-overlay h3 {
                    font-size: 20px !important;
                }
                #ochd-guest-overlay p {
                    font-size: 14px !important;
                    line-height: 1.5 !important;
                }
                .ochd-button {
                    font-size: 14px !important;
                    padding: 12px 25px !important;
                }
            }

            /* Small mobile devices: Further optimization */
            @media (max-width: 480px) {
                #ochd-guest-overlay {
                    height: calc(100vh - 100px) !important;
                    max-height: calc(100vh - 100px) !important;
                }
            }
        </style>

        <?php return ob_get_clean();
    }

    public function ajax_check_user_exists() {
        $allowed_origin = get_option( 'ochd_booking_cors_origin', '' );
        if ( ! empty( $allowed_origin ) ) {
            header("Access-control-allow-origin: " . esc_url_raw( $allowed_origin ));
        }
        header("Access-Control-Allow-Methods: POST, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type");
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') { exit(0); }
        check_ajax_referer('wp_ajax', 'security');
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
        $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        if (empty($email) && empty($phone)) { wp_send_json_error(['message' => 'Email or phone required.']); }
        $user_exists = false;
        if (!empty($email) && email_exists($email)) { $user_exists = true; }
        if (!$user_exists && !empty($phone)) {
            $users = get_users(['meta_key' => 'billing_phone', 'meta_value' => $phone, 'number' => 1, 'fields' => 'ID']);
            if (!empty($users)) { $user_exists = true; }
        }
        wp_send_json_success(['user_exists' => $user_exists]);
    }

    /**
     * Render Easy!Appointments iframe directly (replaces third-party plugin dependency)
     * Maintains backward compatibility with existing shortcode usage
     */
    private function render_easyappointments_iframe() {
        $logger = OCHD_Debug_Logger::get_instance();

        // Get the Easy!Appointments URL from the same option the original plugin used
        $booking_url = get_option( 'easyappointments_url' );

        $logger->debug( 'Starting Easy!Appointments iframe generation', 'BookingForm-PHP', [
            'booking_url' => $booking_url ? 'configured' : 'not configured',
            'user_logged_in' => is_user_logged_in(),
            'current_user' => is_user_logged_in() ? wp_get_current_user()->user_login : 'guest'
        ] );

        if ( empty( $booking_url ) ) {
            return '<div class="ochd-booking-error" style="padding: 2rem; text-align: center; background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 12px; color: #721c24;">
                <p style="margin: 0; font-weight: 500;">Booking system is not configured. Please contact support or configure Easy!Appointments in <a href="' . admin_url( 'options-general.php?page=ochandydude-settings' ) . '">OCHandyDude Settings</a>.</p>
            </div>';
        }

        // Parse any query parameters that might be in the shortcode attributes
        // This maintains compatibility with the original [easyappointments provider="X" service="Y"] format
        $query_params = [];

        // Check if we're in a shortcode context and have attributes
        global $ochd_booking_shortcode_atts;
        if ( ! empty( $ochd_booking_shortcode_atts ) ) {
            if ( ! empty( $ochd_booking_shortcode_atts['provider'] ) ) {
                $query_params['provider'] = $ochd_booking_shortcode_atts['provider'];
            }
            if ( ! empty( $ochd_booking_shortcode_atts['service'] ) ) {
                $query_params['service'] = $ochd_booking_shortcode_atts['service'];
            }
        }

        // Build the final URL with query parameters
        $final_url = $booking_url;
        if ( ! empty( $query_params ) ) {
            $final_url .= ( strpos( $booking_url, '?' ) === false ? '?' : '&' ) . http_build_query( $query_params );
        }

        // Generate unique ID for this iframe instance
        $iframe_id = 'ochd-easyappointments-iframe-' . uniqid();

        $logger->info( 'Generating Easy!Appointments iframe', 'BookingForm-PHP', [
            'iframe_id' => $iframe_id,
            'final_url' => $final_url,
            'query_params' => $query_params,
            'has_attributes' => ! empty( $ochd_booking_shortcode_atts ),
            'attributes' => $ochd_booking_shortcode_atts ?? []
        ] );

        // Return the iframe with responsive styling that matches the original plugin behavior
        ob_start();
        ?>
        <iframe
            id="<?php echo esc_attr( $iframe_id ); ?>"
            class="easyappointments-iframe ochd-integrated-booking-iframe"
            src="<?php echo esc_url( $final_url ); ?>"
            width="100%"
            height="600"
            frameborder="0"
            scrolling="no"
            style="border: none; width: 100%; display: block; min-height: 600px; margin: 0 auto; padding: 0; height: auto;"
        >
            <p>Your browser does not support iframes. Please <a href="<?php echo esc_url( $final_url ); ?>" target="_blank">click here</a> to access the booking form.</p>
        </iframe>

        <script>
        // Enhanced height calculation for full content display without scrolling
        (function() {
            var logger = window.OCHDDebugLogger;
            var iframe = document.getElementById('<?php echo esc_js( $iframe_id ); ?>');

            if (!iframe) {
                if (logger) {
                    logger.logError('Iframe not found for height calculation', new Error('Iframe ID: <?php echo esc_js( $iframe_id ); ?>'));
                }
                return;
            }

            if (logger) {
                logger.logIframeEvent('Starting enhanced height calculation script', {
                    iframeId: '<?php echo esc_js( $iframe_id ); ?>',
                    initialHeight: iframe.style.height || iframe.getAttribute('height'),
                    src: iframe.src,
                    timestamp: Date.now()
                });
            }

            var contentHeightDetected = false;
            var fallbackHeight = 800; // Dynamic fallback based on content needs
            var minHeight = 600; // Minimum reasonable height
            var maxReasonableHeight = 1200; // Maximum reasonable height to prevent excessive white space

            function detectContentHeight() {
                if (logger) logger.startTimer('content-height-detection');

                try {
                    if (logger) {
                        logger.logBookingHeight('Attempting content height detection', {
                            hasContentDocument: !!iframe.contentDocument,
                            hasBody: !!(iframe.contentDocument && iframe.contentDocument.body),
                            crossOrigin: false
                        });
                    }

                    // Try to access iframe content height (works if same-origin)
                    if (iframe.contentDocument && iframe.contentDocument.body) {
                        var measurements = {
                            bodyScrollHeight: iframe.contentDocument.body.scrollHeight,
                            bodyOffsetHeight: iframe.contentDocument.body.offsetHeight,
                            documentClientHeight: iframe.contentDocument.documentElement.clientHeight,
                            documentScrollHeight: iframe.contentDocument.documentElement.scrollHeight,
                            documentOffsetHeight: iframe.contentDocument.documentElement.offsetHeight
                        };

                        var contentHeight = Math.max(
                            measurements.bodyScrollHeight,
                            measurements.bodyOffsetHeight,
                            measurements.documentClientHeight,
                            measurements.documentScrollHeight,
                            measurements.documentOffsetHeight
                        );

                        if (logger) {
                            logger.logBookingHeight('Content height measurements', {
                                measurements: measurements,
                                calculatedHeight: contentHeight,
                                minHeight: minHeight,
                                willApply: contentHeight > minHeight
                            });
                        }

                        if (contentHeight > minHeight) {
                            // Cap the height to prevent excessive white space
                            var cappedHeight = Math.min(contentHeight, maxReasonableHeight);
                            var newHeight = cappedHeight + 30; // Add smaller buffer
                            var oldHeight = iframe.style.height;

                            iframe.style.height = newHeight + 'px';
                            contentHeightDetected = true;

                            if (logger) {
                                logger.logHeightChange(oldHeight, newHeight + 'px', 'Content height detection');
                                logger.endTimer('content-height-detection', 'Content height detection successful');
                            }

                            return true;
                        }
                    }
                } catch (e) {
                    // Cross-origin restriction - use fallback method
                    if (logger) {
                        logger.logBookingHeight('Cross-origin iframe detected, using fallback', {
                            error: e.message,
                            crossOrigin: true
                        });
                        logger.endTimer('content-height-detection', 'Content height detection failed (cross-origin)');
                    }
                }

                if (logger) {
                    logger.endTimer('content-height-detection', 'Content height detection failed');
                }
                return false;
            }

            function applyOptimalHeight() {
                if (logger) logger.startTimer('optimal-height-calculation');

                var isMobile = window.innerWidth <= 768;
                var isSmallMobile = window.innerWidth <= 480;
                var isVerySmallMobile = window.innerWidth <= 360;
                var isLandscape = window.innerWidth > window.innerHeight;

                var deviceInfo = {
                    viewport: window.innerWidth + 'x' + window.innerHeight,
                    isMobile: isMobile,
                    isSmallMobile: isSmallMobile,
                    isVerySmallMobile: isVerySmallMobile,
                    isLandscape: isLandscape,
                    timestamp: Date.now()
                };

                if (logger) {
                    logger.logBookingHeight('Starting optimal height calculation', deviceInfo);
                }

                // First try to detect actual content height
                if (detectContentHeight()) {
                    if (logger) {
                        logger.logBookingHeight('Content height detection successful, skipping fallback', {
                            finalHeight: iframe.style.height
                        });
                        logger.endTimer('optimal-height-calculation', 'Optimal height calculation (content detected)');
                    }
                    return; // Content height detected and applied
                }

                // Fallback: Use dynamic heights based on viewport and device
                var height;
                var calculatedMinHeight;
                var deviceType;

                if (isMobile) {
                    if (isVerySmallMobile) {
                        // Very small mobile: Dynamic height based on content needs
                        height = Math.min(fallbackHeight, 700) + 'px';
                        calculatedMinHeight = '600px';
                        deviceType = 'very-small-mobile' + (isLandscape ? '-landscape' : '');
                    } else if (isSmallMobile) {
                        // Small mobile: Responsive height
                        height = Math.min(fallbackHeight, 800) + 'px';
                        calculatedMinHeight = '650px';
                        deviceType = 'small-mobile' + (isLandscape ? '-landscape' : '');
                    } else {
                        // Regular mobile: Adaptive height
                        height = Math.min(fallbackHeight, 900) + 'px';
                        calculatedMinHeight = '700px';
                        deviceType = 'mobile' + (isLandscape ? '-landscape' : '');
                    }
                } else {
                    // Desktop: Dynamic height with reasonable maximum
                    height = Math.min(fallbackHeight, maxReasonableHeight) + 'px';
                    calculatedMinHeight = '600px';
                    deviceType = 'desktop';
                }

                var oldHeight = iframe.style.height;
                var oldMinHeight = iframe.style.minHeight;
                var oldMaxHeight = iframe.style.maxHeight;

                iframe.style.height = height;
                iframe.style.minHeight = calculatedMinHeight;
                // Set reasonable max-height to prevent excessive white space
                iframe.style.maxHeight = maxReasonableHeight + 'px';

                if (logger) {
                    logger.logBookingHeight('Applied fallback height calculation', {
                        deviceType: deviceType,
                        oldHeight: oldHeight,
                        newHeight: height,
                        oldMinHeight: oldMinHeight,
                        newMinHeight: minHeight,
                        oldMaxHeight: oldMaxHeight,
                        newMaxHeight: 'none',
                        fallbackHeight: fallbackHeight,
                        calculatedDesktopHeight: Math.max(fallbackHeight, 1200)
                    });

                    logger.endTimer('optimal-height-calculation', 'Optimal height calculation (fallback applied)');
                }
            }

            // Apply initial height with delay to allow iframe to start loading
            setTimeout(function() {
                applyOptimalHeight();
            }, 100);

            // Try to detect content height when iframe loads
            iframe.addEventListener('load', function() {
                if (logger) {
                    logger.logIframeEvent('Iframe load event triggered', {
                        src: iframe.src,
                        currentHeight: iframe.style.height,
                        timestamp: Date.now()
                    });
                }

                // Multiple attempts to detect height as content may load progressively
                setTimeout(function() {
                    if (logger) logger.log('Height detection attempt 1 (500ms delay)', 'BookingForm-Height', 'debug');
                    applyOptimalHeight();
                }, 500);

                setTimeout(function() {
                    if (logger) logger.log('Height detection attempt 2 (1000ms delay) - CRITICAL TIMING', 'BookingForm-Height', 'warn');
                    applyOptimalHeight();
                }, 1000);

                setTimeout(function() {
                    if (logger) logger.log('Height detection attempt 3 (2000ms delay)', 'BookingForm-Height', 'debug');
                    applyOptimalHeight();
                }, 2000);

                // Set up periodic checks for dynamic content
                var checkCount = 0;
                var heightCheckInterval = setInterval(function() {
                    checkCount++;

                    if (logger) {
                        logger.logBookingHeight('Periodic height check', {
                            checkNumber: checkCount,
                            maxChecks: 10,
                            currentHeight: iframe.style.height,
                            contentHeightDetected: contentHeightDetected
                        });
                    }

                    if (detectContentHeight() || checkCount > 10) {
                        if (logger) {
                            logger.logBookingHeight('Stopping periodic height checks', {
                                reason: contentHeightDetected ? 'content height detected' : 'max checks reached',
                                totalChecks: checkCount,
                                finalHeight: iframe.style.height
                            });
                        }
                        clearInterval(heightCheckInterval);
                    }
                }, 1000);
            });

            // Apply on window resize (but maintain content-based height if detected)
            window.addEventListener('resize', function() {
                if (logger) {
                    logger.logBookingHeight('Window resize detected in iframe script', {
                        newViewport: window.innerWidth + 'x' + window.innerHeight,
                        contentHeightDetected: contentHeightDetected,
                        willRecalculate: !contentHeightDetected
                    });
                }

                if (!contentHeightDetected) {
                    setTimeout(function() {
                        if (logger) logger.log('Recalculating height after resize', 'BookingForm-Height', 'debug');
                        applyOptimalHeight();
                    }, 100);
                } else {
                    if (logger) {
                        logger.logBookingHeight('Skipping height recalculation - content height already detected', {
                            currentHeight: iframe.style.height
                        });
                    }
                }
            });

            // Listen for messages from iframe (if Easy!Appointments sends height info)
            window.addEventListener('message', function(event) {
                if (logger) {
                    logger.logIframeEvent('Message received from iframe', {
                        origin: event.origin,
                        hasData: !!event.data,
                        dataType: typeof event.data,
                        hasHeight: event.data && typeof event.data.height === 'number',
                        height: event.data && event.data.height
                    });
                }

                if (event.data && typeof event.data.height === 'number' && event.data.height > minHeight) {
                    var newHeight = event.data.height + 50;
                    var oldHeight = iframe.style.height;

                    iframe.style.height = newHeight + 'px';
                    contentHeightDetected = true;

                    if (logger) {
                        logger.logHeightChange(oldHeight, newHeight + 'px', 'Message from iframe');
                        logger.logIframeEvent('Height set from iframe message', {
                            receivedHeight: event.data.height,
                            appliedHeight: newHeight,
                            buffer: 50
                        });
                    }
                }
            });
        })();
        </script>
        <?php
        return ob_get_clean();
    }
}