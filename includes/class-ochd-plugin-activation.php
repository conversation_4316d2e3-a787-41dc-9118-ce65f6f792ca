<?php

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_Plugin_Activation {

    public static function create_plugin_pages() {
        // Initialize default settings
        self::initialize_default_settings();

        // Create pages based on current mode
        self::create_pages_for_current_mode();
    }

    public static function initialize_default_settings() {
        // Set default profile display mode if not already set
        if ( ! get_option( 'ochd_profile_display_mode' ) ) {
            update_option( 'ochd_profile_display_mode', 'separate' );
        }

        // Initialize other default settings
        if ( ! get_option( 'ochd_use_unified_profile' ) ) {
            $display_mode = get_option( 'ochd_profile_display_mode', 'separate' );
            update_option( 'ochd_use_unified_profile', $display_mode === 'unified' );
        }

        // Set plugin version for future migrations
        update_option( 'ochd_plugin_version', '1.1' );
    }

    public static function create_pages_for_current_mode() {
        $display_mode = get_option( 'ochd_profile_display_mode', 'separate' );

        if ( $display_mode === 'unified' ) {
            self::create_unified_profile_page();
        } else {
            self::create_separate_pages();
        }
    }

    public static function create_separate_pages() {
        $pages = [
            'ochd_profile_page_id' => [
                'title' => 'My Profile',
                'content' => '[ochd_profile_content]',
                'slug' => 'my-profile'
            ],
            'ochd_bookings_page_id' => [
                'title' => 'My Bookings',
                'content' => '[ochd_bookings_content]',
                'slug' => 'my-bookings'
            ]
        ];

        foreach ($pages as $option_name => $page_data) {
            $page_id = get_option( $option_name );
            $page_exists = ($page_id) ? get_post($page_id) : null;

            if ( ! $page_exists ) {
                $page = get_page_by_title( $page_data['title'] );
                if ( ! $page ) {
                    $new_page_id = wp_insert_post([
                        'post_title'     => $page_data['title'],
                        'post_content'   => $page_data['content'],
                        'post_status'    => 'publish',
                        'post_author'    => 1,
                        'post_type'      => 'page',
                        'post_name'      => $page_data['slug'],
                        'comment_status' => 'closed',
                        'ping_status'    => 'closed',
                        'meta_input'     => [
                            'ochd_page_type' => str_replace('ochd_', '', str_replace('_page_id', '', $option_name))
                        ]
                    ]);
                    update_option( $option_name, $new_page_id );
                } else {
                    update_option( $option_name, $page->ID );
                }
            }
        }
    }

    public static function create_unified_profile_page() {
        $page_id = get_option( 'ochd_unified_profile_page_id' );
        $page_exists = ($page_id) ? get_post($page_id) : null;

        if ( ! $page_exists ) {
            $page = get_page_by_title( 'My Account' );
            if ( ! $page ) {
                $new_page_id = wp_insert_post([
                    'post_title'     => 'My Account',
                    'post_content'   => '[ochd_unified_profile]',
                    'post_status'    => 'publish',
                    'post_author'    => 1,
                    'post_type'      => 'page',
                    'post_name'      => 'my-account',
                    'comment_status' => 'closed',
                    'ping_status'    => 'closed',
                    'meta_input'     => [
                        'ochd_page_type' => 'unified_profile'
                    ]
                ]);
                update_option( 'ochd_unified_profile_page_id', $new_page_id );
            } else {
                update_option( 'ochd_unified_profile_page_id', $page->ID );
            }
        }

        // Also create separate pages as fallback
        self::create_separate_pages();
    }

    public static function handle_plugin_update() {
        $current_version = get_option( 'ochd_plugin_version', '1.0' );
        $new_version = '1.1';

        if ( version_compare( $current_version, $new_version, '<' ) ) {
            // Handle migration from older versions
            self::migrate_from_version( $current_version );
            update_option( 'ochd_plugin_version', $new_version );
        }
    }

    public static function migrate_from_version( $from_version ) {
        if ( version_compare( $from_version, '1.1', '<' ) ) {
            // Migration from pre-unified profile versions

            // Initialize new settings
            if ( ! get_option( 'ochd_profile_display_mode' ) ) {
                update_option( 'ochd_profile_display_mode', 'separate' );
            }

            // Ensure separate pages exist for backward compatibility
            self::create_separate_pages();

            // Log migration
            error_log( "OCHandyDude Plugin: Migrated from version {$from_version} to 1.1" );
        }
    }

    public static function cleanup_on_deactivation() {
        // Don't delete pages on deactivation, just clean up transients
        global $wpdb;

        // Clean up transients
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_ochd_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_ochd_%'" );

        // Log deactivation
        error_log( "OCHandyDude Plugin: Cleaned up transients on deactivation" );
    }
}
