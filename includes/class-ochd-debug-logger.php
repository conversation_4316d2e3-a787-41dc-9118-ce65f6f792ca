<?php
/**
 * OCHandyDude Debug Logger
 * Centralized logging system for troubleshooting booking form height issues and integration problems
 */

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_Debug_Logger {
    
    private static $instance = null;
    private $debug_enabled = false;
    private $wp_debug_enabled = false;
    private $session_id = '';
    
    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->debug_enabled = get_option( 'ochd_enable_debug_logging', false );
        $this->wp_debug_enabled = defined( 'WP_DEBUG' ) && WP_DEBUG;
        $this->session_id = $this->generate_session_id();
        
        // Add JavaScript logging functions to frontend
        if ( $this->debug_enabled && ! is_admin() ) {
            add_action( 'wp_footer', [ $this, 'output_js_logger' ], 5 );
            add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_debug_scripts' ] );
        }
    }
    
    /**
     * Generate unique session ID for tracking logs across page loads
     */
    private function generate_session_id() {
        if ( ! session_id() ) {
            return substr( md5( uniqid() ), 0, 8 );
        }
        return substr( session_id(), 0, 8 );
    }
    
    /**
     * Main logging function - routes to appropriate logging method based on configuration
     */
    public function log( $message, $component = 'OCHandyDude', $level = 'info', $data = null ) {
        if ( ! $this->debug_enabled ) {
            return; // Logging disabled - no performance impact
        }
        
        $log_entry = $this->format_log_entry( $message, $component, $level, $data );
        
        // Level 3: Both WordPress debug.log and browser console (WP_DEBUG enabled)
        if ( $this->wp_debug_enabled ) {
            $this->log_to_file( $log_entry );
            $this->log_to_console( $log_entry );
        }
        // Level 2: Browser console only (WP_DEBUG disabled)
        else {
            $this->log_to_console( $log_entry );
        }
    }
    
    /**
     * Format log entry with timestamp and session info
     */
    private function format_log_entry( $message, $component, $level, $data ) {
        $timestamp = current_time( 'Y-m-d H:i:s.u' );
        $user_info = is_user_logged_in() ? wp_get_current_user()->user_login : 'guest';
        
        $entry = [
            'timestamp' => $timestamp,
            'session_id' => $this->session_id,
            'component' => $component,
            'level' => strtoupper( $level ),
            'user' => $user_info,
            'message' => $message,
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'viewport' => '', // Will be filled by JavaScript
            'data' => $data
        ];
        
        return $entry;
    }
    
    /**
     * Log to WordPress debug.log file
     */
    private function log_to_file( $log_entry ) {
        $log_line = sprintf(
            "[%s] [%s] [%s] %s: %s",
            $log_entry['timestamp'],
            $log_entry['session_id'],
            $log_entry['component'],
            $log_entry['level'],
            $log_entry['message']
        );
        
        if ( $log_entry['data'] ) {
            $log_line .= ' | Data: ' . wp_json_encode( $log_entry['data'] );
        }
        
        error_log( $log_line );
    }
    
    /**
     * Queue log entry for browser console output
     */
    private function log_to_console( $log_entry ) {
        if ( ! isset( $GLOBALS['ochd_console_logs'] ) ) {
            $GLOBALS['ochd_console_logs'] = [];
        }
        
        $GLOBALS['ochd_console_logs'][] = $log_entry;
    }
    
    /**
     * Enqueue debug scripts and localize ajaxurl
     */
    public function enqueue_debug_scripts() {
        if ( ! $this->debug_enabled ) {
            return;
        }

        // Create a minimal script handle for localization
        wp_register_script( 'ochd-debug-ajax', '', [], '1.0', true );
        wp_enqueue_script( 'ochd-debug-ajax' );

        // Localize ajaxurl for debug logging
        wp_localize_script( 'ochd-debug-ajax', 'ochdDebugAjax', [
            'ajaxurl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'ochd_debug_log' ),
            'enabled' => $this->debug_enabled,
            'wpDebugEnabled' => $this->wp_debug_enabled
        ] );
    }

    /**
     * Output JavaScript logger and queued console logs
     */
    public function output_js_logger() {
        if ( ! $this->debug_enabled ) {
            return;
        }
        
        $console_logs = $GLOBALS['ochd_console_logs'] ?? [];
        ?>
        <script type="text/javascript">
        // OCHandyDude Debug Logger - JavaScript Component
        window.OCHDDebugLogger = {
            enabled: <?php echo $this->debug_enabled ? 'true' : 'false'; ?>,
            sessionId: '<?php echo esc_js( $this->session_id ); ?>',
            wpDebugEnabled: <?php echo $this->wp_debug_enabled ? 'true' : 'false'; ?>,
            
            log: function(message, component, level, data) {
                if (!this.enabled) return;

                try {
                    component = component || 'OCHandyDude-JS';
                    level = level || 'info';
                
                var timestamp = new Date().toISOString();
                var viewport = window.innerWidth + 'x' + window.innerHeight;
                var userAgent = navigator.userAgent;
                
                var logEntry = {
                    timestamp: timestamp,
                    sessionId: this.sessionId,
                    component: component,
                    level: level.toUpperCase(),
                    message: message,
                    viewport: viewport,
                    userAgent: userAgent,
                    url: window.location.href,
                    data: data
                };
                
                // Format console output based on level
                var consoleMethod = level === 'error' ? 'error' : 
                                  level === 'warn' ? 'warn' : 
                                  level === 'debug' ? 'debug' : 'log';
                
                var consoleMessage = '[' + timestamp + '] [' + this.sessionId + '] [' + component + '] ' + 
                                   level.toUpperCase() + ': ' + message;
                
                if (data) {
                    console[consoleMethod](consoleMessage, data);
                } else {
                    console[consoleMethod](consoleMessage);
                }
                
                    // Send to WordPress if WP_DEBUG is enabled
                    if (this.wpDebugEnabled && typeof jQuery !== 'undefined' && typeof ochdDebugAjax !== 'undefined') {
                        jQuery.post(ochdDebugAjax.ajaxurl, {
                            action: 'ochd_log_to_file',
                            log_entry: JSON.stringify(logEntry),
                            nonce: ochdDebugAjax.nonce
                        }).fail(function() {
                            // Silently fail AJAX logging to prevent console spam
                        });
                    }
                } catch (error) {
                    // Prevent debug logging from breaking the page
                    console.error('OCHandyDude Debug Logger Error:', error);
                }
            },
            
            // Basic logging level methods
            debug: function(message, component, data) {
                this.log(message, component || 'OCHandyDude-JS', 'debug', data);
            },

            info: function(message, component, data) {
                this.log(message, component || 'OCHandyDude-JS', 'info', data);
            },

            warn: function(message, component, data) {
                this.log(message, component || 'OCHandyDude-JS', 'warn', data);
            },

            error: function(message, component, data) {
                this.log(message, component || 'OCHandyDude-JS', 'error', data);
            },

            // Specialized logging methods
            logBookingHeight: function(message, heightData) {
                this.log(message, 'BookingForm-Height', 'debug', heightData);
            },
            
            logIframeEvent: function(message, iframeData) {
                this.log(message, 'BookingForm-Iframe', 'info', iframeData);
            },
            
            logHeightChange: function(oldHeight, newHeight, reason) {
                this.log('Height changed: ' + oldHeight + ' → ' + newHeight + ' (' + reason + ')', 
                        'BookingForm-Height', 'info', {
                    oldHeight: oldHeight,
                    newHeight: newHeight,
                    reason: reason,
                    timestamp: Date.now()
                });
            },
            
            logError: function(message, error) {
                this.log(message, 'OCHandyDude-Error', 'error', {
                    error: error.toString(),
                    stack: error.stack
                });
            },
            
            // Performance monitoring
            startTimer: function(name) {
                this._timers = this._timers || {};
                this._timers[name] = Date.now();
            },
            
            endTimer: function(name, message) {
                if (!this._timers || !this._timers[name]) return;
                
                var duration = Date.now() - this._timers[name];
                delete this._timers[name];
                
                this.log((message || name) + ' completed in ' + duration + 'ms', 
                        'Performance', 'debug', { duration: duration });
            }
        };
        
        // Output queued PHP logs to console (safe output to prevent syntax errors)
        <?php if ( ! empty( $console_logs ) ): ?>
        try {
            console.group('OCHandyDude Debug Logs (from PHP)');
            <?php foreach ( $console_logs as $log ): ?>
            <?php
            $safe_message = '[' . $log['timestamp'] . '] [' . $log['session_id'] . '] [' . $log['component'] . '] ' . $log['level'] . ': ' . $log['message'];
            $safe_message = str_replace( array( "\r", "\n", "\t" ), ' ', $safe_message );
            $safe_message = preg_replace( '/[^\x20-\x7E]/', '', $safe_message );
            ?>
            console.log(<?php echo wp_json_encode( $safe_message ); ?>);
            <?php endforeach; ?>
            console.groupEnd();
        } catch (e) {
            console.error('OCHandyDude Debug Logger: Error outputting PHP logs', e);
        }
        <?php endif; ?>
        
        // Log initial page load info
        OCHDDebugLogger.log('Debug logging initialized', 'DebugLogger', 'info', {
            wpDebugEnabled: OCHDDebugLogger.wpDebugEnabled,
            viewport: window.innerWidth + 'x' + window.innerHeight,
            userAgent: navigator.userAgent,
            url: window.location.href
        });
        </script>
        <?php
    }
    
    /**
     * AJAX handler for logging JavaScript messages to WordPress debug.log
     */
    public function ajax_log_to_file() {
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ochd_debug_log' ) ) {
            wp_die( 'Invalid nonce' );
        }
        
        if ( ! $this->debug_enabled || ! $this->wp_debug_enabled ) {
            wp_die( 'Logging not enabled' );
        }
        
        $log_entry = json_decode( stripslashes( $_POST['log_entry'] ?? '{}' ), true );
        
        if ( $log_entry ) {
            $log_line = sprintf(
                "[%s] [%s] [%s] %s: %s | Viewport: %s",
                $log_entry['timestamp'],
                $log_entry['sessionId'],
                $log_entry['component'],
                $log_entry['level'],
                $log_entry['message'],
                $log_entry['viewport']
            );
            
            if ( isset( $log_entry['data'] ) ) {
                $log_line .= ' | Data: ' . wp_json_encode( $log_entry['data'] );
            }
            
            error_log( $log_line );
        }
        
        wp_die();
    }
    
    /**
     * Convenience methods for different log levels
     */
    public function debug( $message, $component = 'OCHandyDude', $data = null ) {
        $this->log( $message, $component, 'debug', $data );
    }
    
    public function info( $message, $component = 'OCHandyDude', $data = null ) {
        $this->log( $message, $component, 'info', $data );
    }
    
    public function warn( $message, $component = 'OCHandyDude', $data = null ) {
        $this->log( $message, $component, 'warn', $data );
    }
    
    public function error( $message, $component = 'OCHandyDude', $data = null ) {
        $this->log( $message, $component, 'error', $data );
    }
    
    /**
     * Check if debug logging is enabled
     */
    public function is_enabled() {
        return $this->debug_enabled;
    }
    
    /**
     * Get current logging configuration
     */
    public function get_config() {
        return [
            'debug_enabled' => $this->debug_enabled,
            'wp_debug_enabled' => $this->wp_debug_enabled,
            'session_id' => $this->session_id,
            'logging_level' => $this->wp_debug_enabled ? 3 : 2
        ];
    }
}
