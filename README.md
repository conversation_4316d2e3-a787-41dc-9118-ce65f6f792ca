# OCHandyDude WordPress Plugin

A comprehensive WordPress plugin providing unified profile management, booking system integration, and Invoice Ninja integration for handyman service businesses.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Installation](#installation)
- [Configuration](#configuration)
- [Project Structure](#project-structure)
- [API Integrations](#api-integrations)
- [Developer Documentation](#developer-documentation)
- [Troubleshooting](#troubleshooting)
- [Security](#security)
- [Changelog](#changelog)

## Overview

OCHandyDude is a production-ready WordPress plugin designed for handyman service businesses. It provides a unified profile system, booking form integration with Easy!Appointments, and comprehensive Invoice Ninja integration for billing and project management.

### Key Features

- **Unified Profile System**: Single interface for profile management and bookings
- **Easy!Appointments Integration**: Built-in booking form functionality (no third-party plugin required)
- **Invoice Ninja Integration**: Complete billing, invoicing, and project management
- **Responsive Design**: Mobile-first approach with carousel navigation
- **SSO Integration**: Keycloak authentication support
- **Security-First**: All sensitive data configurable via WordPress admin

## Features

### 🔧 **Profile Management**
- Unified profile interface combining user data and bookings
- Responsive carousel navigation for mobile devices
- Guest overlay system for non-authenticated users
- Keycloak SSO integration for seamless authentication

### 📅 **Booking System**
- Integrated Easy!Appointments functionality
- Responsive booking forms with dynamic height adjustment
- Support for provider and service parameters
- Guest booking capabilities with user registration prompts

### 💰 **Invoice Ninja Integration**
- Real-time project and task synchronization
- Invoice status tracking with click-to-portal functionality
- Task-invoice relationship display with expandable details
- Client filtering based on WordPress user credentials

### 📱 **Mobile Optimization**
- Responsive design optimized for all screen sizes
- Carousel navigation for narrow screens (320px+)
- Touch-friendly interface with smooth animations
- Optimized booking forms for mobile devices

## Installation

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

### Installation Steps

1. **Upload Plugin Files**
   ```bash
   # Upload the plugin directory to your WordPress installation
   wp-content/plugins/ochandydude-master/
   ```

2. **Activate Plugin**
   - Go to WordPress Admin > Plugins
   - Find "OCHandyDude Master" and click "Activate"

3. **Configure Settings**
   - Navigate to Settings > OCHandyDude
   - Configure required integrations (see Configuration section)

## Configuration

### WordPress Admin Settings

Access plugin settings at **WordPress Admin > Settings > OCHandyDude**

#### **Invoice Ninja Integration**
```
API URL: https://your-invoice-ninja-domain.com
API Token: [Generate from Invoice Ninja > Settings > API Tokens]
```

#### **Easy!Appointments Integration**
```
Easy!Appointments URL: https://your-domain.com/easyappointments
```

#### **Security Configuration**
```
Keycloak Account URL: https://auth.your-domain.com/realms/your-realm/account/
Booking CORS Origin: https://book.your-domain.com (optional)
```

### Shortcode Usage

#### **Unified Profile**
```html
[ochd_unified_profile]
```
Displays the complete unified profile interface with carousel navigation.

#### **Booking Form**
```html
<!-- Basic booking form -->
[ochandydude_booking_form]

<!-- With parameters -->
[ochandydude_booking_form provider="1" service="2"]
```

#### **Profile Menu**
```html
[ochandydude_profile_menu]
```
Displays the profile dropdown menu for authenticated users.

## Project Structure

```
ochandydude-master/
├── ochandydude-master.php          # Main plugin file
├── README.md                       # This documentation
├── assets/                         # Frontend assets
│   ├── css/                       # Stylesheets
│   │   ├── booking-form.css       # Booking form styles
│   │   ├── mobile-menu.css        # Mobile navigation
│   │   └── profile-menu.css       # Profile interface styles
│   └── js/                        # JavaScript files
│       ├── booking-form.js         # Booking form functionality
│       ├── carousel-tabs.js        # Carousel navigation
│       ├── header-icon-manager.js  # Header icon management
│       ├── invoice-ninja-integration.js # Invoice Ninja API
│       ├── mobile-menu.js          # Mobile menu functionality
│       ├── profile-menu-navigation.js # Profile navigation
│       ├── profile-menu.js         # Profile menu functionality
│       └── profile-tabs.js         # Tab switching logic
└── includes/                       # PHP classes
    ├── class-ochd-admin-settings.php    # Admin settings page
    ├── class-ochd-invoice-ninja-integration.php # Invoice Ninja API
    ├── class-ochd-mobile-menu.php       # Mobile menu handler
    ├── class-ochd-plugin-activation.php # Plugin activation
    ├── class-ochd-profile-assets.php    # Asset management
    ├── class-ochd-profile-shortcodes.php # Shortcode handlers
    ├── class-ochd-smart-booking.php     # Booking system
    ├── class-ochd-sso-integration.php   # SSO functionality
    └── class-ochd-title-manager.php     # Page title management
```

### Core Classes

#### **OCHD_Admin_Settings**
- Manages WordPress admin settings page
- Handles API configuration and security settings
- Provides connection testing for integrations

#### **OCHD_Invoice_Ninja_Integration**
- Singleton class for Invoice Ninja API communication
- Handles client, project, task, and invoice data
- Provides caching and error handling

#### **OCHD_Profile_Shortcodes**
- Renders unified profile interface
- Manages carousel navigation and responsive design
- Handles user authentication and guest overlays

#### **OCHD_Smart_Booking**
- Integrated Easy!Appointments functionality
- Dynamic iframe height calculation
- Guest booking with user registration prompts

## API Integrations

### Invoice Ninja API

#### **Authentication**
```php
// API configuration
$api_url = get_option('ochd_invoice_ninja_api_url');
$api_token = get_option('ochd_invoice_ninja_api_token');

// Headers
'X-API-TOKEN' => $api_token
'X-Requested-With' => 'XMLHttpRequest'
'Content-Type' => 'application/json'
```

#### **Endpoints Used**
- `GET /api/v1/clients` - Client data retrieval
- `GET /api/v1/projects` - Project information
- `GET /api/v1/tasks` - Task management
- `GET /api/v1/invoices` - Invoice data
- `GET /api/v1/task_statuses` - Status information

#### **Client Filtering**
```php
// Filter by WordPress username (Keycloak UUID)
$username = wp_get_current_user()->user_login;
$clients = $api->get_clients(['id_number' => $username]);
```

### Easy!Appointments Integration

#### **Iframe Generation**
```php
// Dynamic iframe with responsive height
$booking_url = get_option('easyappointments_url');
$iframe_html = '<iframe src="' . $booking_url . '" height="2000" scrolling="no">';
```

#### **Parameter Support**
```php
// URL parameters for provider/service selection
$query_params = [
    'provider' => $provider_id,
    'service' => $service_id
];
$final_url = $booking_url . '?' . http_build_query($query_params);
```

## Developer Documentation

### Hooks and Filters

#### **Filters**
```php
// Customize Keycloak account URL
apply_filters('ochd_keycloak_account_url', $default_url);

// Modify profile display mode
apply_filters('ochd_profile_display_mode', $mode);

// Customize Invoice Ninja API timeout
apply_filters('ochd_invoice_ninja_timeout', 30);
```

#### **Actions**
```php
// Before profile content render
do_action('ochd_before_profile_content');

// After booking form render
do_action('ochd_after_booking_form');

// On Invoice Ninja API error
do_action('ochd_invoice_ninja_error', $error_message);
```

### Custom Development

#### **Adding New Shortcodes**
```php
class Custom_Shortcode_Handler {
    public function __construct() {
        add_shortcode('custom_shortcode', [$this, 'render_shortcode']);
    }
    
    public function render_shortcode($atts) {
        // Custom shortcode logic
        return $output;
    }
}
```

#### **Extending Invoice Ninja Integration**
```php
$invoice_ninja = OCHD_Invoice_Ninja_Integration::get_instance();
$custom_data = $invoice_ninja->make_request('GET', '/api/v1/custom_endpoint');
```

### Database Schema

#### **WordPress Options**
```sql
-- Core settings
ochd_invoice_ninja_api_url          # Invoice Ninja API URL
ochd_invoice_ninja_api_token        # Invoice Ninja API token
easyappointments_url               # Easy!Appointments URL
ochd_profile_display_mode          # Profile display mode (unified/separate)

-- Security settings
ochd_keycloak_account_url          # Keycloak account management URL
ochd_booking_cors_origin           # Allowed CORS origin for booking
```

## Troubleshooting

### Common Issues

#### **Invoice Ninja Connection Failed**
```bash
# Check API URL and token
curl -H "X-API-TOKEN: your-token" https://your-domain.com/api/v1/ping

# Verify WordPress options
wp option get ochd_invoice_ninja_api_url
wp option get ochd_invoice_ninja_api_token
```

#### **Booking Form Not Loading**
1. Verify Easy!Appointments URL in settings
2. Check that Easy!Appointments installation is accessible
3. Ensure no CORS restrictions blocking iframe

#### **Profile Menu Not Displaying**
1. Check user authentication status
2. Verify shortcode placement: `[ochd_unified_profile]`
3. Ensure CSS/JS assets are loading properly

#### **Mobile Layout Issues**
1. Clear browser cache and WordPress cache
2. Check for theme CSS conflicts
3. Verify responsive breakpoints in browser dev tools

### Debug Mode

Enable WordPress debug mode for detailed logging:
```php
// wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Performance Optimization

#### **Caching**
- Invoice Ninja API responses are cached for 5 minutes
- Profile data cached per user session
- Asset minification recommended for production

#### **Database Queries**
- Optimized WordPress option retrieval
- Minimal database queries per page load
- Efficient user authentication checks

## Security

### Security Features

#### **Data Protection**
- All sensitive data stored in WordPress options (encrypted)
- No hardcoded API keys or credentials
- Secure AJAX nonce verification
- Input sanitization and output escaping

#### **Authentication**
- WordPress user authentication required
- Keycloak SSO integration support
- Guest access with registration prompts
- CORS origin validation for booking forms

#### **API Security**
- Secure API token transmission
- Request timeout limits
- Error message sanitization
- Rate limiting considerations

### Security Best Practices

1. **Regular Updates**: Keep plugin and dependencies updated
2. **Strong API Tokens**: Use complex, unique API tokens
3. **HTTPS Only**: Ensure all API endpoints use HTTPS
4. **Access Control**: Limit WordPress admin access
5. **Monitoring**: Monitor API usage and error logs

## Changelog

### Version 2.0.0 (Production Release)
- **Security**: Removed all hardcoded credentials
- **Integration**: Complete Easy!Appointments integration (no third-party plugin required)
- **Cleanup**: Removed development files and consolidated documentation
- **Enhancement**: Dynamic booking form height calculation
- **Fix**: Task/invoice relationship expansion functionality
- **Improvement**: Responsive carousel navigation for narrow screens
- **Security**: Added configurable security settings for all external URLs

### Previous Versions
- v1.x: Development and testing phases
- Multiple iterations of UI/UX improvements
- Invoice Ninja integration development
- Mobile responsiveness enhancements

---

## Support

For technical support or feature requests, please refer to the plugin documentation or contact the development team.

**Plugin Version**: 2.0.0 (Production)  
**WordPress Compatibility**: 5.0+  
**PHP Compatibility**: 7.4+  
**Last Updated**: 2024
