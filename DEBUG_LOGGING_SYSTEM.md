# OCHandyDude Debug Logging System

## Overview

The OCHandyDude plugin includes a comprehensive debug logging system specifically designed to troubleshoot booking form height issues and other integration problems. The system provides multi-level logging with detailed insights into iframe behavior, height calculations, and dynamic content changes.

## Features

### 🔧 **Multi-Level Logging System**
- **Level 1 (Disabled)**: No logging - optimal production performance
- **Level 2 (Debug Enabled + WP_DEBUG Off)**: Browser console logging only
- **Level 3 (Debug Enabled + WP_DEBUG On)**: Both WordPress debug.log and browser console

### 📊 **Specialized Logging Categories**
- **Booking Form Height**: Detailed height calculation and change tracking
- **Iframe Events**: Load events, content detection, and cross-origin handling
- **Performance Monitoring**: Timing measurements for critical operations
- **Error Tracking**: Comprehensive error logging with stack traces
- **Device/Viewport**: Responsive behavior and device-specific calculations

### ⏱️ **Critical Timing Analysis**
- Timestamps for all log entries to track the "1-second height truncation issue"
- Performance timers for height calculation operations
- Periodic status reporting for ongoing monitoring
- Event sequence tracking for debugging dynamic changes

## Configuration

### WordPress Admin Settings

Navigate to **Settings > OCHandyDude > Debug & Troubleshooting**

#### **Enable Debug Logging**
- **Checkbox**: Enable comprehensive logging for troubleshooting
- **Description**: Provides detailed logs for booking form height truncation, API integration issues, and other plugin problems
- **Warning**: Shows active status when enabled with performance reminder

#### **WordPress Debug Status**
- **Automatic Detection**: Shows current WP_DEBUG status
- **Level Indication**: Explains logging level based on WP_DEBUG setting
- **Configuration Guidance**: Instructions for enabling file logging

### Logging Levels Explained

#### **Level 1: Disabled (Production)**
```php
ochd_enable_debug_logging = false
```
- No performance impact
- No log output
- Optimal for production environments

#### **Level 2: Console Only**
```php
ochd_enable_debug_logging = true
WP_DEBUG = false (or undefined)
```
- Browser console logging only
- No file system writes
- Good for development/staging

#### **Level 3: Full Logging**
```php
ochd_enable_debug_logging = true
WP_DEBUG = true
WP_DEBUG_LOG = true
```
- Browser console AND WordPress debug.log
- Complete audit trail
- Best for comprehensive troubleshooting

## Usage

### JavaScript Logging API

#### **Basic Logging Methods**
```javascript
const logger = window.OCHDDebugLogger;

// Basic levels
logger.debug('Debug message', 'Component');
logger.info('Info message', 'Component');
logger.warn('Warning message', 'Component');
logger.error('Error message', 'Component');

// With data
logger.log('Message with data', 'Component', 'info', {
    key: 'value',
    timestamp: Date.now()
});
```

#### **Specialized Booking Form Methods**
```javascript
// Height-specific logging
logger.logBookingHeight('Height calculation started', {
    viewport: '1024x768',
    deviceType: 'desktop',
    currentHeight: '800px'
});

// Iframe event logging
logger.logIframeEvent('Iframe loaded', {
    iframeId: 'booking-iframe',
    src: 'https://example.com',
    loadTime: Date.now()
});

// Height change tracking
logger.logHeightChange('600px', '800px', 'Content detection');

// Error logging with stack traces
logger.logError('Height detection failed', error);
```

#### **Performance Monitoring**
```javascript
// Start timing
logger.startTimer('height-calculation');

// ... perform operation ...

// End timing with automatic duration logging
logger.endTimer('height-calculation', 'Height calculation completed');
```

### PHP Logging API

#### **Basic Usage**
```php
$logger = OCHD_Debug_Logger::get_instance();

// Basic levels
$logger->debug('Debug message', 'Component');
$logger->info('Info message', 'Component');
$logger->warn('Warning message', 'Component');
$logger->error('Error message', 'Component');

// With data
$logger->log('Message', 'Component', 'info', [
    'key' => 'value',
    'user_id' => get_current_user_id()
]);
```

#### **Configuration Check**
```php
$logger = OCHD_Debug_Logger::get_instance();

if ($logger->is_enabled()) {
    // Perform debug operations
    $logger->info('Debug operations active');
}

// Get current configuration
$config = $logger->get_config();
// Returns: debug_enabled, wp_debug_enabled, session_id, logging_level
```

## Booking Form Height Issue Debugging

### Key Log Messages to Monitor

#### **Critical Timing Points**
```
[TIMESTAMP] [SESSION] [BookingForm-Height] WARN: Height detection attempt 2 (1000ms delay) - CRITICAL TIMING
```
This marks the critical 1-second point where height truncation typically occurs.

#### **Height Change Detection**
```
[TIMESTAMP] [SESSION] [BookingForm-Height] INFO: Height changed: 800px → 600px (MutationObserver detected style change)
```
Tracks when and why height changes occur.

#### **Content Height Detection**
```
[TIMESTAMP] [SESSION] [BookingForm-Height] DEBUG: Content height measurements
Data: {
    "measurements": {
        "bodyScrollHeight": 1200,
        "documentScrollHeight": 1200
    },
    "calculatedHeight": 1200,
    "willApply": true
}
```
Shows actual content measurements when cross-origin restrictions allow.

#### **Fallback Height Application**
```
[TIMESTAMP] [SESSION] [BookingForm-Height] INFO: Applied fallback height calculation
Data: {
    "deviceType": "desktop",
    "oldHeight": "800px",
    "newHeight": "1200px",
    "reason": "content detection failed"
}
```
Indicates when and why fallback heights are used.

### Debugging Workflow

#### **Step 1: Enable Debug Logging**
1. Go to WordPress Admin > Settings > OCHandyDude
2. Check "Enable Debug Logging"
3. Save settings

#### **Step 2: Reproduce the Issue**
1. Navigate to page with booking form
2. Open browser developer tools (F12)
3. Go to Console tab
4. Refresh the page

#### **Step 3: Analyze Log Output**
Look for these patterns:
- Initial height setting
- 1-second timing critical point
- Height changes after initial load
- Cross-origin restrictions
- Fallback height applications

#### **Step 4: Check WordPress Debug Log** (if WP_DEBUG enabled)
```bash
tail -f /path/to/wordpress/wp-content/debug.log | grep OCHandyDude
```

### Common Issues and Log Patterns

#### **Issue: Height Truncation After 1 Second**
**Log Pattern:**
```
[TIME] Height detection attempt 1 (500ms delay)
[TIME] Height detection attempt 2 (1000ms delay) - CRITICAL TIMING
[TIME] Height changed: 1200px → 600px (MutationObserver detected style change)
```
**Diagnosis:** External script overriding height after initial load

#### **Issue: Cross-Origin Content Detection Failure**
**Log Pattern:**
```
[TIME] Cross-origin iframe detected, using fallback
[TIME] Applied fallback height calculation
```
**Diagnosis:** Cannot access iframe content due to CORS restrictions

#### **Issue: Responsive Height Calculation Problems**
**Log Pattern:**
```
[TIME] Applied mobile height constraints
Data: { "deviceType": "mobile", "viewport": "375x667" }
[TIME] Window resize detected
[TIME] Height changed: calc(100vh - 120px) → calc(100vh - 100px)
```
**Diagnosis:** Responsive calculations changing unexpectedly

## Performance Impact

### When Disabled (Production)
- **Zero Performance Impact**: No logging code executes
- **No Memory Usage**: Logger instance minimal footprint
- **No Network Requests**: No AJAX logging calls

### When Enabled (Development)
- **Minimal Impact**: Logging operations are lightweight
- **Efficient Queuing**: Console logs batched for output
- **Smart Filtering**: Only relevant events logged
- **Automatic Cleanup**: Timers and intervals properly managed

### Memory Management
- **Session-Based**: Logs tied to user session
- **Automatic Rotation**: Old logs naturally expire
- **Efficient Storage**: Minimal memory footprint
- **Clean Shutdown**: Proper cleanup on page unload

## Troubleshooting the Debug System

### Debug Logging Not Working

#### **Check 1: Setting Enabled**
```javascript
console.log('Debug enabled:', window.OCHDDebugLogger?.enabled);
```

#### **Check 2: Logger Available**
```javascript
console.log('Logger available:', typeof window.OCHDDebugLogger);
```

#### **Check 3: WordPress Setting**
```php
var_dump(get_option('ochd_enable_debug_logging'));
```

### No Console Output

#### **Check Browser Console Filters**
- Ensure console shows all message types
- Check for console.log filtering
- Verify no browser extensions blocking output

#### **Check JavaScript Errors**
- Look for JavaScript errors preventing logger initialization
- Verify all dependencies loaded correctly

### No File Logging (Level 3)

#### **Check WordPress Debug Configuration**
```php
// wp-config.php should have:
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

#### **Check File Permissions**
```bash
# Debug log should be writable
ls -la wp-content/debug.log
```

#### **Check AJAX Handler**
- Verify AJAX requests reaching server
- Check nonce validation
- Confirm user permissions

## Advanced Configuration

### Custom Log Filtering
```javascript
// Filter logs by component
window.OCHDDebugLogger.componentFilter = ['BookingForm-Height'];

// Filter logs by level
window.OCHDDebugLogger.levelFilter = ['error', 'warn'];
```

### Extended Monitoring
```javascript
// Monitor specific iframe
window.OCHDDebugLogger.monitorIframe('specific-iframe-id');

// Custom height change detection
window.OCHDDebugLogger.onHeightChange = function(oldHeight, newHeight, reason) {
    // Custom handling
};
```

### Integration with External Tools
```javascript
// Send logs to external service
window.OCHDDebugLogger.externalLogger = function(logEntry) {
    // Send to external logging service
    fetch('/external-log-endpoint', {
        method: 'POST',
        body: JSON.stringify(logEntry)
    });
};
```

## Security Considerations

### Data Privacy
- **No Sensitive Data**: Logs avoid personal information
- **URL Sanitization**: External URLs logged safely
- **User Identification**: Only session IDs, no personal data

### Production Safety
- **Performance Guard**: Automatic disable when not needed
- **Error Handling**: Logging failures don't break functionality
- **Resource Limits**: Automatic cleanup prevents memory leaks

### Access Control
- **Admin Only**: Settings require manage_options capability
- **Nonce Protection**: AJAX requests properly secured
- **Capability Checks**: All operations verify permissions

## Conclusion

The OCHandyDude debug logging system provides comprehensive insights into booking form height issues and other integration problems. With multi-level logging, specialized booking form tracking, and detailed timing analysis, it enables effective troubleshooting while maintaining optimal production performance when disabled.
