[17-Aug-2025 07:59:39 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-content/plugins/invoiceninja/InvoiceNinja/WordPress/PostApi.php on line 212
[17-Aug-2025 08:00:01 UTC] OCHD SSO: Claims received for user 12: Array
(
    [sub] => fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea
    [address] => Array
        (
            [street_address] => 122 sosaevo
            [locality] => Pomoika
            [region] => CA
            [postal_code] => 66699
            [country] => US
        )

    [email_verified] => 1
    [phone] => ************
    [roles] => Array
        (
            [0] => offline_access
            [1] => uma_authorization
            [2] => default-roles-ochandydude
            [3] => customer
        )

    [name] => Se<PERSON>
    [preferred_username] => <EMAIL>
    [given_name] => Se<PERSON>
    [family_name] => Sosekas
    [email] => <EMAIL>
)

[17-Aug-2025 08:00:01 UTC] OCHD SSO: Found phone number in claim 'phone': ************
[17-Aug-2025 08:00:01 UTC] OCHD SSO: Phone number found: ************
[17-Aug-2025 08:00:01 UTC] OCHD SSO: Syncing given_name -> first_name: Sekas
[17-Aug-2025 08:00:01 UTC] OCHD SSO: Syncing family_name -> last_name: Sosekas
[17-Aug-2025 08:00:01 UTC] OCHD SSO: Syncing email -> billing_email: <EMAIL>
[17-Aug-2025 08:00:02 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-content/plugins/invoiceninja/InvoiceNinja/WordPress/PostApi.php on line 212
[17-Aug-2025 08:00:14 UTC] Invoice Ninja: Looking for client with id_number: fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea (user_id: 12)
[17-Aug-2025 08:00:14 UTC] Invoice Ninja: Client search response: Array
(
    [data] => Array
        (
            [0] => Array
                (
                    [id] => l4zbq2dprO
                    [user_id] => VolejRejNm
                    [assigned_user_id] => 
                    [name] => <EMAIL>
                    [website] => 
                    [private_notes] => 
                    [balance] => 225
                    [group_settings_id] => 
                    [paid_to_date] => 0
                    [payment_balance] => 0
                    [credit_balance] => 0
                    [last_login] => **********
                    [size_id] => 
                    [public_notes] => 
                    [client_hash] => kysyUHDovFb3iPbZP4wE2r10z3B34xCHe6xAtsUz
                    [address1] => 122 sosaevo
                    [address2] => 
                    [phone] => 
                    [city] => Pomoika
                    [state] => CA
                    [postal_code] => 66699
                    [country_id] => 840
                    [industry_id] => 
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [shipping_address1] => 122 sosaevo
                    [shipping_address2] => 
                    [shipping_city] => Pomoika
                    [shipping_state] => CA
                    [shipping_postal_code] => 66699
                    [shipping_country_id] => 
                    [settings] => Array
                        (
                            [entity] => App\Models\Client
                            [industry_id] => 
                            [size_id] => 
                            [currency_id] => 1
                        )

                    [is_deleted] => 
                    [vat_number] => 
                    [id_number] => fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea
                    [updated_at] => **********
                    [archived_at] => 0
                    [created_at] => **********
                    [display_name] => <EMAIL>
                    [number] => 0006
                    [has_valid_vat_number] => 
                    [is_tax_exempt] => 
                    [routing_id] => 
                    [tax_info] => Array
                        (
                        )

                    [classification] => 
                    [e_invoice] => Array
                        (
                        )

                    [contacts] => Array
                        (
                            [0] => Array
                                (
                                    [id] => l4zbq2dprO
                                    [first_name] => Sekas
                                    [last_name] => Sosekas
                                    [email] => <EMAIL>
                                    [created_at] => **********
                                    [updated_at] => **********
                                    [archived_at] => 0
                                    [is_primary] => 1
                                    [is_locked] => 
                                    [phone] => ************
                                    [custom_value1] => 
                                    [custom_value2] => 
                                    [custom_value3] => 
                                    [custom_value4] => 
                                    [contact_key] => SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                                    [send_email] => 1
                                    [last_login] => **********
                                    [password] => 
                                    [link] => https://billing.ochandydude.pro/client/key_login/SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                                )

                        )

                    [documents] => Array
                        (
                        )

                    [gateway_tokens] => Array
                        (
                        )

                    [locations] => Array
                        (
                        )

                )

        )

    [meta] => Array
        (
            [pagination] => Array
                (
                    [total] => 1
                    [count] => 1
                    [per_page] => 1
                    [current_page] => 1
                    [total_pages] => 1
                    [links] => Array
                        (
                        )

                )

        )

)

[17-Aug-2025 08:00:14 UTC] Invoice Ninja: Found client ID l4zbq2dprO for user fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea
[17-Aug-2025 08:00:14 UTC] Invoice Ninja: Client data: Array
(
    [id] => l4zbq2dprO
    [user_id] => VolejRejNm
    [assigned_user_id] => 
    [name] => <EMAIL>
    [website] => 
    [private_notes] => 
    [balance] => 225
    [group_settings_id] => 
    [paid_to_date] => 0
    [payment_balance] => 0
    [credit_balance] => 0
    [last_login] => **********
    [size_id] => 
    [public_notes] => 
    [client_hash] => kysyUHDovFb3iPbZP4wE2r10z3B34xCHe6xAtsUz
    [address1] => 122 sosaevo
    [address2] => 
    [phone] => 
    [city] => Pomoika
    [state] => CA
    [postal_code] => 66699
    [country_id] => 840
    [industry_id] => 
    [custom_value1] => 
    [custom_value2] => 
    [custom_value3] => 
    [custom_value4] => 
    [shipping_address1] => 122 sosaevo
    [shipping_address2] => 
    [shipping_city] => Pomoika
    [shipping_state] => CA
    [shipping_postal_code] => 66699
    [shipping_country_id] => 
    [settings] => Array
        (
            [entity] => App\Models\Client
            [industry_id] => 
            [size_id] => 
            [currency_id] => 1
        )

    [is_deleted] => 
    [vat_number] => 
    [id_number] => fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea
    [updated_at] => **********
    [archived_at] => 0
    [created_at] => **********
    [display_name] => <EMAIL>
    [number] => 0006
    [has_valid_vat_number] => 
    [is_tax_exempt] => 
    [routing_id] => 
    [tax_info] => Array
        (
        )

    [classification] => 
    [e_invoice] => Array
        (
        )

    [contacts] => Array
        (
            [0] => Array
                (
                    [id] => l4zbq2dprO
                    [first_name] => Sekas
                    [last_name] => Sosekas
                    [email] => <EMAIL>
                    [created_at] => **********
                    [updated_at] => **********
                    [archived_at] => 0
                    [is_primary] => 1
                    [is_locked] => 
                    [phone] => ************
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [contact_key] => SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                    [send_email] => 1
                    [last_login] => **********
                    [password] => 
                    [link] => https://billing.ochandydude.pro/client/key_login/SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                )

        )

    [documents] => Array
        (
        )

    [gateway_tokens] => Array
        (
        )

    [locations] => Array
        (
        )

)

[17-Aug-2025 08:00:14 UTC] Invoice Ninja: Retrieved 4 tasks for status: active
[17-Aug-2025 08:00:14 UTC] Invoice Ninja: Task filtering for active (all): Task WJxbojagwO: status='Ready to do', category='upcoming', requested='active', include=YES; Task k8mep2bMyJ: is_deleted/archived, requested='active', include=NO; Task yMYerEdOBQ: is_deleted/archived, requested='active', include=NO; Task 7LDdwRb1YK: status='Ready to do', category='upcoming', requested='active', include=YES
[17-Aug-2025 08:00:14 UTC] Invoice Ninja: Filtered 2 tasks from 4 total for status: active
[17-Aug-2025 08:00:27 UTC] Invoice Ninja: BOOKING HISTORY REQUEST RECEIVED for user 12
[17-Aug-2025 08:00:27 UTC] Invoice Ninja: Cleared cache for user 12
[17-Aug-2025 08:00:27 UTC] Invoice Ninja: Looking for client with id_number: fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea (user_id: 12)
[17-Aug-2025 08:00:27 UTC] Invoice Ninja: Client search response: Array
(
    [data] => Array
        (
            [0] => Array
                (
                    [id] => l4zbq2dprO
                    [user_id] => VolejRejNm
                    [assigned_user_id] => 
                    [name] => <EMAIL>
                    [website] => 
                    [private_notes] => 
                    [balance] => 225
                    [group_settings_id] => 
                    [paid_to_date] => 0
                    [payment_balance] => 0
                    [credit_balance] => 0
                    [last_login] => **********
                    [size_id] => 
                    [public_notes] => 
                    [client_hash] => kysyUHDovFb3iPbZP4wE2r10z3B34xCHe6xAtsUz
                    [address1] => 122 sosaevo
                    [address2] => 
                    [phone] => 
                    [city] => Pomoika
                    [state] => CA
                    [postal_code] => 66699
                    [country_id] => 840
                    [industry_id] => 
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [shipping_address1] => 122 sosaevo
                    [shipping_address2] => 
                    [shipping_city] => Pomoika
                    [shipping_state] => CA
                    [shipping_postal_code] => 66699
                    [shipping_country_id] => 
                    [settings] => Array
                        (
                            [entity] => App\Models\Client
                            [industry_id] => 
                            [size_id] => 
                            [currency_id] => 1
                        )

                    [is_deleted] => 
                    [vat_number] => 
                    [id_number] => fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea
                    [updated_at] => **********
                    [archived_at] => 0
                    [created_at] => **********
                    [display_name] => <EMAIL>
                    [number] => 0006
                    [has_valid_vat_number] => 
                    [is_tax_exempt] => 
                    [routing_id] => 
                    [tax_info] => Array
                        (
                        )

                    [classification] => 
                    [e_invoice] => Array
                        (
                        )

                    [contacts] => Array
                        (
                            [0] => Array
                                (
                                    [id] => l4zbq2dprO
                                    [first_name] => Sekas
                                    [last_name] => Sosekas
                                    [email] => <EMAIL>
                                    [created_at] => **********
                                    [updated_at] => **********
                                    [archived_at] => 0
                                    [is_primary] => 1
                                    [is_locked] => 
                                    [phone] => ************
                                    [custom_value1] => 
                                    [custom_value2] => 
                                    [custom_value3] => 
                                    [custom_value4] => 
                                    [contact_key] => SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                                    [send_email] => 1
                                    [last_login] => **********
                                    [password] => 
                                    [link] => https://billing.ochandydude.pro/client/key_login/SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                                )

                        )

                    [documents] => Array
                        (
                        )

                    [gateway_tokens] => Array
                        (
                        )

                    [locations] => Array
                        (
                        )

                )

        )

    [meta] => Array
        (
            [pagination] => Array
                (
                    [total] => 1
                    [count] => 1
                    [per_page] => 1
                    [current_page] => 1
                    [total_pages] => 1
                    [links] => Array
                        (
                        )

                )

        )

)

[17-Aug-2025 08:00:27 UTC] Invoice Ninja: Found client ID l4zbq2dprO for user fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea
[17-Aug-2025 08:00:27 UTC] Invoice Ninja: Client data: Array
(
    [id] => l4zbq2dprO
    [user_id] => VolejRejNm
    [assigned_user_id] => 
    [name] => <EMAIL>
    [website] => 
    [private_notes] => 
    [balance] => 225
    [group_settings_id] => 
    [paid_to_date] => 0
    [payment_balance] => 0
    [credit_balance] => 0
    [last_login] => **********
    [size_id] => 
    [public_notes] => 
    [client_hash] => kysyUHDovFb3iPbZP4wE2r10z3B34xCHe6xAtsUz
    [address1] => 122 sosaevo
    [address2] => 
    [phone] => 
    [city] => Pomoika
    [state] => CA
    [postal_code] => 66699
    [country_id] => 840
    [industry_id] => 
    [custom_value1] => 
    [custom_value2] => 
    [custom_value3] => 
    [custom_value4] => 
    [shipping_address1] => 122 sosaevo
    [shipping_address2] => 
    [shipping_city] => Pomoika
    [shipping_state] => CA
    [shipping_postal_code] => 66699
    [shipping_country_id] => 
    [settings] => Array
        (
            [entity] => App\Models\Client
            [industry_id] => 
            [size_id] => 
            [currency_id] => 1
        )

    [is_deleted] => 
    [vat_number] => 
    [id_number] => fce7cdfc-58a2-4eba-b9ae-a71ba62d61ea
    [updated_at] => **********
    [archived_at] => 0
    [created_at] => **********
    [display_name] => <EMAIL>
    [number] => 0006
    [has_valid_vat_number] => 
    [is_tax_exempt] => 
    [routing_id] => 
    [tax_info] => Array
        (
        )

    [classification] => 
    [e_invoice] => Array
        (
        )

    [contacts] => Array
        (
            [0] => Array
                (
                    [id] => l4zbq2dprO
                    [first_name] => Sekas
                    [last_name] => Sosekas
                    [email] => <EMAIL>
                    [created_at] => **********
                    [updated_at] => **********
                    [archived_at] => 0
                    [is_primary] => 1
                    [is_locked] => 
                    [phone] => ************
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [contact_key] => SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                    [send_email] => 1
                    [last_login] => **********
                    [password] => 
                    [link] => https://billing.ochandydude.pro/client/key_login/SPLQ3OdenHc9KS58hKY1pjNoRgVgoCdE
                )

        )

    [documents] => Array
        (
        )

    [gateway_tokens] => Array
        (
        )

    [locations] => Array
        (
        )

)

[17-Aug-2025 08:00:27 UTC] Invoice Ninja: Making tasks API request with params: Array
(
    [client_id] => l4zbq2dprO
    [per_page] => 100
    [include_deleted] => true
)

[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Retrieved 4 unfiltered tasks for client l4zbq2dprO
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Pagination - total: 4, current_page: 1, total_pages: 1
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Full task API response sample: Array
(
    [0] => Array
        (
            [id] => WJxbojagwO
            [user_id] => VolejRejNm
            [assigned_user_id] => 
            [number] => 0006
            [description] => Sosisku obrezat
            [duration] => 0
            [rate] => 55
            [created_at] => 1755146470
            [updated_at] => 1755146616
            [archived_at] => 0
            [invoice_id] => Opnel5aKBz
            [client_id] => l4zbq2dprO
            [project_id] => 
            [is_deleted] => 
            [time_log] => [[1755362700,1755366300]]
            [is_running] => 
            [custom_value1] => 
            [custom_value2] => 
            [custom_value3] => 
            [custom_value4] => 
            [status_id] => Wpmbk5ezJn
            [status_sort_order] => 0
            [is_date_based] => 
            [status_order] => 
            [date] => 2025-08-16
            [documents] => Array
                (
                )

        )

    [1] => Array
        (
            [id] => k8mep2bMyJ
            [user_id] => VolejRejNm
            [assigned_user_id] => 
            [number] => 0007
            [description] => 
            [duration] => 0
            [rate] => 0
            [created_at] => 1755148499
            [updated_at] => 1755148541
            [archived_at] => 1755148541
            [invoice_id] => 
            [client_id] => l4zbq2dprO
            [project_id] => 
            [is_deleted] => 1
            [time_log] => [[1755367200,1755370800]]
            [is_running] => 
            [custom_value1] => 
            [custom_value2] => 
            [custom_value3] => 
            [custom_value4] => 
            [status_id] => VolejRejNm
            [status_sort_order] => 0
            [is_date_based] => 
            [status_order] => 
            [date] => 2025-08-16
            [documents] => Array
                (
                )

        )

)

[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Invoice Opnel5aKBz has status_id '2' -> 'Overdue' -> 'Overdue'
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Invoice Opnel5aKBz due_date: 2025-08-14, current_time: 2025-08-17
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Task WJxbojagwO (#0006) has invoice Opnel5aKBz with status 'Overdue'
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Unfiltered task WJxbojagwO (#0006): status_name='Overdue', invoice_id=Opnel5aKBz, is_deleted=false, archived_at=0
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Status mappings not cached, fetching now...
[17-Aug-2025 08:00:28 UTC] Invoice Ninja API Error: Invalid JSON response
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Task statuses: Array
(
    [VolejRejNm] => Backlog
    [Wpmbk5ezJn] => Ready to do
    [Opnel5aKBz] => In progress
    [wMvbmOeYAl] => Done
)

[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Failed to fetch project statuses: WP_Error Object
(
    [errors] => Array
        (
            [invalid_json] => Array
                (
                    [0] => Invalid JSON response from API
                )

        )

    [error_data] => Array
        (
        )

    [additional_data:protected] => Array
        (
        )

)

[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Unfiltered task k8mep2bMyJ (#0007): status_name='Backlog', invoice_id=none, is_deleted=true, archived_at=1755148541
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Unfiltered task yMYerEdOBQ (#0009): status_name='Backlog', invoice_id=none, is_deleted=true, archived_at=1755148772
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Unfiltered task 7LDdwRb1YK (#0011): status_name='Ready to do', invoice_id=none, is_deleted=false, archived_at=0
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history - retrieved 4 total tasks
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: categorize_booking_history called with 4 tasks
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: First task fields: id, user_id, assigned_user_id, number, description, duration, rate, created_at, updated_at, archived_at, invoice_id, client_id, project_id, is_deleted, time_log, is_running, custom_value1, custom_value2, custom_value3, custom_value4, status_id, status_sort_order, is_date_based, status_order, date, documents, start_date, task_date, calculated_start_date, status_name
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: First task sample data: Array
(
    [id] => 7LDdwRb1YK
    [user_id] => VolejRejNm
    [assigned_user_id] => 
    [number] => 0011
    [description] => 
    [duration] => 0
    [rate] => 0
    [created_at] => 1755148786
    [updated_at] => 1755376021
    [archived_at] => 0
)

[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history task 7LDdwRb1YK: using existing status_name='Ready to do'
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history task 7LDdwRb1YK: status='Ready to do', category='upcoming'
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history task yMYerEdOBQ: is_deleted=true, archived_at=1755148772 -> cancelled
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history task k8mep2bMyJ: is_deleted=true, archived_at=1755148541 -> cancelled
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history task WJxbojagwO: using existing status_name='Overdue'
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history task WJxbojagwO: status='Overdue', category='completed'
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: FOUND INVOICE-BASED TASK WJxbojagwO with status 'Overdue' - categorizing as completed
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Booking history categorized - completed: 1, cancelled: 2, all: 3
[17-Aug-2025 08:00:28 UTC] Invoice Ninja: Completed tasks: #0006 (WJxbojagwO) status='Overdue'
[17-Aug-2025 08:00:57 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-content/plugins/invoiceninja/InvoiceNinja/WordPress/PostApi.php on line 212
[17-Aug-2025 08:01:03 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-content/plugins/invoiceninja/InvoiceNinja/WordPress/PostApi.php on line 212
[17-Aug-2025 08:01:13 UTC] OCHD SSO: Claims received for user 1: Array
(
    [sub] => 9e9d5df4-bfa0-4093-89f2-e37b42a7bba4
    [address] => Array
        (
            [street_address] => 1206 Quail Ridge
            [locality] => Irvine
            [region] => CA
            [postal_code] => 92603
            [country] => US
        )

    [email_verified] => 1
    [phone] => **********
    [roles] => Array
        (
            [0] => offline_access
            [1] => admin
            [2] => uma_authorization
            [3] => default-roles-ochandydude
            [4] => customer
        )

    [name] => Viacheslav Volkov
    [preferred_username] => <EMAIL>
    [given_name] => Viacheslav
    [family_name] => Volkov
    [email] => <EMAIL>
)

[17-Aug-2025 08:01:13 UTC] OCHD SSO: Found phone number in claim 'phone': **********
[17-Aug-2025 08:01:13 UTC] OCHD SSO: Phone number found: **********
[17-Aug-2025 08:01:13 UTC] OCHD SSO: Syncing given_name -> first_name: Viacheslav
[17-Aug-2025 08:01:13 UTC] OCHD SSO: Syncing family_name -> last_name: Volkov
[17-Aug-2025 08:01:13 UTC] OCHD SSO: Syncing email -> billing_email: <EMAIL>
[17-Aug-2025 08:01:15 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-content/plugins/invoiceninja/InvoiceNinja/WordPress/PostApi.php on line 212
[17-Aug-2025 08:01:31 UTC] Invoice Ninja: Looking for client with id_number: adm (user_id: 1)
[17-Aug-2025 08:01:32 UTC] Invoice Ninja: Client search response: Array
(
    [data] => Array
        (
            [0] => Array
                (
                    [id] => wMvbmOeYAl
                    [user_id] => VolejRejNm
                    [assigned_user_id] => 
                    [name] => adm
                    [website] => 
                    [private_notes] => Synced from WordPress (https://ochandydude.pro) on 13 August 2025 02:28
                    [balance] => 0
                    [group_settings_id] => 
                    [paid_to_date] => 0
                    [payment_balance] => 0
                    [credit_balance] => 0
                    [last_login] => 0
                    [size_id] => 
                    [public_notes] => 
                    [client_hash] => h4PqhKVTu8cS5q2ra6tUOKUsr8mFeP9diAbdgKrR
                    [address1] => 1206 Quail Ridge
                    [address2] => 
                    [phone] => 
                    [city] => Irvine
                    [state] => CA
                    [postal_code] => 92603
                    [country_id] => 840
                    [industry_id] => 
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [shipping_address1] => 
                    [shipping_address2] => 
                    [shipping_city] => 
                    [shipping_state] => 
                    [shipping_postal_code] => 
                    [shipping_country_id] => 
                    [settings] => Array
                        (
                            [currency_id] => 1
                        )

                    [is_deleted] => 
                    [vat_number] => 
                    [id_number] => adm
                    [updated_at] => **********
                    [archived_at] => 0
                    [created_at] => **********
                    [display_name] => adm
                    [number] => 0002
                    [has_valid_vat_number] => 
                    [is_tax_exempt] => 
                    [routing_id] => 
                    [tax_info] => Array
                        (
                        )

                    [classification] => 
                    [e_invoice] => Array
                        (
                        )

                    [contacts] => Array
                        (
                            [0] => Array
                                (
                                    [id] => wMvbmOeYAl
                                    [first_name] => Viacheslav
                                    [last_name] => Volkov
                                    [email] => <EMAIL>
                                    [created_at] => **********
                                    [updated_at] => **********
                                    [archived_at] => 0
                                    [is_primary] => 1
                                    [is_locked] => 
                                    [phone] => **********
                                    [custom_value1] => 
                                    [custom_value2] => 
                                    [custom_value3] => 
                                    [custom_value4] => 
                                    [contact_key] => HScTVP0uJSv35o31zyfjb1b2CecHiz07
                                    [send_email] => 1
                                    [last_login] => 0
                                    [password] => 
                                    [link] => https://billing.ochandydude.pro/client/key_login/HScTVP0uJSv35o31zyfjb1b2CecHiz07
                                )

                        )

                    [documents] => Array
                        (
                        )

                    [gateway_tokens] => Array
                        (
                        )

                    [locations] => Array
                        (
                        )

                )

        )

    [meta] => Array
        (
            [pagination] => Array
                (
                    [total] => 1
                    [count] => 1
                    [per_page] => 1
                    [current_page] => 1
                    [total_pages] => 1
                    [links] => Array
                        (
                        )

                )

        )

)

[17-Aug-2025 08:01:32 UTC] Invoice Ninja: Found client ID wMvbmOeYAl for user adm
[17-Aug-2025 08:01:32 UTC] Invoice Ninja: Client data: Array
(
    [id] => wMvbmOeYAl
    [user_id] => VolejRejNm
    [assigned_user_id] => 
    [name] => adm
    [website] => 
    [private_notes] => Synced from WordPress (https://ochandydude.pro) on 13 August 2025 02:28
    [balance] => 0
    [group_settings_id] => 
    [paid_to_date] => 0
    [payment_balance] => 0
    [credit_balance] => 0
    [last_login] => 0
    [size_id] => 
    [public_notes] => 
    [client_hash] => h4PqhKVTu8cS5q2ra6tUOKUsr8mFeP9diAbdgKrR
    [address1] => 1206 Quail Ridge
    [address2] => 
    [phone] => 
    [city] => Irvine
    [state] => CA
    [postal_code] => 92603
    [country_id] => 840
    [industry_id] => 
    [custom_value1] => 
    [custom_value2] => 
    [custom_value3] => 
    [custom_value4] => 
    [shipping_address1] => 
    [shipping_address2] => 
    [shipping_city] => 
    [shipping_state] => 
    [shipping_postal_code] => 
    [shipping_country_id] => 
    [settings] => Array
        (
            [currency_id] => 1
        )

    [is_deleted] => 
    [vat_number] => 
    [id_number] => adm
    [updated_at] => **********
    [archived_at] => 0
    [created_at] => **********
    [display_name] => adm
    [number] => 0002
    [has_valid_vat_number] => 
    [is_tax_exempt] => 
    [routing_id] => 
    [tax_info] => Array
        (
        )

    [classification] => 
    [e_invoice] => Array
        (
        )

    [contacts] => Array
        (
            [0] => Array
                (
                    [id] => wMvbmOeYAl
                    [first_name] => Viacheslav
                    [last_name] => Volkov
                    [email] => <EMAIL>
                    [created_at] => **********
                    [updated_at] => **********
                    [archived_at] => 0
                    [is_primary] => 1
                    [is_locked] => 
                    [phone] => **********
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [contact_key] => HScTVP0uJSv35o31zyfjb1b2CecHiz07
                    [send_email] => 1
                    [last_login] => 0
                    [password] => 
                    [link] => https://billing.ochandydude.pro/client/key_login/HScTVP0uJSv35o31zyfjb1b2CecHiz07
                )

        )

    [documents] => Array
        (
        )

    [gateway_tokens] => Array
        (
        )

    [locations] => Array
        (
        )

)

[17-Aug-2025 08:01:32 UTC] Invoice Ninja: Retrieved 3 tasks for status: active
[17-Aug-2025 08:01:32 UTC] Invoice Ninja: Unknown status 'Backlog' defaulting to 'upcoming'
[17-Aug-2025 08:01:32 UTC] Invoice Ninja: Task filtering for active (all): Task JX7ax9byv4: status='Ready to do', category='upcoming', requested='active', include=YES; Task kzPdy7aQro: is_deleted/archived, requested='active', include=NO; Task mxkazYeJ0P: status='Backlog', category='upcoming', requested='active', include=YES
[17-Aug-2025 08:01:32 UTC] Invoice Ninja: Filtered 2 tasks from 3 total for status: active
[17-Aug-2025 08:01:45 UTC] Invoice Ninja: BOOKING HISTORY REQUEST RECEIVED for user 1
[17-Aug-2025 08:01:45 UTC] Invoice Ninja: Cleared cache for user 1
[17-Aug-2025 08:01:45 UTC] Invoice Ninja: Looking for client with id_number: adm (user_id: 1)
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Client search response: Array
(
    [data] => Array
        (
            [0] => Array
                (
                    [id] => wMvbmOeYAl
                    [user_id] => VolejRejNm
                    [assigned_user_id] => 
                    [name] => adm
                    [website] => 
                    [private_notes] => Synced from WordPress (https://ochandydude.pro) on 13 August 2025 02:28
                    [balance] => 0
                    [group_settings_id] => 
                    [paid_to_date] => 0
                    [payment_balance] => 0
                    [credit_balance] => 0
                    [last_login] => 0
                    [size_id] => 
                    [public_notes] => 
                    [client_hash] => h4PqhKVTu8cS5q2ra6tUOKUsr8mFeP9diAbdgKrR
                    [address1] => 1206 Quail Ridge
                    [address2] => 
                    [phone] => 
                    [city] => Irvine
                    [state] => CA
                    [postal_code] => 92603
                    [country_id] => 840
                    [industry_id] => 
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [shipping_address1] => 
                    [shipping_address2] => 
                    [shipping_city] => 
                    [shipping_state] => 
                    [shipping_postal_code] => 
                    [shipping_country_id] => 
                    [settings] => Array
                        (
                            [currency_id] => 1
                        )

                    [is_deleted] => 
                    [vat_number] => 
                    [id_number] => adm
                    [updated_at] => **********
                    [archived_at] => 0
                    [created_at] => **********
                    [display_name] => adm
                    [number] => 0002
                    [has_valid_vat_number] => 
                    [is_tax_exempt] => 
                    [routing_id] => 
                    [tax_info] => Array
                        (
                        )

                    [classification] => 
                    [e_invoice] => Array
                        (
                        )

                    [contacts] => Array
                        (
                            [0] => Array
                                (
                                    [id] => wMvbmOeYAl
                                    [first_name] => Viacheslav
                                    [last_name] => Volkov
                                    [email] => <EMAIL>
                                    [created_at] => **********
                                    [updated_at] => **********
                                    [archived_at] => 0
                                    [is_primary] => 1
                                    [is_locked] => 
                                    [phone] => **********
                                    [custom_value1] => 
                                    [custom_value2] => 
                                    [custom_value3] => 
                                    [custom_value4] => 
                                    [contact_key] => HScTVP0uJSv35o31zyfjb1b2CecHiz07
                                    [send_email] => 1
                                    [last_login] => 0
                                    [password] => 
                                    [link] => https://billing.ochandydude.pro/client/key_login/HScTVP0uJSv35o31zyfjb1b2CecHiz07
                                )

                        )

                    [documents] => Array
                        (
                        )

                    [gateway_tokens] => Array
                        (
                        )

                    [locations] => Array
                        (
                        )

                )

        )

    [meta] => Array
        (
            [pagination] => Array
                (
                    [total] => 1
                    [count] => 1
                    [per_page] => 1
                    [current_page] => 1
                    [total_pages] => 1
                    [links] => Array
                        (
                        )

                )

        )

)

[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Found client ID wMvbmOeYAl for user adm
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Client data: Array
(
    [id] => wMvbmOeYAl
    [user_id] => VolejRejNm
    [assigned_user_id] => 
    [name] => adm
    [website] => 
    [private_notes] => Synced from WordPress (https://ochandydude.pro) on 13 August 2025 02:28
    [balance] => 0
    [group_settings_id] => 
    [paid_to_date] => 0
    [payment_balance] => 0
    [credit_balance] => 0
    [last_login] => 0
    [size_id] => 
    [public_notes] => 
    [client_hash] => h4PqhKVTu8cS5q2ra6tUOKUsr8mFeP9diAbdgKrR
    [address1] => 1206 Quail Ridge
    [address2] => 
    [phone] => 
    [city] => Irvine
    [state] => CA
    [postal_code] => 92603
    [country_id] => 840
    [industry_id] => 
    [custom_value1] => 
    [custom_value2] => 
    [custom_value3] => 
    [custom_value4] => 
    [shipping_address1] => 
    [shipping_address2] => 
    [shipping_city] => 
    [shipping_state] => 
    [shipping_postal_code] => 
    [shipping_country_id] => 
    [settings] => Array
        (
            [currency_id] => 1
        )

    [is_deleted] => 
    [vat_number] => 
    [id_number] => adm
    [updated_at] => **********
    [archived_at] => 0
    [created_at] => **********
    [display_name] => adm
    [number] => 0002
    [has_valid_vat_number] => 
    [is_tax_exempt] => 
    [routing_id] => 
    [tax_info] => Array
        (
        )

    [classification] => 
    [e_invoice] => Array
        (
        )

    [contacts] => Array
        (
            [0] => Array
                (
                    [id] => wMvbmOeYAl
                    [first_name] => Viacheslav
                    [last_name] => Volkov
                    [email] => <EMAIL>
                    [created_at] => **********
                    [updated_at] => **********
                    [archived_at] => 0
                    [is_primary] => 1
                    [is_locked] => 
                    [phone] => **********
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [contact_key] => HScTVP0uJSv35o31zyfjb1b2CecHiz07
                    [send_email] => 1
                    [last_login] => 0
                    [password] => 
                    [link] => https://billing.ochandydude.pro/client/key_login/HScTVP0uJSv35o31zyfjb1b2CecHiz07
                )

        )

    [documents] => Array
        (
        )

    [gateway_tokens] => Array
        (
        )

    [locations] => Array
        (
        )

)

[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Making tasks API request with params: Array
(
    [client_id] => wMvbmOeYAl
    [per_page] => 100
    [include_deleted] => true
)

[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Retrieved 3 unfiltered tasks for client wMvbmOeYAl
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Pagination - total: 3, current_page: 1, total_pages: 1
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Full task API response sample: Array
(
    [0] => Array
        (
            [id] => JX7ax9byv4
            [user_id] => VolejRejNm
            [assigned_user_id] => 
            [number] => 0012
            [description] => 
            [duration] => 0
            [rate] => 0
            [created_at] => 1755212959
            [updated_at] => 1755373657
            [archived_at] => 0
            [invoice_id] => 
            [client_id] => wMvbmOeYAl
            [project_id] => VolejRejNm
            [is_deleted] => 
            [time_log] => [[1755298800,1755313200]]
            [is_running] => 
            [custom_value1] => 
            [custom_value2] => 
            [custom_value3] => 
            [custom_value4] => 
            [status_id] => Wpmbk5ezJn
            [status_sort_order] => 0
            [is_date_based] => 
            [status_order] => 0
            [date] => 2025-08-15
            [documents] => Array
                (
                )

            [project] => Array
                (
                    [id] => VolejRejNm
                    [user_id] => VolejRejNm
                    [assigned_user_id] => 
                    [client_id] => wMvbmOeYAl
                    [name] => Jobs 1
                    [number] => 0001
                    [created_at] => 1755212922
                    [updated_at] => 1755373657
                    [archived_at] => 0
                    [is_deleted] => 
                    [task_rate] => 0
                    [due_date] => 
                    [private_notes] => 
                    [public_notes] => 
                    [budgeted_hours] => 0
                    [custom_value1] => 
                    [custom_value2] => 
                    [custom_value3] => 
                    [custom_value4] => 
                    [color] => 
                    [current_hours] => 4
                    [documents] => Array
                        (
                        )

                )

        )

    [1] => Array
        (
            [id] => kzPdy7aQro
            [user_id] => VolejRejNm
            [assigned_user_id] => 
            [number] => 0013
            [description] => sos
            [duration] => 0
            [rate] => 0
            [created_at] => 1755380071
            [updated_at] => 1755380321
            [archived_at] => 1755380322
            [invoice_id] => 
            [client_id] => wMvbmOeYAl
            [project_id] => 
            [is_deleted] => 1
            [time_log] => [[1756416862,1755380100,"",true],[1755380268,0]]
            [is_running] => 1
            [custom_value1] => 
            [custom_value2] => 
            [custom_value3] => 
            [custom_value4] => 
            [status_id] => VolejRejNm
            [status_sort_order] => 0
            [is_date_based] => 
            [status_order] => 
            [date] => 2025-08-28
            [documents] => Array
                (
                )

        )

)

[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Status mappings not cached, fetching now...
[17-Aug-2025 08:01:46 UTC] Invoice Ninja API Error: Invalid JSON response
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Task statuses: Array
(
    [VolejRejNm] => Backlog
    [Wpmbk5ezJn] => Ready to do
    [Opnel5aKBz] => In progress
    [wMvbmOeYAl] => Done
)

[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Failed to fetch project statuses: WP_Error Object
(
    [errors] => Array
        (
            [invalid_json] => Array
                (
                    [0] => Invalid JSON response from API
                )

        )

    [error_data] => Array
        (
        )

    [additional_data:protected] => Array
        (
        )

)

[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Unfiltered task JX7ax9byv4 (#0012): status_name='Ready to do', invoice_id=none, is_deleted=false, archived_at=0
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Unfiltered task kzPdy7aQro (#0013): status_name='Backlog', invoice_id=none, is_deleted=true, archived_at=1755380322
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Unfiltered task mxkazYeJ0P (#0014): status_name='Backlog', invoice_id=none, is_deleted=false, archived_at=0
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Booking history - retrieved 3 total tasks
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: categorize_booking_history called with 3 tasks
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: First task fields: id, user_id, assigned_user_id, number, description, duration, rate, created_at, updated_at, archived_at, invoice_id, client_id, project_id, is_deleted, time_log, is_running, custom_value1, custom_value2, custom_value3, custom_value4, status_id, status_sort_order, is_date_based, status_order, date, documents, start_date, task_date, calculated_start_date, status_name
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: First task sample data: Array
(
    [id] => kzPdy7aQro
    [user_id] => VolejRejNm
    [assigned_user_id] => 
    [number] => 0013
    [description] => sos
    [duration] => 0
    [rate] => 0
    [created_at] => 1755380071
    [updated_at] => 1755380321
    [archived_at] => 1755380322
)

[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Booking history task kzPdy7aQro: is_deleted=true, archived_at=1755380322 -> cancelled
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Booking history task mxkazYeJ0P: using existing status_name='Backlog'
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Unknown status 'Backlog' defaulting to 'upcoming'
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Booking history task mxkazYeJ0P: status='Backlog', category='upcoming'
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Booking history task JX7ax9byv4: using existing status_name='Ready to do'
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Booking history task JX7ax9byv4: status='Ready to do', category='upcoming'
[17-Aug-2025 08:01:46 UTC] Invoice Ninja: Booking history categorized - completed: 0, cancelled: 1, all: 1
