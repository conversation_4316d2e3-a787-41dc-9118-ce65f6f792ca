/**
 * OCHandyDude Invoice Ninja Integration Script
 * Handles dynamic loading of Invoice Ninja data into booking tabs
 */

class OCHDInvoiceNinjaIntegration {
    constructor() {
        this.cache = new Map();
        this.loadingStates = new Map();
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindEvents());
        } else {
            this.bindEvents();
        }
    }

    bindEvents() {
        // Listen for tab clicks to load data
        const tabLinks = document.querySelectorAll('.ochd-tab-link');
        tabLinks.forEach(tab => {
            tab.addEventListener('click', (e) => this.handleTabClick(e));
        });

        // Load initial data for the active tab
        this.loadInitialData();
    }

    handleTabClick(event) {
        const tabName = event.currentTarget.getAttribute('onclick')?.match(/openOchdTab\(event, '(.+)'\)/)?.[1];
        if (tabName) {
            setTimeout(() => this.loadTabData(tabName), 100); // Small delay to ensure tab is switched
        }
    }

    loadInitialData() {
        // Load data for the initially active tab
        const activeTab = document.querySelector('.ochd-tab-link.active');
        if (activeTab) {
            const tabName = activeTab.getAttribute('onclick')?.match(/openOchdTab\(event, '(.+)'\)/)?.[1];
            if (tabName) {
                this.loadTabData(tabName);
            }
        }
    }

    async loadTabData(tabName) {
        // Check if data is already loaded or loading
        if (this.cache.has(tabName) || this.loadingStates.get(tabName)) {
            return;
        }

        const tabContent = document.getElementById(tabName);
        if (!tabContent) return;

        // Set loading state
        this.loadingStates.set(tabName, true);
        this.showLoadingState(tabContent);

        try {
            let dataType;
            switch (tabName) {
                case 'upcoming':
                    dataType = 'upcoming_bookings';
                    break;
                case 'history':
                    dataType = 'booking_history';
                    break;
                case 'invoices':
                    dataType = 'invoices';
                    break;
                default:
                    return;
            }

            const data = await this.fetchInvoiceNinjaData(dataType);
            this.cache.set(tabName, data);
            this.renderTabContent(tabName, data);

        } catch (error) {
            console.error('Failed to load Invoice Ninja data:', error);
            this.showErrorState(tabContent, error.message);
        } finally {
            this.loadingStates.set(tabName, false);
        }
    }

    async fetchInvoiceNinjaData(dataType) {
        console.log(`Fetching Invoice Ninja data for: ${dataType}`);

        const formData = new FormData();
        formData.append('action', 'ochd_get_invoice_ninja_data');
        formData.append('data_type', dataType);
        formData.append('nonce', ochdInvoiceNinjaData.nonce);

        const response = await fetch(ochdInvoiceNinjaData.ajax_url, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log(`Invoice Ninja API response for ${dataType}:`, result);

        if (!result.success) {
            console.error(`Invoice Ninja API error for ${dataType}:`, result.data);
            throw new Error(result.data?.message || 'Failed to fetch data');
        }

        // Log data structure for debugging
        if (result.data) {
            console.log(`Data structure for ${dataType}:`, {
                projects: result.data.projects?.length || 0,
                tasks: result.data.tasks?.length || 0,
                invoices: result.data.invoices?.length || 0,
                sampleProject: result.data.projects?.[0],
                sampleTask: result.data.tasks?.[0],
                sampleInvoice: result.data.invoices?.[0]
            });
        }

        return result.data;
    }

    showLoadingState(container) {
        container.innerHTML = `
            <div class="ochd-loading-state">
                <div class="ochd-loading-spinner"></div>
                <p>Loading your data...</p>
            </div>
        `;
    }

    showErrorState(container, message) {
        container.innerHTML = `
            <div class="ochd-error-state">
                <div class="ochd-error-icon">⚠️</div>
                <h4>Unable to load data</h4>
                <p>${message}</p>
                <button class="ochd-retry-button" onclick="location.reload()">Retry</button>
            </div>
        `;
    }

    renderTabContent(tabName, data) {
        const container = document.getElementById(tabName);
        if (!container) return;

        switch (tabName) {
            case 'upcoming':
            case 'history':
                this.renderBookingsContent(container, data, tabName);
                break;
            case 'invoices':
                this.renderInvoicesContent(container, data);
                break;
        }
    }

    renderBookingsContent(container, data, tabName) {
        const title = tabName === 'upcoming' ? 'Upcoming Bookings' : 'Booking History';

        let html = `<h3>${title}</h3>`;

        if (tabName === 'history') {
            // Render booking history with subcategories
            html += this.renderBookingHistoryWithSubcategories(data);
        } else {
            // Render upcoming bookings with subcategories (all / confirmed / unconfirmed)
            html += this.renderUpcomingWithSubcategories(data);
        }

        container.innerHTML = html;

        // Add click handlers for expandable project groups and subcategory tabs
        this.bindProjectGroupHandlers(container);
        this.bindSubcategoryHandlers(container);
        this.bindExpansionHandlers(container);
        this.bindInvoiceStatusHandlers(container);
    }

    renderBookingHistoryWithSubcategories(data) {
        // Check if data has subcategories from backend
        if (data.all && data.completed && data.cancelled) {
            return `
                <div class="ochd-subcategory-tabs">
                    <button class="ochd-subcategory-tab active" data-subcategory="completed">Completed</button>
                    <button class="ochd-subcategory-tab" data-subcategory="cancelled">Cancelled</button>
                    <button class="ochd-subcategory-tab" data-subcategory="all">All</button>
                </div>
                <div class="ochd-subcategory-content">
                    <div id="subcategory-completed" class="ochd-subcategory-panel active">${this.renderBookingsList(data.completed.projects, data.completed.tasks)}</div>
                    <div id="subcategory-cancelled" class="ochd-subcategory-panel">${this.renderBookingsList(data.cancelled.projects, data.cancelled.tasks)}</div>
                    <div id="subcategory-all" class="ochd-subcategory-panel">${this.renderBookingsList(data.all.projects, data.all.tasks)}</div>
                </div>
            `;
        }
        // Fallback to normal rendering
        const { projects = [], tasks = [] } = data;
        return this.renderBookingsList(projects, tasks);
    }

    renderUpcomingWithSubcategories(data) {
        const { projects = [], tasks = [] } = data;
        // Build subcategories from server data if present, else categorize client-side
        let categorized = data.all && data.confirmed && data.unconfirmed ? data : null;
        if (!categorized) {
            categorized = { all: { projects: [], tasks: [] }, confirmed: { projects: [], tasks: [] }, unconfirmed: { projects: [], tasks: [] } };
            const isConfirmed = (name) => ['in progress','running','ready to go','ready to do'].includes((''+(name||'')).toLowerCase());
            const isUnconfirmed = (name) => ['backlog'].includes((''+(name||'')).toLowerCase());
            const isCompleted = (name) => ['done','invoiced','paid','completed','finished','closed','pending payment','overdue'].includes((''+(name||'')).toLowerCase());
            const isDeleted = (item) => item.is_deleted === true || (item.archived_at && item.archived_at !== 0);

            // Filter out deleted/cancelled and completed items from upcoming bookings
            const activeProjects = projects.filter(p => {
                const deleted = isDeleted(p);
                const completed = isCompleted(p.status_name);
                if (deleted) console.log(`Frontend: Filtering out deleted project ${p.id} from upcoming bookings`);
                if (completed) console.log(`Frontend: Filtering out completed project ${p.id} (status: ${p.status_name}) from upcoming bookings`);
                return !deleted && !completed;
            });
            const activeTasks = tasks.filter(t => {
                const deleted = isDeleted(t);
                const completed = isCompleted(t.status_name);
                if (deleted) console.log(`Frontend: Filtering out deleted task ${t.id} (${t.number}) from upcoming bookings`);
                if (completed) console.log(`Frontend: Filtering out completed task ${t.id} (${t.number}) with status '${t.status_name}' from upcoming bookings`);
                return !deleted && !completed;
            });

            categorized.all.projects = activeProjects;
            categorized.all.tasks = activeTasks;

            activeProjects.forEach(p => { const s=(p.status_name||'').toLowerCase(); if (isConfirmed(s)) categorized.confirmed.projects.push(p); else if (isUnconfirmed(s)) categorized.unconfirmed.projects.push(p); });
            activeTasks.forEach(t => {
                const s=(t.status_name||'').toLowerCase();
                if (isConfirmed(s)) {
                    categorized.confirmed.tasks.push(t);
                    console.log(`Frontend: Task ${t.id} (${t.number}) categorized as confirmed (status: ${s})`);
                } else if (isUnconfirmed(s)) {
                    categorized.unconfirmed.tasks.push(t);
                    console.log(`Frontend: Task ${t.id} (${t.number}) categorized as unconfirmed (status: ${s})`);
                }
            });
        }

        return `
            <div class="ochd-subcategory-tabs">
                <button class="ochd-subcategory-tab active" data-subcategory="confirmed">Confirmed</button>
                <button class="ochd-subcategory-tab" data-subcategory="unconfirmed">Unconfirmed</button>
                <button class="ochd-subcategory-tab" data-subcategory="all">All</button>
            </div>
            <div class="ochd-subcategory-content">
                <div id="subcategory-confirmed" class="ochd-subcategory-panel active">${this.renderBookingsList(categorized.confirmed.projects, categorized.confirmed.tasks)}</div>
                <div id="subcategory-unconfirmed" class="ochd-subcategory-panel">${this.renderBookingsList(categorized.unconfirmed.projects, categorized.unconfirmed.tasks)}</div>
                <div id="subcategory-all" class="ochd-subcategory-panel">${this.renderBookingsList(categorized.all.projects, categorized.all.tasks)}</div>
            </div>
        `;
    }

    renderBookingsList(projects, tasks) {
        // Ignore projects: render only tasks
        if (tasks.length === 0) {
            return `
                <div class="ochd-empty-state">
                    <div class="ochd-empty-icon">📋</div>
                    <p>No tasks found in this category.</p>
                </div>
            `;
        }

        let html = '<div class="ochd-bookings-list">';

        html += `
            <div class="ochd-standalone-tasks-section">
                <div class="ochd-section-header">
                    <h4>
                        <span class="ochd-section-icon">📋</span>
                        Tasks
                        <span class="ochd-section-count">${tasks.length}</span>
                    </h4>
                </div>
        `;
        tasks.forEach(task => {
            html += this.renderStandaloneTaskCard(task);
        });
        html += '</div>';

        html += '</div>';
        return html;
    }

    renderStandaloneTaskCard(task) {
        const status = this.determineStatus(task);
        const statusClass = this.getStatusClass(status);
        const statusLabel = this.getStatusLabel(status);
        const taskTime = this.calculateTaskTime(task);
        const startDate = this.getTaskStartDate(task);

        return `
            <div class="ochd-standalone-task-card">
                <div class="ochd-task-header">
                    <h4>${this.escapeHtml(task.description || 'Handyman Service')}</h4>
                    <span class="ochd-status-badge ${statusClass}">${statusLabel}</span>
                </div>
                ${startDate ? `<div class="ochd-task-start-time">
                    <span class="ochd-start-date-label">Start Date:</span>
                    <span class="ochd-start-date-value">${this.formatDate(startDate)}</span>
                </div>` : ''}
                <div class="ochd-task-meta">
                    ${taskTime > 0 ? `<span class="ochd-task-time">⏱ ${this.formatDuration(taskTime)}</span>` : ''}
                    ${task.rate ? `<span class="ochd-task-rate">💰 $${parseFloat(task.rate).toFixed(2)}/hr</span>` : ''}
                </div>
                <div class="ochd-task-meta-secondary">
                    ${task.created_at ? `<span class="ochd-task-created">📅 Created ${this.formatDate(task.created_at)}</span>` : ''}
                    ${task.updated_at ? `<span class="ochd-task-updated">🔄 Updated ${this.formatDate(task.updated_at)}</span>` : ''}
                </div>
            </div>
        `;
    }

    // Project rendering removed
    renderProjectGroup(project) { return ''; }

    renderTaskInvoiceExpansion(task) {
        return `
            <div class="ochd-expansion-panel" data-expansion="invoice" style="display: none;">
                <div class="ochd-expansion-header">
                    <h5>📄 Related Invoice</h5>
                </div>
                <div class="ochd-expansion-content">
                    <div class="ochd-loading-state">
                        <div class="ochd-loading-spinner"></div>
                        <p>Loading invoice details...</p>
                    </div>
                </div>
            </div>
        `;
    }

    renderInvoiceTaskExpansion(invoice) {
        return `
            <div class="ochd-expansion-panel" data-expansion="tasks" style="display: none;">
                <div class="ochd-expansion-header">
                    <h5>📋 Related Tasks</h5>
                </div>
                <div class="ochd-expansion-content">
                    <div class="ochd-loading-state">
                        <div class="ochd-loading-spinner"></div>
                        <p>Loading task details...</p>
                    </div>
                </div>
            </div>
        `;
    }

    renderTaskCard(task) {
        const status = this.determineStatus(task);
        const statusClass = this.getStatusClass(status);
        const statusLabel = this.getStatusLabel(status);
        const taskTime = this.calculateTaskTime(task);
        const hasInvoice = task.invoice_id && task.invoice_id !== '';
        const isCompleted = statusClass === 'completed'; // Fixed: use statusClass instead of status

        console.log(`OCHandyDude: Rendering task card for task ${task.id}:`, {
            status,
            statusClass,
            hasInvoice,
            isCompleted,
            invoice_id: task.invoice_id,
            willShowExpandButton: hasInvoice && isCompleted
        });

        return `
            <div class="ochd-task-card ${hasInvoice ? 'has-invoice' : ''}" data-task-id="${task.id}">
                <div class="ochd-task-header">
                    <h4>${this.escapeHtml(task.description || 'Handyman Service')}</h4>
                    <div class="ochd-task-header-actions">
                        <span class="ochd-status-badge ${statusClass}">${statusLabel}</span>
                        ${hasInvoice && isCompleted ? '<button class="ochd-expand-btn" data-expand="invoice" title="View Invoice Details">📄</button>' : ''}
                    </div>
                </div>
                <div class="ochd-task-meta">
                    ${task.created_at ? `<span class="ochd-task-created">Created: ${this.formatDate(task.created_at)}</span>` : ''}
                    ${task.updated_at ? `<span class="ochd-task-updated">Updated: ${this.formatDate(task.updated_at)}</span>` : ''}
                    ${taskTime > 0 ? `<span class="ochd-task-time">Time Logged: ${this.formatDuration(taskTime)}</span>` : ''}
                    ${task.rate ? `<span class="ochd-task-rate">Rate: $${parseFloat(task.rate).toFixed(2)}/hr</span>` : ''}
                </div>
                ${hasInvoice && isCompleted ? this.renderTaskInvoiceExpansion(task) : ''}
            </div>
        `;
    }

    // Project task item rendering removed
    renderProjectTaskItem(task) { return ''; }

    bindProjectGroupHandlers(container) {
        // Make toggleProjectGroup function globally available
        window.toggleProjectGroup = (projectId) => {
            const content = document.getElementById(projectId);
            const header = content.previousElementSibling;
            const icon = header.querySelector('.ochd-project-expand-icon');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.textContent = '▼';
                header.classList.add('expanded');
            } else {
                content.style.display = 'none';
                icon.textContent = '▶';
                header.classList.remove('expanded');
            }
        };
    }

    bindExpansionHandlers(container) {
        // Bind expand button click handlers
        const expandButtons = container.querySelectorAll('.ochd-expand-btn');
        console.log(`OCHandyDude: Found ${expandButtons.length} expand buttons`);

        expandButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log('OCHandyDude: Expand button clicked');

                const expandType = button.getAttribute('data-expand');
                const card = button.closest('.ochd-task-card, .ochd-invoice-card');
                const expansionPanel = card.querySelector(`[data-expansion="${expandType}"]`);

                console.log(`OCHandyDude: Expand type: ${expandType}, Card found: ${!!card}, Panel found: ${!!expansionPanel}`);

                if (expansionPanel) {
                    this.toggleExpansion(card, expansionPanel, expandType);
                } else {
                    console.error('OCHandyDude: Expansion panel not found for type:', expandType);
                }
            });
        });
    }

    toggleExpansion(card, expansionPanel, expandType) {
        const isExpanded = expansionPanel.style.display !== 'none';
        const expandButton = card.querySelector('.ochd-expand-btn');

        console.log(`OCHandyDude: Toggling expansion for ${expandType}:`, {
            isExpanded,
            expansionPanel,
            expandButton,
            card
        });

        if (isExpanded) {
            // Collapse
            console.log('OCHandyDude: Collapsing expansion panel');
            expansionPanel.style.display = 'none';
            expandButton.classList.remove('expanded');
            card.classList.remove('expanded');
        } else {
            // Expand
            console.log('OCHandyDude: Expanding panel');
            expansionPanel.style.display = 'block';
            expandButton.classList.add('expanded');
            card.classList.add('expanded');

            // Load content if not already loaded
            const content = expansionPanel.querySelector('.ochd-expansion-content');
            if (content && content.querySelector('.ochd-loading-state')) {
                console.log('OCHandyDude: Loading expansion content');
                this.loadExpansionContent(card, expansionPanel, expandType);
            } else {
                console.log('OCHandyDude: Content already loaded or no loading state found');
            }
        }
    }

    loadExpansionContent(card, expansionPanel, expandType) {
        const content = expansionPanel.querySelector('.ochd-expansion-content');

        if (expandType === 'invoice') {
            // Load invoice details for task
            const taskId = card.getAttribute('data-task-id');
            this.loadTaskInvoiceDetails(taskId, content);
        } else if (expandType === 'tasks') {
            // Load task details for invoice
            const invoiceId = card.getAttribute('data-invoice-id');
            this.loadInvoiceTaskDetails(invoiceId, content);
        }
    }

    loadTaskInvoiceDetails(taskId, contentContainer) {
        console.log(`OCHandyDude: Loading invoice details for task ${taskId}`);

        // Find the task data and get invoice information
        const task = this.findTaskById(taskId);
        console.log(`OCHandyDude: Found task:`, task);

        if (!task || !task.invoice_id) {
            console.log('OCHandyDude: No task or invoice_id found');
            contentContainer.innerHTML = '<p class="ochd-no-data">No invoice information available.</p>';
            return;
        }

        // Find the invoice data
        const invoice = this.findInvoiceById(task.invoice_id);
        console.log(`OCHandyDude: Found invoice:`, invoice);

        if (!invoice) {
            console.log('OCHandyDude: Invoice not found in cache');
            contentContainer.innerHTML = '<p class="ochd-no-data">Invoice details not found.</p>';
            return;
        }

        // Render invoice summary
        const statusClass = this.getInvoiceStatusClass(invoice.status_id);
        const statusLabel = this.getInvoiceStatusLabel(invoice.status_id);
        const isPaid = invoice.status_id === 6;

        contentContainer.innerHTML = `
            <div class="ochd-expansion-invoice-summary">
                <div class="ochd-expansion-row">
                    <span class="ochd-expansion-label">Invoice Number:</span>
                    <span class="ochd-expansion-value">#${invoice.number}</span>
                </div>
                <div class="ochd-expansion-row">
                    <span class="ochd-expansion-label">Amount:</span>
                    <span class="ochd-expansion-value">${this.formatCurrency(invoice.amount, invoice.client?.currency?.code)}</span>
                </div>
                <div class="ochd-expansion-row">
                    <span class="ochd-expansion-label">Status:</span>
                    <span class="ochd-expansion-value">
                        <span class="ochd-status-badge ${statusClass}">${statusLabel}</span>
                    </span>
                </div>
                <div class="ochd-expansion-row">
                    <span class="ochd-expansion-label">Date:</span>
                    <span class="ochd-expansion-value">${this.formatDate(invoice.date)}</span>
                </div>
                ${invoice.due_date ? `
                <div class="ochd-expansion-row">
                    <span class="ochd-expansion-label">Due Date:</span>
                    <span class="ochd-expansion-value">${this.formatDate(invoice.due_date)}</span>
                </div>
                ` : ''}
                ${invoice.balance > 0 ? `
                <div class="ochd-expansion-row">
                    <span class="ochd-expansion-label">Balance:</span>
                    <span class="ochd-expansion-value">${this.formatCurrency(invoice.balance, invoice.client?.currency?.code)}</span>
                </div>
                ` : ''}
            </div>
        `;
    }

    loadInvoiceTaskDetails(invoiceId, contentContainer) {
        console.log(`OCHandyDude: Loading task details for invoice ${invoiceId}`);

        // Find the invoice data
        const invoice = this.findInvoiceById(invoiceId);
        console.log(`OCHandyDude: Found invoice:`, invoice);

        if (!invoice || !invoice.line_items) {
            console.log('OCHandyDude: No invoice or line_items found');
            contentContainer.innerHTML = '<p class="ochd-no-data">No task information available.</p>';
            return;
        }

        // Filter task line items
        const taskItems = invoice.line_items.filter(item => item.type_id === 2); // Type 2 = Task
        console.log(`OCHandyDude: Found ${taskItems.length} task items in invoice`);

        if (taskItems.length === 0) {
            contentContainer.innerHTML = '<p class="ochd-no-data">No related tasks found.</p>';
            return;
        }

        // Render task summary
        let html = '<div class="ochd-expansion-task-summary">';
        taskItems.forEach(item => {
            html += `
                <div class="ochd-expansion-task-item">
                    <div class="ochd-expansion-row">
                        <span class="ochd-expansion-label">Task:</span>
                        <span class="ochd-expansion-value">${this.escapeHtml(item.product_key || item.notes || 'Handyman Service')}</span>
                    </div>
                    ${item.quantity ? `
                    <div class="ochd-expansion-row">
                        <span class="ochd-expansion-label">Hours:</span>
                        <span class="ochd-expansion-value">${parseFloat(item.quantity).toFixed(2)}</span>
                    </div>
                    ` : ''}
                    ${item.cost ? `
                    <div class="ochd-expansion-row">
                        <span class="ochd-expansion-label">Rate:</span>
                        <span class="ochd-expansion-value">${this.formatCurrency(item.cost, invoice.client?.currency?.code)}/hr</span>
                    </div>
                    ` : ''}
                    <div class="ochd-expansion-row">
                        <span class="ochd-expansion-label">Total:</span>
                        <span class="ochd-expansion-value">${this.formatCurrency(item.quantity * item.cost, invoice.client?.currency?.code)}</span>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        contentContainer.innerHTML = html;
    }

    findTaskById(taskId) {
        console.log(`OCHandyDude: Searching for task ${taskId} in cache:`, this.cache);

        // Search through cached data for the task
        for (const [tabName, data] of this.cache.entries()) {
            console.log(`OCHandyDude: Checking tab ${tabName}:`, data);

            if (data && data.tasks) {
                const task = data.tasks.find(task => task.id == taskId);
                if (task) {
                    console.log(`OCHandyDude: Found task ${taskId} in ${tabName}.tasks`);
                    return task;
                }
            }
            // Also check in subcategory data
            if (data && data.completed && data.completed.tasks) {
                const task = data.completed.tasks.find(task => task.id == taskId);
                if (task) {
                    console.log(`OCHandyDude: Found task ${taskId} in ${tabName}.completed.tasks`);
                    return task;
                }
            }
            if (data && data.cancelled && data.cancelled.tasks) {
                const task = data.cancelled.tasks.find(task => task.id == taskId);
                if (task) {
                    console.log(`OCHandyDude: Found task ${taskId} in ${tabName}.cancelled.tasks`);
                    return task;
                }
            }
            if (data && data.all && data.all.tasks) {
                const task = data.all.tasks.find(task => task.id == taskId);
                if (task) {
                    console.log(`OCHandyDude: Found task ${taskId} in ${tabName}.all.tasks`);
                    return task;
                }
            }
        }

        console.log(`OCHandyDude: Task ${taskId} not found in cache`);
        return null;
    }

    findInvoiceById(invoiceId) {
        console.log(`OCHandyDude: Searching for invoice ${invoiceId} in cache:`, this.cache);

        // Search through cached data for the invoice
        for (const [tabName, data] of this.cache.entries()) {
            console.log(`OCHandyDude: Checking tab ${tabName} for invoices:`, data);

            if (data && data.invoices) {
                const invoice = data.invoices.find(invoice => invoice.id == invoiceId);
                if (invoice) {
                    console.log(`OCHandyDude: Found invoice ${invoiceId} in ${tabName}.invoices`);
                    return invoice;
                }
            }
        }

        console.log(`OCHandyDude: Invoice ${invoiceId} not found in cache`);
        return null;
    }

    bindInvoiceStatusHandlers(container) {
        // Bind click handlers to invoice status badges
        const invoiceStatusBadges = container.querySelectorAll('.ochd-invoice-card .ochd-status-badge');
        console.log(`OCHandyDude: Found ${invoiceStatusBadges.length} invoice status badges`);

        invoiceStatusBadges.forEach(badge => {
            // Add visual indicator that badge is clickable
            badge.style.cursor = 'pointer';
            badge.title = 'Click to access billing portal';

            badge.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log('OCHandyDude: Invoice status badge clicked');

                // Get the billing portal URL
                this.openBillingPortal();
            });
        });
    }

    openBillingPortal() {
        // Try to get the billing portal URL from the custom button
        const billingPortalButton = document.querySelector('.ochd-custom-client-portal-button');

        if (billingPortalButton && billingPortalButton.href) {
            console.log('OCHandyDude: Opening billing portal:', billingPortalButton.href);
            window.open(billingPortalButton.href, '_blank', 'noopener,noreferrer');
        } else {
            // Fallback: try to find any Invoice Ninja portal link
            const fallbackButton = document.querySelector('a[href*="client/key_login"], a[href*="invoicing.co"]');

            if (fallbackButton && fallbackButton.href) {
                console.log('OCHandyDude: Opening fallback billing portal:', fallbackButton.href);
                window.open(fallbackButton.href, '_blank', 'noopener,noreferrer');
            } else {
                console.error('OCHandyDude: No billing portal URL found');
                alert('Billing portal is not available. Please contact support.');
            }
        }
    }

    bindSubcategoryHandlers(container) {
        const subcategoryTabs = container.querySelectorAll('.ochd-subcategory-tab');
        const subcategoryPanels = container.querySelectorAll('.ochd-subcategory-panel');

        subcategoryTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const targetSubcategory = e.target.getAttribute('data-subcategory');

                // Remove active class from all tabs and panels
                subcategoryTabs.forEach(t => t.classList.remove('active'));
                subcategoryPanels.forEach(p => p.classList.remove('active'));

                // Add active class to clicked tab and corresponding panel
                e.target.classList.add('active');
                const targetPanel = container.querySelector(`#subcategory-${targetSubcategory}`);
                if (targetPanel) {
                    targetPanel.classList.add('active');
                }
            });
        });
    }

    renderInvoicesContent(container, data) {
        const { invoices = [] } = data;

        let html = '<h3>Invoices</h3>';

        if (invoices.length === 0) {
            html += `
                <div class="ochd-empty-state">
                    <div class="ochd-empty-icon">🧾</div>
                    <p>No invoices found.</p>
                </div>
            `;
        } else {
            html += '<div class="ochd-invoices-section">';
            invoices.forEach(invoice => {
                html += this.renderInvoiceCard(invoice);
            });
            html += '</div>';
        }

        container.innerHTML = html;

        // Add click handlers for expansion buttons
        this.bindExpansionHandlers(container);

        // Add click handlers for invoice status badges
        this.bindInvoiceStatusHandlers(container);
    }

    renderInvoiceCard(invoice) {
        const statusClass = this.getInvoiceStatusClass(invoice.status_id);
        const isPaid = invoice.status_id === 6;
        const isOverdue = this.isInvoiceOverdue(invoice);
        const statusLabel = this.getInvoiceStatusLabel(invoice.status_id);
        const hasTasks = invoice.line_items && invoice.line_items.some(item => item.type_id === 2); // Type 2 = Task

        console.log(`OCHandyDude: Rendering invoice card for invoice ${invoice.id}:`, {
            statusClass,
            isPaid,
            isOverdue,
            statusLabel,
            hasTasks,
            line_items: invoice.line_items,
            willShowExpandButton: hasTasks
        });

        return `
            <div class="ochd-invoice-card ${isPaid ? 'paid' : 'unpaid'} ${isOverdue ? 'overdue' : ''} ${hasTasks ? 'has-tasks' : ''}" data-invoice-id="${invoice.id}">
                <div class="ochd-invoice-header">
                    <h4>Invoice #${invoice.number}</h4>
                    <div class="ochd-invoice-header-actions">
                        <span class="ochd-status-badge ${statusClass} ${isOverdue ? 'overdue' : ''}">${isOverdue && !isPaid ? 'Overdue' : statusLabel}</span>
                        ${hasTasks ? '<button class="ochd-expand-btn" data-expand="tasks" title="View Related Tasks">📋</button>' : ''}
                    </div>
                </div>
                <div class="ochd-invoice-details">
                    <div class="ochd-invoice-amount">
                        <strong>${this.formatCurrency(invoice.amount, invoice.client?.currency?.code)}</strong>
                        ${invoice.balance > 0 ? `<span class="ochd-balance">Balance: ${this.formatCurrency(invoice.balance, invoice.client?.currency?.code)}</span>` : ''}
                    </div>
                    <div class="ochd-invoice-dates">
                        <span>Date: ${this.formatDate(invoice.date)}</span>
                        ${invoice.due_date ? `<span class="${isOverdue && !isPaid ? 'overdue-date' : ''}">Due: ${this.formatDate(invoice.due_date)}</span>` : ''}
                    </div>
                </div>
                ${invoice.public_notes ? `<p class="ochd-invoice-notes">${this.escapeHtml(invoice.public_notes)}</p>` : ''}
                ${hasTasks ? this.renderInvoiceTaskExpansion(invoice) : ''}
            </div>
        `;
    }

    // Utility methods
    determineStatus(item) {
        // FIRST: Check deletion and archival status (this overrides status_name)
        if (item.is_deleted === true) {
            console.log(`Task ${item.id}: Detected as deleted (is_deleted=true), overriding status_name='${item.status_name}'`);
            return 'deleted';
        }

        if (item.archived_at && item.archived_at !== 0) {
            console.log(`Task ${item.id}: Detected as archived (archived_at=${item.archived_at}), overriding status_name='${item.status_name}'`);
            return 'archived';
        }

        // SECOND: Check for status_name (human-readable status from backend)
        if (item.hasOwnProperty('status_name') && item.status_name) {
            return item.status_name;
        }

        // THIRD: Fallback to status IDs
        if (item.hasOwnProperty('task_status_id') && item.task_status_id) {
            return item.task_status_id;
        }
        if (item.hasOwnProperty('project_status_id') && item.project_status_id) {
            return item.project_status_id;
        }
        if (item.hasOwnProperty('status_id') && item.status_id) {
            return item.status_id;
        }

        // Log all available fields for debugging
        console.warn('No status field found for item:', item.id, 'Available fields:', Object.keys(item));

        // Fallback: assume active
        return 'active';
    }

    getStatusClass(status) {
        // Handle Invoice Ninja string-based status IDs
        if (typeof status === 'string') {
            const normalizedStatus = ('' + status).toLowerCase().trim();

            // Map specific Invoice Ninja status names to CSS classes
            const statusMap = {
                // Confirmed/In-progress
                'ready to go': 'active',
                'ready to do': 'active',
                'in progress': 'active',
                'running': 'active',
                'pending': 'active',
                'open': 'active',
                'started': 'active',
                'active': 'active',

                // Unconfirmed/needs review
                'backlog': 'review',

                // Completed
                'paid': 'completed',
                'done': 'completed',
                'finished': 'completed',

                // Almost done / invoiced
                'invoiced': 'almost-done',
                'pending payment': 'almost-done',

                // Overdue
                'overdue': 'overdue',

                // Cancelled
                'deleted': 'cancelled',
                'canceled': 'cancelled',
                'cancelled': 'cancelled',
                'archived': 'cancelled',

                // Other
                'paused': 'paused',
                'draft': 'draft'
            };

            return statusMap[normalizedStatus] || 'active';
        }

        if (typeof status === 'number') {
            const classes = {
                1: 'draft',
                2: 'active',
                3: 'active',
                4: 'completed',
                5: 'cancelled',
                6: 'paused'
            };
            return classes[status] || 'active';
        }

        if (typeof status === 'boolean') {
            return status ? 'completed' : 'active';
        }

        // Default fallback
        return 'active';
    }

    getInvoiceStatusClass(statusId) {
        const classes = {
            1: 'draft',
            2: 'sent',
            3: 'viewed',
            4: 'approved',
            5: 'partial',
            6: 'paid',
            '-1': 'cancelled',
            '-2': 'reversed'
        };
        return classes[statusId] || 'unknown';
    }

    getStatusLabel(status) {
        // Handle Invoice Ninja string-based status IDs and names
        if (typeof status === 'string') {
            const normalizedStatus = ('' + status).toLowerCase().trim();

            // Map specific Invoice Ninja status names to display labels
            const statusMap = {
                // Confirmed (green)
                'ready to go': 'Active',
                'ready to do': 'Active',
                'in progress': 'Active',
                'running': 'Active',
                'active': 'Active',
                'pending': 'Active',
                'open': 'Active',
                'started': 'Active',

                // Review (orange)
                'backlog': 'Review',

                // Almost done (blue)
                'invoiced': 'Almost Done',
                'pending payment': 'Pending Payment',

                // Overdue (red/orange)
                'overdue': 'Overdue',

                // Done (green)
                'done': 'Done',
                'finished': 'Done',
                'paid': 'Paid',

                // Cancelled (red)
                'deleted': 'Cancelled',
                'canceled': 'Cancelled',
                'cancelled': 'Cancelled',
                'archived': 'Cancelled',

                // Other
                'paused': 'Paused',
                'draft': 'Draft'
            };

            return statusMap[normalizedStatus] || this.capitalizeFirst(status);
        }

        if (typeof status === 'number') {
            const labels = {
                1: 'Draft',
                2: 'Active',
                3: 'Active',
                4: 'Completed',
                5: 'Cancelled',
                6: 'Paused'
            };
            return labels[status] || 'Active';
        }

        if (typeof status === 'boolean') {
            return status ? 'Completed' : 'Active';
        }

        // Default fallback
        return 'Active';
    }

    capitalizeFirst(str) {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }

    getTaskStartDate(task) {
        // Priority order: start_date, date, calculated_start_date
        return task.start_date || task.date || task.calculated_start_date || task.task_date || null;
    }

    getInvoiceStatusLabel(statusId) {
        const labels = {
            1: 'Draft',
            2: 'Unpaid',
            3: 'Unpaid',
            4: 'Unpaid',
            5: 'Partially Paid',
            6: 'Paid',
            '-1': 'Cancelled',
            '-2': 'Reversed'
        };
        return labels[statusId] || 'Unknown';
    }

    isInvoiceOverdue(invoice) {
        if (invoice.status_id === 6) { // Already paid
            return false;
        }

        if (!invoice.due_date) {
            return false;
        }

        const dueDate = new Date(invoice.due_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time to start of day

        return dueDate < today;
    }

    formatCurrency(amount, currencyCode = 'USD') {
        // Invoice Ninja v5 stores amounts as actual dollar values, not cents
        const formatted = parseFloat(amount).toFixed(2);
        return `${currencyCode} ${formatted}`;
    }

    formatDate(dateString) {
        if (!dateString || dateString === null || dateString === 'null') return '';

        try {
            // Handle various date formats that Invoice Ninja might return
            let date;
            if (typeof dateString === 'string') {
                // Handle ISO date strings and other formats
                date = new Date(dateString);
            } else if (typeof dateString === 'number') {
                // Handle Unix timestamps
                date = new Date(dateString * 1000);
            } else {
                return '';
            }

            // Check if date is valid
            if (isNaN(date.getTime())) {
                console.warn('Invalid date string:', dateString);
                return '';
            }

            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Error parsing date:', dateString, error);
            return '';
        }
    }

    formatDuration(seconds) {
        if (!seconds || seconds <= 0) return '';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (hours > 0) {
            return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
        } else {
            return `${minutes}m`;
        }
    }

    calculateTaskTime(task) {
        // Invoice Ninja stores time logs as an array of time entries
        if (!task.time_log) return 0;

        let totalSeconds = 0;
        try {
            let timeLogs = task.time_log;

            // Handle string format
            if (typeof timeLogs === 'string') {
                timeLogs = JSON.parse(timeLogs);
            }

            if (Array.isArray(timeLogs)) {
                timeLogs.forEach(log => {
                    if (Array.isArray(log) && log.length >= 2) {
                        const startTime = log[0];
                        const endTime = log[1];

                        if (startTime && endTime && endTime > startTime) {
                            // Calculate duration in seconds
                            totalSeconds += endTime - startTime;
                        }
                    } else if (typeof log === 'object' && log.duration) {
                        // Alternative format where duration is directly provided
                        totalSeconds += log.duration;
                    }
                });
            } else if (typeof timeLogs === 'number') {
                // Direct seconds value
                totalSeconds = timeLogs;
            }
        } catch (e) {
            console.warn('Error parsing time log for task:', task.description, e);
            // Fallback: try to use any numeric value in the time_log field
            if (typeof task.time_log === 'number') {
                totalSeconds = task.time_log;
            }
        }

        return totalSeconds;
    }

    calculateTotalTime(tasks) {
        if (!Array.isArray(tasks)) return 0;

        return tasks.reduce((total, task) => {
            return total + this.calculateTaskTime(task);
        }, 0);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatProjectNumber(project) {
        if (!project.number) return 'No Number';

        // If it's just a number, format it nicely
        if (/^\d+$/.test(project.number)) {
            return `Project #${project.number}`;
        }

        // If it already has formatting, use as-is
        if (project.number.includes('#') || project.number.includes('-')) {
            return project.number;
        }

        // Default formatting
        return `#${project.number}`;
    }
    // Debug method for testing booking history
    debugBookingHistory() {
        console.log('Debugging booking history...');

        fetch(ochd_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'ochd_get_invoice_ninja_data',
                nonce: ochd_ajax.nonce,
                data_type: 'debug_booking_history'
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Debug booking history response:', data);
            if (data.success) {
                console.log(`Total tasks: ${data.data.total_tasks}`);
                console.log(`Completed tasks: ${data.data.completed_tasks}`);
                console.log(`Cancelled tasks: ${data.data.cancelled_tasks}`);
                console.log(`All history tasks: ${data.data.all_tasks}`);
                console.log('Sample tasks:', data.data.sample_tasks);
            }
        })
        .catch(error => {
            console.error('Debug booking history error:', error);
        });
    }
}

// Make debug functions globally available
window.debugInvoiceNinja = function() {
    if (window.invoiceNinjaIntegration) {
        window.invoiceNinjaIntegration.debugBookingHistory();
    } else {
        console.error('Invoice Ninja integration not initialized');
    }
}

window.debugAllTasks = function() {
    console.log('Debugging all tasks...');

    fetch(ochd_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'ochd_get_invoice_ninja_data',
            nonce: ochd_ajax.nonce,
            data_type: 'debug_all_tasks'
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('Debug all tasks response:', data);
        if (data.success) {
            console.log(`User ID: ${data.data.user_id}`);
            console.log(`Total tasks: ${data.data.total_tasks}`);
            console.table(data.data.tasks);

            // Look for invoiced tasks specifically
            const invoicedTasks = data.data.tasks.filter(task =>
                task.status_name && task.status_name.toLowerCase().includes('invoiced')
            );
            if (invoicedTasks.length > 0) {
                console.log('Found invoiced tasks:', invoicedTasks);
            } else {
                console.log('No invoiced tasks found');
            }
        }
    })
    .catch(error => {
        console.error('Debug all tasks error:', error);
    });
}

window.testInvoiceStatus = function(invoiceId = 'Opnel5aKBz') {
    console.log(`Testing invoice status detection for invoice: ${invoiceId}`);

    fetch(ochd_ajax.ajax_url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'ochd_get_invoice_ninja_data',
            nonce: ochd_ajax.nonce,
            data_type: 'test_invoice_status',
            invoice_id: invoiceId
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('Test invoice status response:', data);
        if (data.success) {
            console.log(`Invoice ID: ${data.data.invoice_id}`);
            console.log(`Detected Status: ${data.data.detected_status}`);
            console.log(`Message: ${data.data.message}`);
        }
    })
    .catch(error => {
        console.error('Test invoice status error:', error);
    });
}



// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof ochdInvoiceNinjaData !== 'undefined') {
        window.invoiceNinjaIntegration = new OCHDInvoiceNinjaIntegration();
    }
});
