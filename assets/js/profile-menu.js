/**
 * OCHandyDude Profile Menu Script
 * Handles profile menu dropdown functionality with proper event management
 * Note: This script works in conjunction with header-icon-manager.js for unified icon behavior
 */
document.addEventListener('DOMContentLoaded', function() {
    const profileMenu = document.querySelector('.ochd-profile-menu');
    if (!profileMenu) return;

    const profileIcon = profileMenu.querySelector('.ochd-profile-icon');
    if (!profileIcon) return;

    // Check if header-icon-manager is available and let it handle the events
    // This prevents conflicts between multiple event handlers
    if (window.OCHDIconManager) {
        console.log('Profile menu: Header Icon Manager detected, delegating event handling');
        return;
    }

    // Fallback event handling if header-icon-manager is not available
    console.log('Profile menu: Using fallback event handling');

    // Add click event listener for profile icon only
    profileIcon.addEventListener('click', function(event) {
        // Only handle clicks directly on the icon, not on menu links
        if (event.target.closest('.ochd-profile-dropdown a, .ochd-guest-login-button')) {
            // This is a menu link click, don't interfere
            return;
        }

        // Prevent default link behavior for icon clicks only
        event.preventDefault();
        // Stop event bubbling to prevent triggering parent element events
        event.stopPropagation();
        // Toggle the 'is-active' class to show/hide dropdown
        profileMenu.classList.toggle('is-active');
    });

    // Handle menu link clicks and outside clicks
    document.addEventListener('click', function(event) {
        // Check if click is on a menu link
        const isMenuLink = event.target.closest('.ochd-profile-dropdown a, .ochd-guest-login-button');
        if (isMenuLink && profileMenu.classList.contains('is-active')) {
            // Allow the link to navigate normally, then close menu
            console.log('Profile menu: Menu link clicked, allowing navigation');
            setTimeout(() => {
                profileMenu.classList.remove('is-active');
            }, 100);
            return; // Don't prevent default - allow navigation
        }

        // Close menu if clicking outside and menu is active
        if (profileMenu.classList.contains('is-active') && !profileMenu.contains(event.target)) {
            profileMenu.classList.remove('is-active');
        }
    });
});