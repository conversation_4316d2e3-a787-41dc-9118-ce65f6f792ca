/**
 * OCHandyDude Profile Menu Navigation Handler
 * Ensures proper navigation for all menu links
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Profile Menu Navigation: Initializing...');
    
    // Wait for other scripts to load
    setTimeout(initializeMenuNavigation, 200);
    
    function initializeMenuNavigation() {
        const profileMenu = document.querySelector('.ochd-profile-menu');
        if (!profileMenu) {
            console.log('Profile Menu Navigation: No profile menu found');
            return;
        }
        
        // Get all menu links
        const menuLinks = profileMenu.querySelectorAll('.ochd-menu-link, .ochd-profile-dropdown a, .ochd-guest-login-button');
        
        if (menuLinks.length === 0) {
            console.log('Profile Menu Navigation: No menu links found');
            return;
        }
        
        console.log(`Profile Menu Navigation: Found ${menuLinks.length} menu links`);
        
        // Add navigation handlers to each link
        menuLinks.forEach((link, index) => {
            setupLinkNavigation(link, index);
        });
        
        console.log('Profile Menu Navigation: All links initialized');
    }
    
    function setupLinkNavigation(link, index) {
        const linkText = link.textContent.trim();
        const linkHref = link.href;
        const linkAction = link.getAttribute('data-action') || 'navigate';
        
        console.log(`Setting up link ${index + 1}: "${linkText}" -> ${linkHref} (${linkAction})`);
        
        // Validate URL
        if (!linkHref || linkHref === window.location.href + '#' || linkHref.endsWith('#')) {
            console.error(`Invalid URL for "${linkText}": ${linkHref}`);
            fixInvalidUrl(link, linkText);
        }
        
        // Remove any existing click handlers by cloning
        const newLink = link.cloneNode(true);
        link.parentNode.replaceChild(newLink, link);
        
        // Add clean navigation handler
        newLink.addEventListener('click', function(event) {
            handleLinkClick(event, this, linkText, linkAction);
        });
    }
    
    function handleLinkClick(event, link, linkText, action) {
        console.log(`Profile Menu Navigation: "${linkText}" clicked`);
        
        // Validate URL again
        if (!link.href || link.href === window.location.href + '#' || link.href.endsWith('#')) {
            console.error(`Cannot navigate - invalid URL for "${linkText}": ${link.href}`);
            event.preventDefault();
            return;
        }
        
        // Close the menu first
        closeProfileMenu();
        
        // Handle different types of actions
        switch (action) {
            case 'external':
                // External links - let them open normally
                console.log(`Opening external link: ${link.href}`);
                break;
                
            case 'logout':
                // Logout - ensure it works
                console.log(`Logging out: ${link.href}`);
                // Add small delay to ensure menu closes
                setTimeout(() => {
                    window.location.href = link.href;
                }, 100);
                event.preventDefault();
                break;
                
            case 'login':
                // Login - navigate normally
                console.log(`Navigating to login: ${link.href}`);
                break;
                
            case 'navigate':
            default:
                // Regular navigation
                console.log(`Navigating to: ${link.href}`);
                
                // For internal links, add a small delay to ensure smooth transition
                if (link.href.includes(window.location.hostname)) {
                    setTimeout(() => {
                        window.location.href = link.href;
                    }, 100);
                    event.preventDefault();
                }
                break;
        }
    }
    
    function closeProfileMenu() {
        const profileMenu = document.querySelector('.ochd-profile-menu');
        if (profileMenu && profileMenu.classList.contains('is-active')) {
            profileMenu.classList.remove('is-active');
            console.log('Profile Menu Navigation: Menu closed');
        }
    }
    
    function fixInvalidUrl(link, linkText) {
        let newUrl = '';
        
        switch (linkText) {
            case 'My Profile':
                newUrl = '/my-profile/';
                break;
            case 'My Bookings':
                newUrl = '/my-bookings/';
                break;
            case 'Account Settings':
                newUrl = 'https://auth.ochandydude.pro/realms/ochandydude/account/';
                break;
            case 'Logout':
                newUrl = '/wp-login.php?action=logout&_wpnonce=' + getLogoutNonce();
                break;
            case 'Login / Register':
                newUrl = '/wp-login.php';
                break;
        }
        
        if (newUrl) {
            link.href = newUrl;
            console.log(`Fixed URL for "${linkText}": ${newUrl}`);
        }
    }
    
    function getLogoutNonce() {
        // Try to get logout nonce from existing logout links
        const existingLogoutLinks = document.querySelectorAll('a[href*="action=logout"]');
        for (let logoutLink of existingLogoutLinks) {
            const match = logoutLink.href.match(/_wpnonce=([^&]+)/);
            if (match) {
                return match[1];
            }
        }
        return '';
    }
    
    // Expose debug functions
    window.debugProfileNavigation = {
        testLink: function(index = 0) {
            const menuLinks = document.querySelectorAll('.ochd-menu-link, .ochd-profile-dropdown a, .ochd-guest-login-button');
            if (menuLinks[index]) {
                const link = menuLinks[index];
                console.log(`Testing link: "${link.textContent.trim()}" -> ${link.href}`);
                link.click();
            }
        },
        
        checkUrls: function() {
            const menuLinks = document.querySelectorAll('.ochd-menu-link, .ochd-profile-dropdown a, .ochd-guest-login-button');
            menuLinks.forEach((link, index) => {
                console.log(`Link ${index + 1}: "${link.textContent.trim()}" -> ${link.href}`);
            });
        },
        
        forceNavigate: function(url) {
            closeProfileMenu();
            setTimeout(() => {
                window.location.href = url;
            }, 100);
        }
    };
    
    console.log('Profile Menu Navigation: Debug functions available via window.debugProfileNavigation');
});

// Also add a fallback handler for any missed clicks
document.addEventListener('click', function(event) {
    const menuLink = event.target.closest('.ochd-profile-dropdown a, .ochd-guest-login-button');
    if (menuLink) {
        // This is a fallback - should not normally trigger if main handler works
        console.log('Profile Menu Navigation: Fallback handler triggered for:', menuLink.textContent.trim());
        
        // Ensure menu closes
        const profileMenu = document.querySelector('.ochd-profile-menu');
        if (profileMenu) {
            setTimeout(() => {
                profileMenu.classList.remove('is-active');
            }, 50);
        }
    }
});
