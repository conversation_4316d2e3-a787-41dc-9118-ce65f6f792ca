/**
 * OCHandyDude Smart Booking Wrapper Script v14.0 (Enhanced with Comprehensive Debug Logging)
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize debug logging
    const logger = window.OCHDDebugLogger;

    if (logger && logger.enabled) {
        logger.log('Booking form script initialized', 'BookingForm', 'info', {
            viewport: window.innerWidth + 'x' + window.innerHeight,
            userAgent: navigator.userAgent,
            timestamp: Date.now()
        });
    }

    // Start performance timer
    if (logger) logger.startTimer('booking-form-init');

    // Override Easy!Appointments iframe resizing to prevent double scrolling
    // TEMPORARILY DISABLED to fix height truncation issue
    // overrideEasyAppointmentsResizing();

    if (logger) {
        logger.logIframeEvent('Height override system temporarily disabled', {
            reason: 'Investigating height truncation at 1-second mark',
            timestamp: Date.now()
        });
    }

    // Alternative: Smart height management without iframe cloning
    setTimeout(() => {
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        if (iframe) {
            if (logger) {
                logger.logIframeEvent('Applying smart height management', {
                    iframeId: iframe.id,
                    currentHeight: iframe.style.height,
                    timestamp: Date.now()
                });
            }

            // Enable dynamic height with reasonable constraints
            const isMobile = window.innerWidth <= 768;
            const maxHeight = 1200; // Prevent excessive white space
            const minHeight = isMobile ? 600 : 650; // Responsive minimum

            iframe.style.height = 'auto';
            iframe.style.minHeight = minHeight + 'px';
            iframe.style.maxHeight = maxHeight + 'px';
            iframe.style.width = '100%';
            iframe.style.border = 'none';

            // Center the iframe and remove spacing
            iframe.style.margin = '0 auto';
            iframe.style.padding = '0';
            iframe.style.display = 'block';

            if (logger) {
                logger.logHeightChange(
                    iframe.style.height || 'auto',
                    'auto (dynamic with constraints)',
                    'Dynamic height management applied'
                );
            }
        }
    }, 500); // Apply after 500ms instead of 1000ms

    function overrideEasyAppointmentsResizing() {
        const logger = window.OCHDDebugLogger;

        if (logger) {
            logger.logIframeEvent('Starting Easy!Appointments override process', {
                timestamp: Date.now(),
                delayMs: 1000
            });
        }

        // Wait for Easy!Appointments script to load
        setTimeout(() => {
            const iframe = document.querySelector('.easyappointments-iframe');

            if (!iframe) {
                if (logger) {
                    logger.logIframeEvent('No Easy!Appointments iframe found', {
                        selector: '.easyappointments-iframe',
                        allIframes: document.querySelectorAll('iframe').length
                    });
                }
                return;
            }

            if (logger) {
                logger.logIframeEvent('Easy!Appointments iframe found, starting override', {
                    iframeId: iframe.id,
                    currentHeight: iframe.style.height || iframe.getAttribute('height'),
                    currentWidth: iframe.style.width || iframe.getAttribute('width'),
                    src: iframe.src
                });
            }

            // Store current iframe properties before cloning
            const currentHeight = iframe.style.height || iframe.getAttribute('height');
            const currentSrc = iframe.src;
            const currentId = iframe.id;
            const currentClasses = iframe.className;

            if (logger) {
                logger.logIframeEvent('Storing iframe properties before cloning', {
                    currentHeight: currentHeight,
                    currentSrc: currentSrc,
                    currentId: currentId,
                    currentClasses: currentClasses
                });
            }

            // Remove any existing resize watchers by cloning the iframe
            const newIframe = iframe.cloneNode(true);

            // Preserve critical properties that might be lost during cloning
            newIframe.style.height = currentHeight;
            newIframe.style.minHeight = '800px';
            newIframe.style.maxHeight = 'none';
            newIframe.style.width = '100%';
            newIframe.style.border = 'none';
            newIframe.style.display = 'block';

            iframe.parentNode.replaceChild(newIframe, iframe);

            if (logger) {
                logger.logIframeEvent('Iframe cloned with preserved properties', {
                    originalId: iframe.id,
                    newId: newIframe.id,
                    preservedHeight: newIframe.style.height,
                    preservedMinHeight: newIframe.style.minHeight
                });
            }

            // Apply our own height constraints (but don't override the preserved height)
            applyHeightConstraints(newIframe);

            // Prevent Easy!Appointments from overriding our height
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        if (logger) {
                            logger.logHeightChange(
                                mutation.oldValue || 'unknown',
                                newIframe.style.cssText,
                                'MutationObserver detected style change'
                            );
                        }
                        applyHeightConstraints(newIframe);
                    }
                });
            });

            observer.observe(newIframe, { attributes: true, attributeFilter: ['style'] });

            if (logger) {
                logger.logIframeEvent('MutationObserver attached to prevent height overrides', {
                    observing: 'style attribute changes'
                });
            }

        }, 1000);
    }

    function applyHeightConstraints(iframe) {
        const logger = window.OCHDDebugLogger;

        if (!iframe) {
            if (logger) {
                logger.logBookingHeight('applyHeightConstraints called with null iframe', {
                    error: 'No iframe provided'
                });
            }
            return;
        }

        const isMobile = window.innerWidth <= 768;
        const isSmallMobile = window.innerWidth <= 480;
        const isVerySmallMobile = window.innerWidth <= 360;
        const isLandscape = window.innerWidth > window.innerHeight;

        const deviceInfo = {
            viewport: window.innerWidth + 'x' + window.innerHeight,
            isMobile: isMobile,
            isSmallMobile: isSmallMobile,
            isVerySmallMobile: isVerySmallMobile,
            isLandscape: isLandscape,
            devicePixelRatio: window.devicePixelRatio || 1
        };

        if (logger) {
            logger.logBookingHeight('Applying height constraints', deviceInfo);
        }

        let heightCalc;

        const oldHeight = iframe.style.height;
        const oldMaxHeight = iframe.style.maxHeight;

        // Check if iframe is using dynamic height (auto) or has reasonable constraints
        const isDynamicHeight = oldHeight === 'auto' || oldHeight === '';
        const currentHeightPx = parseInt(oldHeight);
        const hasOptimalHeight = isDynamicHeight || (currentHeightPx >= 600 && currentHeightPx <= 1200);

        if (logger) {
            logger.logBookingHeight('Checking current height before applying constraints', {
                oldHeight: oldHeight,
                currentHeightPx: currentHeightPx,
                hasOptimalHeight: hasOptimalHeight,
                willPreserve: hasOptimalHeight
            });
        }

        if (hasOptimalHeight) {
            // Preserve dynamic height or optimal height, ensure proper constraints and centering
            iframe.style.maxHeight = '1200px';
            iframe.style.minHeight = '600px';
            iframe.style.margin = '0 auto';
            iframe.style.display = 'block';

            if (logger) {
                logger.logBookingHeight('Preserved optimal height with proper constraints and centering', {
                    preservedHeight: oldHeight,
                    newMaxHeight: '1200px',
                    newMinHeight: '600px',
                    margin: '0 auto'
                });
            }
            return; // Don't apply additional constraints if we have optimal setup
        }

        if (isMobile) {
            // Mobile: Use dynamic height with device-specific constraints
            let mobileMinHeight, mobileMaxHeight;

            if (isVerySmallMobile) {
                mobileMinHeight = '600px';
                mobileMaxHeight = '900px';
            } else if (isSmallMobile) {
                mobileMinHeight = '650px';
                mobileMaxHeight = '1000px';
            } else {
                mobileMinHeight = '700px';
                mobileMaxHeight = '1100px';
            }

            iframe.style.height = 'auto';
            iframe.style.maxHeight = mobileMaxHeight;
            iframe.style.minHeight = mobileMinHeight;
            iframe.style.margin = '0 auto';
            iframe.style.display = 'block';
            iframe.style.overflowY = 'auto';
            iframe.style.overflowX = 'hidden';

            if (logger) {
                logger.logBookingHeight('Applied mobile dynamic height constraints', {
                    deviceType: isVerySmallMobile ? 'very-small-mobile' :
                               isSmallMobile ? 'small-mobile' :
                               isLandscape ? 'mobile-landscape' : 'mobile',
                    oldHeight: oldHeight,
                    newHeight: 'auto',
                    oldMaxHeight: oldMaxHeight,
                    newMaxHeight: mobileMaxHeight,
                    newMinHeight: mobileMinHeight,
                    margin: '0 auto',
                    overflowY: 'auto',
                    overflowX: 'hidden'
                });
            }
        } else {
            // Desktop: Use dynamic height with reasonable constraints
            iframe.style.height = 'auto';
            iframe.style.maxHeight = '1200px'; // Prevent excessive white space
            iframe.style.minHeight = '650px';
            iframe.style.margin = '0 auto';
            iframe.style.display = 'block';

            if (logger) {
                logger.logBookingHeight('Applied desktop dynamic height constraints', {
                    deviceType: 'desktop',
                    oldHeight: oldHeight,
                    newHeight: 'auto',
                    oldMaxHeight: oldMaxHeight,
                    newMaxHeight: '1200px',
                    newMinHeight: '650px',
                    margin: '0 auto'
                });
            }
        }

        // Log final computed styles
        if (logger) {
            const computedStyle = window.getComputedStyle(iframe);
            logger.logBookingHeight('Final computed iframe styles', {
                computedHeight: computedStyle.height,
                computedMaxHeight: computedStyle.maxHeight,
                computedMinHeight: computedStyle.minHeight,
                computedWidth: computedStyle.width,
                scrollHeight: iframe.scrollHeight || 'unavailable',
                clientHeight: iframe.clientHeight || 'unavailable',
                offsetHeight: iframe.offsetHeight || 'unavailable'
            });
        }
    }

    // Apply constraints on window resize with debouncing and logging
    let resizeTimeout;
    window.addEventListener('resize', () => {
        const logger = window.OCHDDebugLogger;

        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            const iframe = document.querySelector('.easyappointments-iframe');

            if (logger) {
                logger.logBookingHeight('Window resize detected', {
                    newViewport: window.innerWidth + 'x' + window.innerHeight,
                    iframeFound: !!iframe,
                    timestamp: Date.now()
                });
            }

            if (iframe) {
                if (logger) logger.startTimer('resize-height-calculation');
                applyHeightConstraints(iframe);
                if (logger) logger.endTimer('resize-height-calculation', 'Height recalculation on resize');
            }
        }, 250); // Debounce resize events
    });
    const guestButton = document.getElementById('ochd-continue-as-guest');
    const guestOverlay = document.getElementById('ochd-guest-overlay');
    if (guestButton) {
        guestButton.addEventListener('click', function(e) {
            e.preventDefault();
            guestOverlay.style.opacity = '0';
            setTimeout(() => { guestOverlay.style.display = 'none'; }, 300);
        });
    }

    window.addEventListener('message', (event) => {
        const wrapper = document.querySelector('.ochd-booking-form-wrapper');
        if (!wrapper) return;
        const eaIframe = wrapper.querySelector('iframe.easyappointments-iframe');

        if (!eaIframe || event.source !== eaIframe.contentWindow) { return; }

        const data = event.data;

        // Handle ready message from iframe
        if (data && data.source === 'OchdIframe' && data.status === 'ready') {
            const logger = window.OCHDDebugLogger;

            if (logger) {
                logger.logIframeEvent('Received ready message from iframe', {
                    data: data,
                    ochdBookingDataAvailable: typeof ochdBookingData !== 'undefined'
                });
            }

            if (typeof ochdBookingData === 'undefined') {
                console.error('ochdBookingData not available in parent window');
                if (logger) {
                    logger.logError('ochdBookingData not available for auto-fill', new Error('ochdBookingData undefined'));
                }
                return;
            }

            console.log('Sending data to iframe:', ochdBookingData);
            if (logger) {
                logger.logIframeEvent('Sending auto-fill data to iframe', {
                    isLoggedIn: ochdBookingData.isLoggedIn,
                    hasUserData: !!ochdBookingData.userData,
                    userData: ochdBookingData.userData
                });
            }
            const targetOrigin = new URL(eaIframe.src).origin;
            const message = {
                source: 'OCHandyDudeParent',
                isLoggedIn: ochdBookingData.isLoggedIn,
                loginUrl: ochdBookingData.login_url,
                ajaxUrl: ochdBookingData.ajax_url,
                userData: ochdBookingData.userData || null,
                nonce: ochdBookingData.nonce
            };

            eaIframe.contentWindow.postMessage(message, targetOrigin);

            if (logger) {
                logger.logIframeEvent('Auto-fill message sent to iframe', {
                    targetOrigin: targetOrigin,
                    messageSize: JSON.stringify(message).length,
                    success: true
                });
            }
        }

        // CRITICAL: Handle redirect request from iframe
        else if (data && data.source === 'OchdIframe' && data.action === 'redirect' && data.url) {
            console.log('Redirect request received from iframe:', data.url);

            try {
                // Perform redirect in parent window
                window.location.href = data.url;
            } catch (error) {
                console.error('Parent window redirect failed:', error);
                // Fallback: try location.replace
                try {
                    window.location.replace(data.url);
                } catch (replaceError) {
                    console.error('Location.replace also failed:', replaceError);
                    // Final fallback: open in new window
                    window.open(data.url, '_self');
                }
            }
        }
    });
});