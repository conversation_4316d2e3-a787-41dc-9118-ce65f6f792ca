/**
 * OCHandyDude Height Fix Test Script
 * Tests the fixes for the 1-second height truncation issue
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run if debug logging is enabled
    if (!window.OCHDDebugLogger || !window.OCHDDebugLogger.enabled) {
        return;
    }
    
    const logger = window.OCHDDebugLogger;
    
    logger.info('Height fix test script initialized', 'HeightFixTest');
    
    // Monitor iframe height changes over time
    monitorIframeHeight();
    
    function monitorIframeHeight() {
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (!iframe) {
            logger.warn('No booking iframe found for height monitoring', 'HeightFixTest');
            return;
        }
        
        logger.info('Starting iframe height monitoring', 'HeightFixTest', {
            iframeId: iframe.id,
            initialHeight: iframe.style.height,
            initialMinHeight: iframe.style.minHeight,
            initialMaxHeight: iframe.style.maxHeight
        });
        
        let previousHeight = iframe.style.height;
        let checkCount = 0;
        const maxChecks = 20; // Monitor for 20 seconds
        
        const heightMonitor = setInterval(function() {
            checkCount++;
            const currentHeight = iframe.style.height;
            const computedStyle = window.getComputedStyle(iframe);
            
            // Log height status every second
            logger.info(`Height check ${checkCount}/${maxChecks}`, 'HeightFixTest', {
                timeElapsed: checkCount + 's',
                currentHeight: currentHeight,
                computedHeight: computedStyle.height,
                minHeight: computedStyle.minHeight,
                maxHeight: computedStyle.maxHeight,
                heightChanged: currentHeight !== previousHeight
            });
            
            // Check for height changes
            if (currentHeight !== previousHeight) {
                logger.warn('Height change detected!', 'HeightFixTest', {
                    timeElapsed: checkCount + 's',
                    oldHeight: previousHeight,
                    newHeight: currentHeight,
                    changeType: parseInt(currentHeight) < parseInt(previousHeight) ? 'TRUNCATION' : 'EXPANSION'
                });
                
                // If height was truncated, try to restore it (less aggressive)
                if (parseInt(currentHeight) < 800) {
                    logger.error('Height truncation detected - attempting to restore', 'HeightFixTest', {
                        truncatedHeight: currentHeight,
                        restoringTo: '1000px'
                    });

                    iframe.style.height = '1000px';
                    iframe.style.minHeight = '800px';
                    iframe.style.maxHeight = 'none';
                    iframe.style.margin = '0';
                    iframe.style.padding = '0';
                }
                
                previousHeight = currentHeight;
            }
            
            // Special attention to the critical 1-second mark
            if (checkCount === 1) {
                logger.warn('CRITICAL: 1-second mark reached - monitoring for truncation', 'HeightFixTest', {
                    heightAt1Second: currentHeight,
                    computedHeightAt1Second: computedStyle.height,
                    expectedIssueTime: true
                });
            }
            
            // Stop monitoring after maxChecks
            if (checkCount >= maxChecks) {
                clearInterval(heightMonitor);
                logger.info('Height monitoring completed', 'HeightFixTest', {
                    totalChecks: checkCount,
                    finalHeight: currentHeight,
                    finalComputedHeight: computedStyle.height,
                    truncationDetected: parseInt(currentHeight) < 1200,
                    hasExcessiveHeight: parseInt(currentHeight) > 1600,
                    optimalHeightRange: '1200px - 1600px'
                });
            }
        }, 1000); // Check every second
        
        // Also monitor for style attribute changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    logger.info('Style attribute changed on iframe', 'HeightFixTest', {
                        oldValue: mutation.oldValue,
                        newValue: iframe.style.cssText,
                        timeElapsed: checkCount + 's'
                    });
                }
            });
        });
        
        observer.observe(iframe, {
            attributes: true,
            attributeFilter: ['style'],
            attributeOldValue: true
        });
        
        // Stop observing after monitoring period
        setTimeout(function() {
            observer.disconnect();
            logger.info('Style mutation observer disconnected', 'HeightFixTest');
        }, maxChecks * 1000);
    }
    
    // Test the debug logger methods that were missing
    setTimeout(function() {
        logger.info('Testing debug logger methods', 'HeightFixTest');
        
        try {
            logger.debug('Debug method test', 'HeightFixTest');
            logger.info('Info method test', 'HeightFixTest');
            logger.warn('Warn method test', 'HeightFixTest');
            logger.error('Error method test', 'HeightFixTest');
            
            logger.info('All debug logger methods working correctly', 'HeightFixTest');
        } catch (error) {
            logger.error('Debug logger method test failed', 'HeightFixTest', {
                error: error.toString(),
                stack: error.stack
            });
        }
    }, 2000);
    
    // Monitor for the specific timing that was causing issues
    const criticalTimings = [500, 1000, 1500, 2000];
    
    criticalTimings.forEach(function(timing) {
        setTimeout(function() {
            const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
            if (iframe) {
                const computedStyle = window.getComputedStyle(iframe);
                logger.info(`Height status at ${timing}ms`, 'HeightFixTest', {
                    timing: timing + 'ms',
                    styleHeight: iframe.style.height,
                    computedHeight: computedStyle.height,
                    minHeight: computedStyle.minHeight,
                    maxHeight: computedStyle.maxHeight,
                    isCriticalTiming: timing === 1000
                });
            }
        }, timing);
    });
});
