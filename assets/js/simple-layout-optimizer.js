/**
 * OCHandyDude Simple Layout Optimizer
 * Non-aggressive layout optimization that runs once without causing interference
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run if debug logging is enabled
    if (!window.OCHDDebugLogger || !window.OCHDDebugLogger.enabled) {
        return;
    }
    
    const logger = window.OCHDDebugLogger;
    
    logger.info('Simple layout optimizer initialized', 'SimpleLayoutOptimizer');
    
    // Run optimization once after iframe has had time to load
    setTimeout(function() {
        optimizeLayoutOnce();
    }, 3000);
    
    function optimizeLayoutOnce() {
        logger.info('Running one-time layout optimization', 'SimpleLayoutOptimizer');
        
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (!iframe) {
            logger.warn('No booking iframe found for optimization', 'SimpleLayoutOptimizer');
            return;
        }
        
        const computedStyle = window.getComputedStyle(iframe);
        const currentHeight = parseInt(computedStyle.height);
        
        logger.info('Current iframe state', 'SimpleLayoutOptimizer', {
            height: computedStyle.height,
            width: computedStyle.width,
            margin: computedStyle.margin,
            padding: computedStyle.padding,
            position: computedStyle.position
        });
        
        let optimizationsApplied = [];
        
        // Optimization 1: Enable dynamic height with reasonable constraints
        const isMobile = window.innerWidth <= 768;
        const maxReasonableHeight = 1200;
        const minReasonableHeight = 600;

        // Remove fixed heights and enable dynamic sizing
        iframe.style.height = 'auto';
        iframe.style.minHeight = minReasonableHeight + 'px';
        iframe.style.maxHeight = maxReasonableHeight + 'px';

        optimizationsApplied.push('Enabled dynamic height with constraints: ' + minReasonableHeight + 'px - ' + maxReasonableHeight + 'px');
        
        // Optimization 2: Center the iframe and remove excessive spacing
        iframe.style.margin = '0 auto';
        iframe.style.display = 'block';
        optimizationsApplied.push('Centered iframe horizontally');
        
        if (iframe.style.padding !== '0') {
            iframe.style.padding = '0';
            optimizationsApplied.push('Removed padding');
        }
        
        // Optimization 3: Ensure proper box model
        iframe.style.boxSizing = 'border-box';
        iframe.style.verticalAlign = 'top';
        iframe.style.display = 'block';
        optimizationsApplied.push('Applied proper box model');
        
        // Optimization 4: Set reasonable height constraints for dynamic sizing
        iframe.style.maxHeight = maxReasonableHeight + 'px';
        iframe.style.minHeight = minReasonableHeight + 'px';
        optimizationsApplied.push('Set reasonable height constraints for dynamic sizing');
        
        // Optimization 5: Optimize container elements
        const container = document.querySelector('.ochd-booking-container');
        const wrapper = document.querySelector('.ochd-booking-form-wrapper');
        
        if (container) {
            container.style.margin = '0 auto';
            container.style.padding = '0';
            container.style.overflow = 'visible';
            container.style.textAlign = 'center';
            optimizationsApplied.push('Centered and optimized container');
        }

        if (wrapper) {
            wrapper.style.paddingTop = '0';
            wrapper.style.paddingBottom = '0';
            wrapper.style.margin = '0 auto';
            wrapper.style.overflow = 'visible';
            wrapper.style.textAlign = 'center';
            wrapper.style.display = 'block';
            optimizationsApplied.push('Centered and optimized wrapper');
        }
        
        // Optimization 6: Ensure overlay alignment
        const overlay = document.querySelector('#ochd-guest-overlay');
        if (overlay) {
            overlay.style.margin = '0';
            overlay.style.padding = '0';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.position = 'absolute';
            overlay.style.boxSizing = 'border-box';
            optimizationsApplied.push('Aligned overlay');
        }
        
        if (optimizationsApplied.length > 0) {
            logger.info('Layout optimizations completed', 'SimpleLayoutOptimizer', {
                optimizations: optimizationsApplied,
                finalHeight: iframe.style.height,
                finalMargin: iframe.style.margin,
                finalPadding: iframe.style.padding
            });
        } else {
            logger.info('No layout optimizations needed', 'SimpleLayoutOptimizer');
        }
        
        // Final state check
        setTimeout(function() {
            const finalStyle = window.getComputedStyle(iframe);
            logger.info('Final iframe state after optimization', 'SimpleLayoutOptimizer', {
                height: finalStyle.height,
                width: finalStyle.width,
                margin: finalStyle.margin,
                padding: finalStyle.padding,
                maxHeight: finalStyle.maxHeight,
                minHeight: finalStyle.minHeight
            });
        }, 1000);
    }
    
    // Monitor for any external changes that might affect our optimization
    let changeCount = 0;
    const maxChanges = 3;
    
    const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
    if (iframe) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    changeCount++;
                    
                    if (changeCount <= maxChanges) {
                        logger.info('External style change detected', 'SimpleLayoutOptimizer', {
                            changeNumber: changeCount,
                            maxChanges: maxChanges,
                            newStyle: iframe.style.cssText
                        });
                        
                        // Only re-optimize if we haven't exceeded the limit
                        if (changeCount === maxChanges) {
                            logger.warn('Maximum style changes reached, stopping monitoring to prevent interference', 'SimpleLayoutOptimizer');
                            observer.disconnect();
                        }
                    }
                }
            });
        });
        
        // Start monitoring after initial optimization
        setTimeout(function() {
            observer.observe(iframe, {
                attributes: true,
                attributeFilter: ['style']
            });
            
            logger.info('Started monitoring for external style changes', 'SimpleLayoutOptimizer', {
                maxChanges: maxChanges
            });
        }, 5000);
    }
});
