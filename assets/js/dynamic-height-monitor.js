/**
 * OCHandyDude Dynamic Height Monitor
 * Monitors and ensures proper dynamic height behavior for booking forms
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run if debug logging is enabled
    if (!window.OCHDDebugLogger || !window.OCHDDebugLogger.enabled) {
        return;
    }
    
    const logger = window.OCHDDebugLogger;
    
    logger.info('Dynamic height monitor initialized', 'DynamicHeightMonitor');
    
    // Start monitoring after iframe has time to load
    setTimeout(function() {
        startDynamicHeightMonitoring();
    }, 2000);
    
    function startDynamicHeightMonitoring() {
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (!iframe) {
            logger.warn('No booking iframe found for dynamic height monitoring', 'DynamicHeightMonitor');
            return;
        }
        
        logger.info('Starting dynamic height monitoring', 'DynamicHeightMonitor', {
            iframeId: iframe.id,
            initialHeight: iframe.style.height,
            initialMinHeight: iframe.style.minHeight,
            initialMaxHeight: iframe.style.maxHeight
        });
        
        // Monitor height changes and ensure proper dynamic behavior
        let lastHeight = iframe.style.height;
        let checkCount = 0;
        const maxChecks = 15; // Monitor for 15 seconds
        
        const heightMonitor = setInterval(function() {
            checkCount++;
            
            const currentHeight = iframe.style.height;
            const computedStyle = window.getComputedStyle(iframe);
            const actualHeight = computedStyle.height;
            const minHeight = computedStyle.minHeight;
            const maxHeight = computedStyle.maxHeight;
            const margin = computedStyle.margin;
            
            // Log current state
            logger.info(`Dynamic height check ${checkCount}/${maxChecks}`, 'DynamicHeightMonitor', {
                styleHeight: currentHeight,
                computedHeight: actualHeight,
                minHeight: minHeight,
                maxHeight: maxHeight,
                margin: margin,
                isDynamic: currentHeight === 'auto' || currentHeight === '',
                heightChanged: currentHeight !== lastHeight
            });
            
            // Check for issues and fix them
            let issuesFixed = [];
            
            // Issue 1: Fixed height when should be dynamic
            if (currentHeight !== 'auto' && currentHeight !== '' && parseInt(currentHeight) > 0) {
                iframe.style.height = 'auto';
                issuesFixed.push('Restored dynamic height (was: ' + currentHeight + ')');
            }
            
            // Issue 2: Missing or incorrect centering
            if (computedStyle.marginLeft !== 'auto' || computedStyle.marginRight !== 'auto') {
                iframe.style.margin = '0 auto';
                issuesFixed.push('Fixed centering (margin: 0 auto)');
            }
            
            // Issue 3: Missing display block
            if (computedStyle.display !== 'block') {
                iframe.style.display = 'block';
                issuesFixed.push('Set display: block');
            }
            
            // Issue 4: Excessive max-height
            const maxHeightPx = parseInt(maxHeight);
            if (maxHeightPx > 1300 || maxHeight === 'none') {
                iframe.style.maxHeight = '1200px';
                issuesFixed.push('Set reasonable max-height: 1200px');
            }
            
            // Issue 5: Insufficient min-height
            const minHeightPx = parseInt(minHeight);
            if (minHeightPx < 600) {
                iframe.style.minHeight = '600px';
                issuesFixed.push('Set adequate min-height: 600px');
            }
            
            // Issue 6: Container alignment
            const container = document.querySelector('.ochd-booking-container');
            const wrapper = document.querySelector('.ochd-booking-form-wrapper');
            
            if (container) {
                const containerStyle = window.getComputedStyle(container);
                if (containerStyle.textAlign !== 'center') {
                    container.style.textAlign = 'center';
                    issuesFixed.push('Fixed container text-align');
                }
                if (containerStyle.marginLeft !== 'auto' || containerStyle.marginRight !== 'auto') {
                    container.style.margin = '0 auto';
                    issuesFixed.push('Fixed container centering');
                }
            }
            
            if (wrapper) {
                const wrapperStyle = window.getComputedStyle(wrapper);
                if (wrapperStyle.textAlign !== 'center') {
                    wrapper.style.textAlign = 'center';
                    issuesFixed.push('Fixed wrapper text-align');
                }
                if (wrapperStyle.marginLeft !== 'auto' || wrapperStyle.marginRight !== 'auto') {
                    wrapper.style.margin = '0 auto';
                    issuesFixed.push('Fixed wrapper centering');
                }
            }
            
            if (issuesFixed.length > 0) {
                logger.warn('Dynamic height issues detected and fixed', 'DynamicHeightMonitor', {
                    checkNumber: checkCount,
                    issuesFixed: issuesFixed
                });
            }
            
            lastHeight = currentHeight;
            
            // Stop monitoring after maxChecks
            if (checkCount >= maxChecks) {
                clearInterval(heightMonitor);
                
                const finalStyle = window.getComputedStyle(iframe);
                logger.info('Dynamic height monitoring completed', 'DynamicHeightMonitor', {
                    totalChecks: checkCount,
                    finalHeight: iframe.style.height,
                    finalComputedHeight: finalStyle.height,
                    finalMinHeight: finalStyle.minHeight,
                    finalMaxHeight: finalStyle.maxHeight,
                    finalMargin: finalStyle.margin,
                    isDynamic: iframe.style.height === 'auto' || iframe.style.height === '',
                    isCentered: finalStyle.marginLeft === 'auto' && finalStyle.marginRight === 'auto'
                });
            }
        }, 1000); // Check every second
    }
    
    // Monitor for content changes that might affect height
    function monitorContentChanges() {
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (!iframe) return;
        
        // Try to monitor iframe content changes (if same-origin)
        try {
            if (iframe.contentDocument) {
                const observer = new MutationObserver(function(mutations) {
                    logger.info('Iframe content changed, checking height', 'DynamicHeightMonitor', {
                        mutations: mutations.length,
                        timestamp: Date.now()
                    });
                    
                    // Ensure height remains dynamic after content changes
                    setTimeout(function() {
                        if (iframe.style.height !== 'auto' && iframe.style.height !== '') {
                            iframe.style.height = 'auto';
                            logger.info('Restored dynamic height after content change', 'DynamicHeightMonitor');
                        }
                    }, 100);
                });
                
                observer.observe(iframe.contentDocument.body, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
                
                logger.info('Started monitoring iframe content changes', 'DynamicHeightMonitor');
            }
        } catch (error) {
            logger.info('Cannot monitor iframe content (cross-origin)', 'DynamicHeightMonitor');
        }
    }
    
    // Start content monitoring after initial setup
    setTimeout(monitorContentChanges, 3000);
    
    // Monitor for window resize events
    window.addEventListener('resize', function() {
        logger.info('Window resized, checking dynamic height behavior', 'DynamicHeightMonitor', {
            newViewport: window.innerWidth + 'x' + window.innerHeight
        });
        
        setTimeout(function() {
            const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
            if (iframe) {
                // Ensure height remains dynamic after resize
                if (iframe.style.height !== 'auto' && iframe.style.height !== '') {
                    iframe.style.height = 'auto';
                    logger.info('Restored dynamic height after resize', 'DynamicHeightMonitor');
                }
                
                // Ensure centering is maintained
                iframe.style.margin = '0 auto';
                iframe.style.display = 'block';
            }
        }, 100);
    });
});
