/**
 * OCHandyDude Header Icon State Manager
 * Manages exclusive activation and proper toggle behavior for all header icons
 */
document.addEventListener('DOMContentLoaded', function() {
    
    // Icon state management
    const IconManager = {
        activeIcon: null,
        
        // Register an icon for state management with improved consistency
        registerIcon: function(element, iconType, toggleCallback) {
            if (!element) return;

            element.addEventListener('click', (event) => {
                // CRITICAL: Only handle clicks on the actual icon elements
                const isMenuLink = event.target.closest('.ochd-profile-dropdown a, .ochd-guest-login-button, .ochd-menu-button__link');
                const isProfileIcon = event.target.closest('.ochd-profile-icon');
                const isMobileToggle = event.target.closest('.ochd-mobile-menu-toggle');
                const isCartIcon = event.target.closest('.ochd-cart-icon');

                if (isMenuLink) {
                    // Allow menu links to work normally - don't interfere at all
                    console.log(`${iconType} menu link clicked, allowing navigation`);
                    // Close menu after navigation starts
                    setTimeout(() => {
                        this.closeAllIcons();
                    }, 100);
                    return; // Don't prevent default or stop propagation
                }

                // Determine if this is a valid icon click based on the registered element
                let isValidIconClick = false;
                if (iconType === 'profile' && isProfileIcon) {
                    isValidIconClick = true;
                } else if (iconType === 'menu' && isMobileToggle) {
                    isValidIconClick = true;
                } else if (iconType === 'cart' && isCartIcon) {
                    isValidIconClick = true;
                }

                if (!isValidIconClick) {
                    return; // Not a valid icon click for this registered element
                }

                // Prevent default and stop propagation only for valid icon clicks
                event.preventDefault();
                event.stopPropagation();

                console.log(`${iconType} icon clicked - processing menu switch`);

                // Check if this icon is currently active
                const isCurrentlyActive = this.activeIcon === element;

                // ALWAYS close all icons first for consistent behavior
                this.closeAllIcons();

                // Small delay to ensure all menus are closed before opening new one
                setTimeout(() => {
                    // If it wasn't active, activate it now
                    if (!isCurrentlyActive) {
                        this.activateIcon(element, iconType, toggleCallback);
                    } else {
                        // If it was active, it's now closed (already handled by closeAllIcons)
                        console.log(`${iconType} icon deactivated`);
                    }
                }, 50); // Small delay for smooth transitions
            });
        },
        
        // Activate a specific icon with proper interaction enabling
        activateIcon: function(element, iconType, toggleCallback) {
            this.activeIcon = element;

            // Apply visual active state
            element.classList.add('ochd-icon-active');

            // Execute the icon-specific toggle callback
            if (toggleCallback) {
                toggleCallback(true);
            }

            // Re-enable interactions for the activated menu
            this.enableMenuInteractions(iconType);

            console.log(`${iconType} icon activated with interactions enabled`);
        },

        // Enable interactions for specific menu types
        enableMenuInteractions: function(iconType) {
            switch (iconType) {
                case 'profile':
                    const profileDropdown = document.querySelector('.ochd-profile-dropdown');
                    if (profileDropdown) {
                        profileDropdown.style.pointerEvents = 'auto';
                        profileDropdown.setAttribute('aria-hidden', 'false');
                        profileDropdown.removeAttribute('tabindex');
                    }
                    break;

                case 'menu':
                    const menuContainer = document.querySelector('.ochd-main-menu-container');
                    if (menuContainer) {
                        menuContainer.style.pointerEvents = 'auto';
                        menuContainer.setAttribute('aria-hidden', 'false');
                        menuContainer.removeAttribute('tabindex');
                    }
                    break;

                case 'cart':
                    // Cart interactions are handled by WooCommerce
                    break;
            }
        },
        
        // Close all icons and menus - enhanced with proper interaction disabling
        closeAllIcons: function() {
            // Remove active state from all icons
            document.querySelectorAll('.ochd-icon-active').forEach(icon => {
                icon.classList.remove('ochd-icon-active');
            });

            // Close profile dropdown with interaction disabling
            const profileMenu = document.querySelector('.ochd-profile-menu');
            if (profileMenu && profileMenu.classList.contains('is-active')) {
                profileMenu.classList.remove('is-active');

                // Ensure dropdown is completely non-interactive
                const profileDropdown = profileMenu.querySelector('.ochd-profile-dropdown');
                if (profileDropdown) {
                    profileDropdown.style.pointerEvents = 'none';
                    profileDropdown.setAttribute('aria-hidden', 'true');
                    profileDropdown.setAttribute('tabindex', '-1');
                }

                console.log('Header Icon Manager: Profile menu closed and disabled');
            }

            // Close mobile menu with interaction disabling
            const menuToggle = document.querySelector('.ochd-mobile-menu-toggle');
            const menuContainer = document.querySelector('.ochd-main-menu-container');
            if (menuToggle && menuContainer) {
                if (menuToggle.classList.contains('is-open')) {
                    menuToggle.classList.remove('is-open');
                    menuContainer.classList.remove('is-open');

                    // Ensure menu container is completely non-interactive
                    menuContainer.style.pointerEvents = 'none';
                    menuContainer.setAttribute('aria-hidden', 'true');
                    menuContainer.setAttribute('tabindex', '-1');

                    menuToggle.setAttribute('aria-expanded', 'false');
                    console.log('Header Icon Manager: Mobile menu closed and disabled');
                }
            }

            // Close WooCommerce mini cart if open
            this.closeMiniCart();

            // Reset active icon
            this.activeIcon = null;
            console.log('Header Icon Manager: All menus closed and interactions disabled');
        },

        // Helper method to close WooCommerce mini cart (React-safe approach)
        closeMiniCart: function() {
            try {
                // Method 1: Use React-safe approach - dispatch custom event
                const cartDrawer = document.querySelector('.wc-block-mini-cart__drawer');
                if (cartDrawer && this.isCartOpen()) {
                    // Dispatch a custom event that WooCommerce can handle
                    const closeEvent = new CustomEvent('wc-blocks-mini-cart-close', {
                        bubbles: true,
                        cancelable: true
                    });
                    cartDrawer.dispatchEvent(closeEvent);
                    console.log('Header Icon Manager: Mini cart close event dispatched');

                    // Fallback: Try to find and click close button without DOM manipulation
                    setTimeout(() => {
                        if (this.isCartOpen()) {
                            const closeButton = document.querySelector('.wc-block-mini-cart__drawer [aria-label*="Close"], .wc-block-mini-cart__drawer .wc-block-mini-cart__close-button');
                            if (closeButton && typeof closeButton.click === 'function') {
                                closeButton.click();
                                console.log('Header Icon Manager: Mini cart closed via close button');
                            }
                        }
                    }, 100);

                    return;
                }

                // Method 2: If no drawer, try to toggle cart button (React-safe)
                const cartButton = document.querySelector('.wc-block-mini-cart button');
                if (cartButton && this.isCartOpen()) {
                    // Only click if cart appears to be open
                    cartButton.click();
                    console.log('Header Icon Manager: Mini cart toggled closed');
                    return;
                }

                console.log('Header Icon Manager: Mini cart already closed or not found');
            } catch (error) {
                console.warn('Header Icon Manager: Error closing mini cart:', error);
                // Don't force DOM manipulation that could conflict with React
            }
        },

        // Helper method to check if cart is open (React-safe)
        isCartOpen: function() {
            const cartDrawer = document.querySelector('.wc-block-mini-cart__drawer');
            if (!cartDrawer) return false;

            // Check multiple indicators that cart is open
            return cartDrawer.classList.contains('is-open') ||
                   cartDrawer.style.display === 'block' ||
                   cartDrawer.offsetParent !== null ||
                   cartDrawer.getAttribute('aria-hidden') === 'false';
        },
        
        // Initialize all header icons
        init: function() {
            // Use requestAnimationFrame for better performance and timing
            requestAnimationFrame(() => {
                this.initializeIcons();
            });
        },

        initializeIcons: function() {
            // Profile menu icon - register only the clickable icon for consistency
            const profileIcon = document.querySelector('.ochd-profile-icon');
            if (profileIcon) {
                try {
                    // Remove existing event listeners by cloning (consistent with other icons)
                    const newProfileIcon = profileIcon.cloneNode(true);
                    profileIcon.parentNode.replaceChild(newProfileIcon, profileIcon);

                    this.registerIcon(newProfileIcon, 'profile', (activate) => {
                        const profileMenu = document.querySelector('.ochd-profile-menu');
                        if (profileMenu && activate) {
                            profileMenu.classList.add('is-active');
                            // Add smooth animation trigger
                            profileMenu.style.transform = 'translateY(0)';
                        }
                    });
                    console.log('Header Icon Manager: Profile icon initialized successfully');
                } catch (error) {
                    console.error('Header Icon Manager: Error initializing profile icon:', error);
                }
            }

            // Mobile menu toggle - override existing handler with improved functionality
            const menuToggle = document.querySelector('.ochd-mobile-menu-toggle');
            if (menuToggle) {
                try {
                    // Remove existing event listeners by cloning
                    const newMenuToggle = menuToggle.cloneNode(true);
                    menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);

                    this.registerIcon(newMenuToggle, 'menu', (activate) => {
                        const menuContainer = document.querySelector('.ochd-main-menu-container');
                        if (activate && menuContainer) {
                            newMenuToggle.classList.add('is-open');
                            menuContainer.classList.add('is-open');
                            // Update ARIA attributes for accessibility
                            newMenuToggle.setAttribute('aria-expanded', 'true');
                            menuContainer.setAttribute('aria-hidden', 'false');
                        }
                    });
                    console.log('Header Icon Manager: Mobile menu toggle initialized successfully');
                } catch (error) {
                    console.error('Header Icon Manager: Error initializing mobile menu toggle:', error);
                }
            }

            // Cart icon - override existing handler with improved error handling
            const cartIcon = document.querySelector('.ochd-cart-icon');
            if (cartIcon) {
                try {
                    // Remove existing event listeners by cloning
                    const newCartIcon = cartIcon.cloneNode(true);
                    cartIcon.parentNode.replaceChild(newCartIcon, cartIcon);

                    this.registerIcon(newCartIcon, 'cart', (activate) => {
                        if (activate) {
                            // Trigger the WooCommerce cart with React-safe approach
                            const miniCart = document.querySelector('.wc-block-mini-cart');
                            if (miniCart) {
                                const cartButton = miniCart.querySelector('button');
                                if (cartButton) {
                                    // Use requestAnimationFrame and React-safe approach
                                    requestAnimationFrame(() => {
                                        try {
                                            cartButton.click();
                                            console.log('Header Icon Manager: Cart opened successfully');
                                        } catch (error) {
                                            console.warn('Header Icon Manager: Error opening cart:', error);
                                            window.location.href = '/cart';
                                        }
                                    });
                                } else {
                                    console.warn('Header Icon Manager: Cart button not found, falling back to cart page');
                                    window.location.href = '/cart';
                                }
                            } else {
                                console.warn('Header Icon Manager: Mini cart not found');
                            }
                        }
                    });
                    console.log('Header Icon Manager: Cart icon initialized successfully');
                } catch (error) {
                    console.error('Header Icon Manager: Error initializing cart icon:', error);
                }
            }

            // Close icons when clicking outside (with proper menu link handling)
            document.addEventListener('click', (event) => {
                // Check if click is on a menu link - if so, allow navigation and close menu after
                const isMenuLink = event.target.closest('.ochd-profile-dropdown a, .ochd-guest-login-button');
                if (isMenuLink) {
                    // Allow the link to navigate, then close menu after a short delay
                    setTimeout(() => {
                        this.closeAllIcons();
                    }, 100);
                    return;
                }

                // Check if click is outside header icons area
                const profileMenu = document.querySelector('.ochd-profile-menu');
                const miniCart = document.querySelector('.wc-block-mini-cart');
                const menuContainer = document.querySelector('.ochd-main-menu-container');

                const isOutsideClick = !profileMenu?.contains(event.target) &&
                                     !miniCart?.contains(event.target) &&
                                     !menuContainer?.contains(event.target);

                if (isOutsideClick) {
                    this.closeAllIcons();
                }
            });

            console.log('Header Icon Manager initialized with exclusive activation');
        }
    };
    
    // Initialize the icon manager
    IconManager.init();
    
    // Make it globally available for debugging
    window.OCHDIconManager = IconManager;
});
