/**
 * OCHandyDude Booking Form Fix Test Script
 * Tests the fixes for auto-fill functionality and JavaScript errors
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run if debug logging is enabled
    if (!window.OCHDDebugLogger || !window.OCHDDebugLogger.enabled) {
        return;
    }
    
    const logger = window.OCHDDebugLogger;
    
    setTimeout(function() {
        runBookingFormFixTests();
    }, 2000);
    
    function runBookingFormFixTests() {
        logger.log('=== BOOKING FORM FIX TESTS START ===', 'FixTest', 'info');
        
        // Test 1: Check if ajaxurl is available
        testAjaxUrlAvailability();
        
        // Test 2: Check if ochdBookingData is available
        testBookingDataAvailability();
        
        // Test 3: Check if debug AJAX data is available
        testDebugAjaxAvailability();
        
        // Test 4: Test iframe communication
        testIframeCommunication();
        
        // Test 5: Check for JavaScript errors
        testJavaScriptErrors();
        
        logger.log('=== BOOKING FORM FIX TESTS END ===', 'FixTest', 'info');
    }
    
    function testAjaxUrlAvailability() {
        logger.log('Testing AJAX URL availability', 'FixTest', 'info');
        
        // Check global ajaxurl (should not be required anymore)
        const globalAjaxUrl = typeof ajaxurl !== 'undefined';
        
        // Check localized debug AJAX data
        const debugAjaxUrl = typeof ochdDebugAjax !== 'undefined' && ochdDebugAjax.ajaxurl;
        
        // Check booking data AJAX URL
        const bookingAjaxUrl = typeof ochdBookingData !== 'undefined' && ochdBookingData.ajax_url;
        
        logger.log('AJAX URL availability results', 'FixTest', 'info', {
            globalAjaxUrl: globalAjaxUrl,
            debugAjaxUrl: !!debugAjaxUrl,
            bookingAjaxUrl: !!bookingAjaxUrl,
            debugAjaxData: typeof ochdDebugAjax !== 'undefined' ? ochdDebugAjax : 'not available'
        });
        
        if (!debugAjaxUrl && !bookingAjaxUrl) {
            logger.logError('No AJAX URL available for debug logging', new Error('AJAX URL not found'));
        }
    }
    
    function testBookingDataAvailability() {
        logger.log('Testing booking data availability', 'FixTest', 'info');
        
        if (typeof ochdBookingData === 'undefined') {
            logger.logError('ochdBookingData not available', new Error('ochdBookingData undefined'));
            return;
        }
        
        logger.log('Booking data available', 'FixTest', 'info', {
            isLoggedIn: ochdBookingData.isLoggedIn,
            hasUserData: !!ochdBookingData.userData,
            hasAjaxUrl: !!ochdBookingData.ajax_url,
            hasLoginUrl: !!ochdBookingData.login_url,
            hasNonce: !!ochdBookingData.nonce,
            userData: ochdBookingData.userData || 'not available'
        });
        
        // Test auto-fill data structure
        if (ochdBookingData.isLoggedIn && ochdBookingData.userData) {
            const userData = ochdBookingData.userData;
            const requiredFields = ['first_name', 'last_name', 'email'];
            const missingFields = requiredFields.filter(field => !userData[field]);
            
            if (missingFields.length > 0) {
                logger.logError('Missing required user data fields', new Error('Missing: ' + missingFields.join(', ')));
            } else {
                logger.log('All required user data fields present', 'FixTest', 'info');
            }
        }
    }
    
    function testDebugAjaxAvailability() {
        logger.log('Testing debug AJAX availability', 'FixTest', 'info');
        
        if (typeof ochdDebugAjax === 'undefined') {
            logger.log('Debug AJAX data not available (expected if WP_DEBUG is off)', 'FixTest', 'warn');
            return;
        }
        
        logger.log('Debug AJAX data available', 'FixTest', 'info', {
            hasAjaxUrl: !!ochdDebugAjax.ajaxurl,
            hasNonce: !!ochdDebugAjax.nonce,
            enabled: ochdDebugAjax.enabled,
            wpDebugEnabled: ochdDebugAjax.wpDebugEnabled
        });
        
        // Test AJAX call
        if (typeof jQuery !== 'undefined' && ochdDebugAjax.ajaxurl) {
            jQuery.post(ochdDebugAjax.ajaxurl, {
                action: 'ochd_log_to_file',
                log_entry: JSON.stringify({
                    timestamp: new Date().toISOString(),
                    component: 'FixTest',
                    level: 'INFO',
                    message: 'AJAX test from fix test script'
                }),
                nonce: ochdDebugAjax.nonce
            }).done(function() {
                logger.log('AJAX test successful', 'FixTest', 'info');
            }).fail(function(xhr, status, error) {
                logger.logError('AJAX test failed', new Error(status + ': ' + error));
            });
        }
    }
    
    function testIframeCommunication() {
        logger.log('Testing iframe communication', 'FixTest', 'info');
        
        const iframes = document.querySelectorAll('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (iframes.length === 0) {
            logger.log('No booking iframes found', 'FixTest', 'warn');
            return;
        }
        
        iframes.forEach(function(iframe, index) {
            logger.log('Found booking iframe', 'FixTest', 'info', {
                index: index,
                id: iframe.id,
                src: iframe.src,
                loaded: iframe.contentDocument !== null
            });
            
            // Test if we can communicate with the iframe
            try {
                if (iframe.contentWindow) {
                    // Send a test message
                    const testMessage = {
                        source: 'OCHandyDudeFixTest',
                        test: true,
                        timestamp: Date.now()
                    };
                    
                    iframe.contentWindow.postMessage(testMessage, '*');
                    logger.log('Test message sent to iframe', 'FixTest', 'info', {
                        iframeIndex: index,
                        message: testMessage
                    });
                }
            } catch (error) {
                logger.logError('Failed to communicate with iframe', error);
            }
        });
    }
    
    function testJavaScriptErrors() {
        logger.log('Testing for JavaScript errors', 'FixTest', 'info');
        
        // Set up error listener for a short time
        const originalErrorHandler = window.onerror;
        const errors = [];
        
        window.onerror = function(message, source, lineno, colno, error) {
            errors.push({
                message: message,
                source: source,
                line: lineno,
                column: colno,
                error: error ? error.toString() : 'unknown'
            });
            
            // Call original handler if it exists
            if (originalErrorHandler) {
                return originalErrorHandler.apply(this, arguments);
            }
        };
        
        // Test some potentially problematic operations
        setTimeout(function() {
            try {
                // Test debug logger methods
                logger.debug('Error test debug message');
                logger.info('Error test info message');
                logger.warn('Error test warn message');
                
                // Test specialized methods
                logger.logBookingHeight('Error test height message', { test: true });
                logger.logIframeEvent('Error test iframe message', { test: true });
                
                logger.log('JavaScript error test completed', 'FixTest', 'info', {
                    errorsDetected: errors.length,
                    errors: errors
                });
            } catch (testError) {
                logger.logError('Error during JavaScript error test', testError);
            }
            
            // Restore original error handler
            window.onerror = originalErrorHandler;
        }, 1000);
    }
    
    // Monitor for auto-fill events
    window.addEventListener('message', function(event) {
        if (event.data && event.data.source === 'OchdIframe') {
            logger.logIframeEvent('Message received from booking iframe', {
                data: event.data,
                origin: event.origin,
                timestamp: Date.now()
            });
        }
    });
    
    logger.log('Booking form fix test script initialized', 'FixTest', 'info');
});
