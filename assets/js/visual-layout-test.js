/**
 * OCHandyDude Visual Layout Test Script
 * Tests and fixes visual layout issues with booking form spacing and margins
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run if debug logging is enabled
    if (!window.OCHDDebugLogger || !window.OCHDDebugLogger.enabled) {
        return;
    }
    
    const logger = window.OCHDDebugLogger;
    
    logger.info('Visual layout test script initialized', 'VisualLayoutTest');
    
    // Test visual layout after a short delay to allow iframe to load
    setTimeout(function() {
        runVisualLayoutTests();
    }, 2000);
    
    function runVisualLayoutTests() {
        logger.info('=== VISUAL LAYOUT TESTS START ===', 'VisualLayoutTest');
        
        // Test 1: Check iframe dimensions and spacing
        testIframeDimensions();
        
        // Test 2: Check container spacing
        testContainerSpacing();
        
        // Test 3: Check for excessive white space
        testWhiteSpaceIssues();
        
        // Test 4: Apply layout fixes if needed
        applyLayoutFixes();
        
        logger.info('=== VISUAL LAYOUT TESTS END ===', 'VisualLayoutTest');
    }
    
    function testIframeDimensions() {
        logger.info('Testing iframe dimensions and spacing', 'VisualLayoutTest');
        
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (!iframe) {
            logger.warn('No booking iframe found for layout testing', 'VisualLayoutTest');
            return;
        }
        
        const computedStyle = window.getComputedStyle(iframe);
        const rect = iframe.getBoundingClientRect();
        
        const dimensions = {
            // Style dimensions
            styleHeight: iframe.style.height,
            styleWidth: iframe.style.width,
            styleMargin: iframe.style.margin,
            stylePadding: iframe.style.padding,
            
            // Computed dimensions
            computedHeight: computedStyle.height,
            computedWidth: computedStyle.width,
            computedMargin: computedStyle.margin,
            computedPadding: computedStyle.padding,
            computedMarginTop: computedStyle.marginTop,
            computedMarginBottom: computedStyle.marginBottom,
            computedPaddingTop: computedStyle.paddingTop,
            computedPaddingBottom: computedStyle.paddingBottom,
            
            // Actual dimensions
            actualWidth: rect.width,
            actualHeight: rect.height,
            
            // Position
            top: rect.top,
            left: rect.left
        };
        
        logger.info('Iframe dimensions analysis', 'VisualLayoutTest', dimensions);
        
        // Check for potential issues
        const heightPx = parseInt(computedStyle.height);
        const marginTopPx = parseInt(computedStyle.marginTop);
        const marginBottomPx = parseInt(computedStyle.marginBottom);
        const paddingTopPx = parseInt(computedStyle.paddingTop);
        const paddingBottomPx = parseInt(computedStyle.paddingBottom);
        
        const issues = [];
        
        if (heightPx > 1600) {
            issues.push('Height may be excessive (' + heightPx + 'px)');
        }
        
        if (marginTopPx > 20 || marginBottomPx > 20) {
            issues.push('Large margins detected (top: ' + marginTopPx + 'px, bottom: ' + marginBottomPx + 'px)');
        }
        
        if (paddingTopPx > 20 || paddingBottomPx > 20) {
            issues.push('Large padding detected (top: ' + paddingTopPx + 'px, bottom: ' + paddingBottomPx + 'px)');
        }
        
        if (issues.length > 0) {
            logger.warn('Iframe dimension issues detected', 'VisualLayoutTest', {
                issues: issues,
                recommendations: [
                    'Consider reducing height if excessive',
                    'Remove large margins/padding',
                    'Ensure content fits properly'
                ]
            });
        } else {
            logger.info('Iframe dimensions look good', 'VisualLayoutTest');
        }
    }
    
    function testContainerSpacing() {
        logger.info('Testing container spacing', 'VisualLayoutTest');
        
        const container = document.querySelector('.ochd-booking-container');
        const wrapper = document.querySelector('.ochd-booking-form-wrapper');
        
        if (container) {
            const containerStyle = window.getComputedStyle(container);
            logger.info('Container spacing analysis', 'VisualLayoutTest', {
                margin: containerStyle.margin,
                padding: containerStyle.padding,
                marginTop: containerStyle.marginTop,
                marginBottom: containerStyle.marginBottom,
                paddingTop: containerStyle.paddingTop,
                paddingBottom: containerStyle.paddingBottom
            });
        }
        
        if (wrapper) {
            const wrapperStyle = window.getComputedStyle(wrapper);
            logger.info('Wrapper spacing analysis', 'VisualLayoutTest', {
                margin: wrapperStyle.margin,
                padding: wrapperStyle.padding,
                marginTop: wrapperStyle.marginTop,
                marginBottom: wrapperStyle.marginBottom,
                paddingTop: wrapperStyle.paddingTop,
                paddingBottom: wrapperStyle.paddingBottom
            });
        }
    }
    
    function testWhiteSpaceIssues() {
        logger.info('Testing for white space issues', 'VisualLayoutTest');
        
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (!iframe) return;
        
        // Check if iframe content is accessible (same-origin)
        try {
            if (iframe.contentDocument && iframe.contentDocument.body) {
                const contentBody = iframe.contentDocument.body;
                const contentStyle = iframe.contentWindow.getComputedStyle(contentBody);
                
                logger.info('Iframe content body analysis', 'VisualLayoutTest', {
                    bodyMargin: contentStyle.margin,
                    bodyPadding: contentStyle.padding,
                    bodyHeight: contentStyle.height,
                    scrollHeight: contentBody.scrollHeight,
                    offsetHeight: contentBody.offsetHeight
                });
                
                // Check for excessive spacing in content
                const bodyMarginTop = parseInt(contentStyle.marginTop);
                const bodyMarginBottom = parseInt(contentStyle.marginBottom);
                const bodyPaddingTop = parseInt(contentStyle.paddingTop);
                const bodyPaddingBottom = parseInt(contentStyle.paddingBottom);
                
                if (bodyMarginTop > 50 || bodyMarginBottom > 50 || bodyPaddingTop > 50 || bodyPaddingBottom > 50) {
                    logger.warn('Excessive spacing detected in iframe content', 'VisualLayoutTest', {
                        bodyMarginTop: bodyMarginTop,
                        bodyMarginBottom: bodyMarginBottom,
                        bodyPaddingTop: bodyPaddingTop,
                        bodyPaddingBottom: bodyPaddingBottom
                    });
                }
            } else {
                logger.info('Cannot access iframe content (cross-origin)', 'VisualLayoutTest');
            }
        } catch (error) {
            logger.info('Cannot access iframe content due to cross-origin restrictions', 'VisualLayoutTest');
        }
    }
    
    function applyLayoutFixes() {
        logger.info('Applying layout fixes', 'VisualLayoutTest');
        
        const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        
        if (!iframe) return;
        
        const computedStyle = window.getComputedStyle(iframe);
        const currentHeight = parseInt(computedStyle.height);
        
        let fixesApplied = [];
        
        // Fix 1: Remove excessive margins and padding
        if (iframe.style.margin !== '0') {
            iframe.style.margin = '0';
            fixesApplied.push('Reset iframe margin to 0');
        }
        
        if (iframe.style.padding !== '0') {
            iframe.style.padding = '0';
            fixesApplied.push('Reset iframe padding to 0');
        }
        
        // Fix 2: Optimize height if excessive (less aggressive)
        if (currentHeight > 1300) {
            const isMobile = window.innerWidth <= 768;
            const optimalHeight = isMobile ? '900px' : '1000px';

            iframe.style.height = optimalHeight;
            fixesApplied.push('Reduced height from ' + currentHeight + 'px to ' + optimalHeight);
        }
        
        // Fix 3: Ensure proper box-sizing
        iframe.style.boxSizing = 'border-box';
        fixesApplied.push('Set box-sizing to border-box');
        
        // Fix 4: Ensure proper vertical alignment
        iframe.style.verticalAlign = 'top';
        fixesApplied.push('Set vertical-align to top');
        
        if (fixesApplied.length > 0) {
            logger.info('Layout fixes applied', 'VisualLayoutTest', {
                fixes: fixesApplied,
                newHeight: iframe.style.height,
                newMargin: iframe.style.margin,
                newPadding: iframe.style.padding
            });
        } else {
            logger.info('No layout fixes needed', 'VisualLayoutTest');
        }
        
        // Apply fixes to container elements as well
        const container = document.querySelector('.ochd-booking-container');
        const wrapper = document.querySelector('.ochd-booking-form-wrapper');
        
        if (container) {
            container.style.margin = '0';
            container.style.overflow = 'visible';
        }
        
        if (wrapper) {
            wrapper.style.paddingTop = '0';
            wrapper.style.paddingBottom = '0';
            wrapper.style.margin = '0';
        }
    }
    
    // Monitor for layout changes
    const iframe = document.querySelector('.easyappointments-iframe, .ochd-integrated-booking-iframe');
    if (iframe) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    logger.info('Iframe style changed - checking layout', 'VisualLayoutTest', {
                        newStyle: iframe.style.cssText,
                        timestamp: Date.now()
                    });
                    
                    // Re-apply fixes if needed (less frequently to prevent interference)
                    setTimeout(applyLayoutFixes, 2000);
                }
            });
        });
        
        observer.observe(iframe, {
            attributes: true,
            attributeFilter: ['style']
        });
        
        logger.info('Layout change monitoring started', 'VisualLayoutTest');
    }
});
