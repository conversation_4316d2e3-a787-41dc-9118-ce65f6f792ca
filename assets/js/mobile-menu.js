/**
 * OCHandyDude Mobile Menu Script
 * Handles mobile menu functionality with enhanced error handling and accessibility
 */
document.addEventListener('DOMContentLoaded', function() {
    const mainHeader = document.querySelector('.wp-block-group.is-content-justification-space-between');
    const iconsContainer = document.querySelector('.ochd-profile-menu, .wc-block-mini-cart')?.parentNode;

    if (!mainHeader || !iconsContainer) {
        console.warn('OCHandyDude Menu: Required header elements not found.');
        return;
    }

    // --- 1. Create container for mobile menu ---
    const menuContainer = document.createElement('nav');
    menuContainer.className = 'ochd-main-menu-container';
    menuContainer.setAttribute('aria-label', 'Main Navigation Menu');
    menuContainer.setAttribute('aria-hidden', 'true');
    // Insert container after header
    mainHeader.parentNode.insertBefore(menuContainer, mainHeader.nextSibling);

    // --- 2. Load menu HTML via AJAX with error handling ---
    fetch(ochdMobileMenu.ajax_url + '?action=ochd_get_main_menu')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            menuContainer.innerHTML = html;
            console.log('OCHandyDude Menu: Menu content loaded successfully');
        })
        .catch(error => {
            console.error('OCHandyDude Menu: Failed to load menu content:', error);
            menuContainer.innerHTML = '<p>Menu temporarily unavailable</p>';
        });

    // --- 3. Create hamburger toggle button and add it to icons container ---
    const toggleButton = document.createElement('button');
    toggleButton.className = 'ochd-mobile-menu-toggle';
    toggleButton.setAttribute('aria-label', 'Toggle Navigation Menu');
    toggleButton.setAttribute('aria-expanded', 'false');
    toggleButton.setAttribute('aria-controls', 'ochd-main-menu-container');
    toggleButton.innerHTML = '<span></span><span></span><span></span>';
    iconsContainer.prepend(toggleButton);

    // --- 4. Create custom cart icon with improved accessibility ---
    const miniCart = document.querySelector('.wc-block-mini-cart');
    if (miniCart) {
        const customCartIcon = document.createElement('a');
        customCartIcon.href = '/cart';
        customCartIcon.className = 'ochd-unified-icon-wrapper ochd-cart-icon';
        customCartIcon.setAttribute('aria-label', 'View Shopping Cart');
        customCartIcon.setAttribute('role', 'button');
        customCartIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path></svg>`;
        miniCart.parentNode.insertBefore(customCartIcon, miniCart.nextSibling);

        customCartIcon.addEventListener('click', function(e) {
            e.preventDefault();
            const cartButton = miniCart.querySelector('button');
            if (cartButton) {
                cartButton.click();
                console.log('OCHandyDude Menu: Cart icon clicked, triggering WooCommerce cart');
            } else {
                console.warn('OCHandyDude Menu: Cart button not found, falling back to cart page');
                window.location.href = '/cart';
            }
        });

        // Add keyboard support for cart icon
        customCartIcon.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    }

    // --- 5. Handle menu toggle with enhanced accessibility ---
    toggleButton.addEventListener('click', function() {
        const isOpen = this.classList.contains('is-open');

        // Toggle classes
        this.classList.toggle('is-open');
        menuContainer.classList.toggle('is-open');

        // Update ARIA attributes
        this.setAttribute('aria-expanded', !isOpen);
        menuContainer.setAttribute('aria-hidden', isOpen);

        // Log state change for debugging
        console.log(`OCHandyDude Menu: Menu ${!isOpen ? 'opened' : 'closed'}`);
    });

    // Close menu when clicking outside or on menu links
    document.addEventListener('click', function(event) {
        // Check if click is on a menu link - if so, allow navigation and close menu
        const isMenuLink = event.target.closest('.ochd-menu-button__link');
        if (isMenuLink && toggleButton.classList.contains('is-open')) {
            // Close menu immediately for navigation
            toggleButton.classList.remove('is-open');
            menuContainer.classList.remove('is-open');
            toggleButton.setAttribute('aria-expanded', 'false');
            menuContainer.setAttribute('aria-hidden', 'true');
            console.log('OCHandyDude Menu: Menu closed by menu link click');
            return;
        }

        // Close menu if clicking outside
        if (!toggleButton.contains(event.target) && !menuContainer.contains(event.target)) {
            if (toggleButton.classList.contains('is-open')) {
                toggleButton.classList.remove('is-open');
                menuContainer.classList.remove('is-open');
                toggleButton.setAttribute('aria-expanded', 'false');
                menuContainer.setAttribute('aria-hidden', 'true');
                console.log('OCHandyDude Menu: Menu closed by outside click');
            }
        }
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && toggleButton.classList.contains('is-open')) {
            toggleButton.classList.remove('is-open');
            menuContainer.classList.remove('is-open');
            toggleButton.setAttribute('aria-expanded', 'false');
            menuContainer.setAttribute('aria-hidden', 'true');
            toggleButton.focus(); // Return focus to toggle button
            console.log('OCHandyDude Menu: Menu closed by Escape key');
        }
    });

    console.log('OCHandyDude Menu: Mobile menu initialized successfully');
});