/**
 * OCHandyDude Debug Logging Test Script
 * Comprehensive testing for the debug logging system
 * Only loads when debug logging is enabled
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run if debug logging is enabled
    if (!window.OCHDDebugLogger || !window.OCHDDebugLogger.enabled) {
        return;
    }

    const logger = window.OCHDDebugLogger;

    // Wait for debug AJAX data to be available
    if (typeof ochdDebugAjax === 'undefined') {
        console.warn('OCHandyDude Debug: AJAX data not available, some features may not work');
    }
    
    // Test the debug logging system
    setTimeout(function() {
        testDebugLoggingSystem();
    }, 3000);
    
    function testDebugLoggingSystem() {
        logger.log('=== DEBUG LOGGING SYSTEM TEST START ===', 'DebugTest', 'info');
        
        // Test 1: Basic logging levels
        logger.debug('Debug level test message', 'DebugTest');
        logger.info('Info level test message', 'DebugTest');
        logger.warn('Warning level test message', 'DebugTest');
        logger.error('Error level test message', 'DebugTest');
        
        // Test 2: Specialized booking form logging
        logger.logBookingHeight('Test booking height message', {
            testHeight: '800px',
            testViewport: '1024x768',
            testDevice: 'desktop'
        });
        
        logger.logIframeEvent('Test iframe event message', {
            testIframeId: 'test-iframe',
            testSrc: 'https://example.com',
            testEvent: 'load'
        });
        
        logger.logHeightChange('600px', '800px', 'Test height change');
        
        // Test 3: Performance timing
        logger.startTimer('test-operation');
        setTimeout(function() {
            logger.endTimer('test-operation', 'Test operation completed');
        }, 100);
        
        // Test 4: Error logging
        try {
            throw new Error('Test error for logging');
        } catch (e) {
            logger.logError('Test error caught', e);
        }
        
        // Test 5: Data logging
        logger.log('Test with complex data', 'DebugTest', 'info', {
            complexObject: {
                nested: {
                    value: 'test',
                    number: 42,
                    boolean: true,
                    array: [1, 2, 3]
                }
            },
            timestamp: Date.now(),
            userAgent: navigator.userAgent
        });
        
        // Test 6: Booking form specific tests
        testBookingFormLogging();
        
        logger.log('=== DEBUG LOGGING SYSTEM TEST END ===', 'DebugTest', 'info');
    }
    
    function testBookingFormLogging() {
        logger.log('Testing booking form specific logging', 'BookingFormTest', 'info');
        
        // Simulate iframe detection
        const testIframes = document.querySelectorAll('iframe');
        logger.logIframeEvent('Found iframes on page', {
            count: testIframes.length,
            iframes: Array.from(testIframes).map(iframe => ({
                id: iframe.id,
                src: iframe.src,
                classes: iframe.className
            }))
        });
        
        // Test height monitoring
        if (testIframes.length > 0) {
            const iframe = testIframes[0];
            const computedStyle = window.getComputedStyle(iframe);
            
            logger.logBookingHeight('Current iframe styles detected', {
                height: computedStyle.height,
                width: computedStyle.width,
                maxHeight: computedStyle.maxHeight,
                minHeight: computedStyle.minHeight,
                overflow: computedStyle.overflow,
                position: computedStyle.position
            });
            
            // Height change simulation disabled to prevent layout interference
            logger.info('Height change simulation disabled to prevent layout issues', 'BookingFormTest', {
                currentHeight: iframe.style.height,
                reason: 'Test was causing unwanted layout changes'
            });
        }
        
        // Test viewport and device detection
        logger.logBookingHeight('Current viewport and device info', {
            viewport: window.innerWidth + 'x' + window.innerHeight,
            isMobile: window.innerWidth <= 768,
            isSmallMobile: window.innerWidth <= 480,
            isVerySmallMobile: window.innerWidth <= 360,
            isLandscape: window.innerWidth > window.innerHeight,
            devicePixelRatio: window.devicePixelRatio,
            screenResolution: screen.width + 'x' + screen.height,
            availableScreen: screen.availWidth + 'x' + screen.availHeight
        });
        
        // Test resize simulation
        logger.log('Simulating resize event for testing', 'BookingFormTest', 'debug');
        window.dispatchEvent(new Event('resize'));
    }
    
    // Monitor for actual booking form height issues
    function monitorBookingFormIssues() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const target = mutation.target;
                    if (target.tagName === 'IFRAME' && target.classList.contains('easyappointments-iframe')) {
                        logger.logHeightChange(
                            mutation.oldValue || 'unknown',
                            target.style.cssText,
                            'MutationObserver detected iframe style change'
                        );
                    }
                }
            });
        });
        
        // Observe all iframes for style changes
        document.querySelectorAll('iframe').forEach(function(iframe) {
            observer.observe(iframe, {
                attributes: true,
                attributeFilter: ['style'],
                attributeOldValue: true
            });
        });
        
        logger.log('Started monitoring iframe style changes', 'BookingFormMonitor', 'info', {
            monitoredIframes: document.querySelectorAll('iframe').length
        });
    }
    
    // Start monitoring after initial tests
    setTimeout(monitorBookingFormIssues, 5000);
    
    // Periodic status reporting
    setInterval(function() {
        const iframes = document.querySelectorAll('.easyappointments-iframe, .ochd-integrated-booking-iframe');
        if (iframes.length > 0) {
            iframes.forEach(function(iframe, index) {
                const computedStyle = window.getComputedStyle(iframe);
                logger.logBookingHeight('Periodic iframe status check', {
                    iframeIndex: index,
                    id: iframe.id,
                    currentHeight: computedStyle.height,
                    currentWidth: computedStyle.width,
                    scrollHeight: iframe.scrollHeight || 'unavailable',
                    clientHeight: iframe.clientHeight || 'unavailable',
                    offsetHeight: iframe.offsetHeight || 'unavailable',
                    src: iframe.src,
                    timestamp: Date.now()
                });
            });
        }
    }, 30000); // Every 30 seconds
    
    // Log page visibility changes (might affect iframe behavior)
    document.addEventListener('visibilitychange', function() {
        logger.log('Page visibility changed', 'PageVisibility', 'debug', {
            hidden: document.hidden,
            visibilityState: document.visibilityState,
            timestamp: Date.now()
        });
    });
    
    // Log focus/blur events on iframes
    document.addEventListener('focusin', function(e) {
        if (e.target.tagName === 'IFRAME') {
            logger.logIframeEvent('Iframe received focus', {
                iframeId: e.target.id,
                src: e.target.src,
                timestamp: Date.now()
            });
        }
    });
    
    document.addEventListener('focusout', function(e) {
        if (e.target.tagName === 'IFRAME') {
            logger.logIframeEvent('Iframe lost focus', {
                iframeId: e.target.id,
                src: e.target.src,
                timestamp: Date.now()
            });
        }
    });
    
    // Monitor for network issues that might affect iframe loading
    window.addEventListener('online', function() {
        logger.log('Network connection restored', 'NetworkStatus', 'info', {
            timestamp: Date.now()
        });
    });
    
    window.addEventListener('offline', function() {
        logger.log('Network connection lost', 'NetworkStatus', 'warn', {
            timestamp: Date.now()
        });
    });
    
    logger.log('Debug logging test script fully initialized', 'DebugTest', 'info', {
        testingEnabled: true,
        monitoringEnabled: true,
        periodicReporting: true,
        eventListeners: true
    });
});
