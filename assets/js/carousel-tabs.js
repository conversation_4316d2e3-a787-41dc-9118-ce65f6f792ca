/**
 * OCHandyDude Carousel Tabs Script
 * Enhanced carousel-style tab navigation with smooth transitions and mobile swipe support
 */

class OCHandyDudeCarouselTabs {
    constructor() {
        this.currentTab = 'profile';
        this.isTransitioning = false;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.touchStartY = 0;
        this.touchEndY = 0;
        this.swipeThreshold = 50;
        this.swipeVelocityThreshold = 0.3;
        this.swipeStartTime = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupMobileSwipe();
        this.setupHashNavigation();
        this.setupMenuIntegration();
        this.updateIndicator();
        this.loadTabContent();
        console.log('OCHandyDude Carousel Tabs: Initialized');
    }

    bindEvents() {
        // Tab click events
        const tabs = document.querySelectorAll('.ochd-carousel-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e));
            
            // Keyboard navigation
            tab.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.switchTab(e);
                }
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    e.preventDefault();
                    this.navigateWithKeyboard(e.key);
                }
            });
        });

        // Window resize handler for responsive adjustments
        window.addEventListener('resize', () => this.updateIndicator());
    }

    setupMobileSwipe() {
        const container = document.querySelector('.ochd-unified-profile-container');
        if (!container) return;

        // Touch events for mobile swipe
        container.addEventListener('touchstart', (e) => {
            this.touchStartX = e.changedTouches[0].screenX;
            this.touchStartY = e.changedTouches[0].screenY;
            this.swipeStartTime = Date.now();

            // Add visual feedback
            container.classList.add('ochd-swiping');
        }, { passive: true });

        container.addEventListener('touchmove', (e) => {
            // Prevent vertical scrolling during horizontal swipe
            const deltaX = Math.abs(e.changedTouches[0].screenX - this.touchStartX);
            const deltaY = Math.abs(e.changedTouches[0].screenY - this.touchStartY);

            // Only prevent default if this is clearly a horizontal swipe
            if (deltaX > deltaY && deltaX > 15) {
                e.preventDefault();
            }
        }, { passive: false });

        container.addEventListener('touchend', (e) => {
            this.touchEndX = e.changedTouches[0].screenX;
            this.touchEndY = e.changedTouches[0].screenY;
            this.handleSwipe();

            // Remove visual feedback
            container.classList.remove('ochd-swiping');
        }, { passive: true });

        container.addEventListener('touchcancel', () => {
            container.classList.remove('ochd-swiping');
        }, { passive: true });

        // Mouse events for desktop drag (only on carousel tabs, not content)
        const carouselTabs = document.querySelector('.ochd-carousel-tabs');
        if (carouselTabs) {
            let isMouseDown = false;
            let mouseStartX = 0;
            let mouseStartTime = 0;
            let isDragging = false;

            carouselTabs.addEventListener('mousedown', (e) => {
                // Only enable drag on the tabs themselves, not on text content
                if (e.target.closest('.ochd-carousel-tab')) {
                    isMouseDown = true;
                    mouseStartX = e.clientX;
                    mouseStartTime = Date.now();
                    isDragging = false;

                    // Prevent text selection only during potential drag
                    e.preventDefault();
                }
            });

            carouselTabs.addEventListener('mousemove', (e) => {
                if (!isMouseDown) return;

                const deltaX = Math.abs(e.clientX - mouseStartX);
                const deltaTime = Date.now() - mouseStartTime;

                // Only consider it dragging if moved more than 10px
                if (deltaX > 10) {
                    isDragging = true;
                    carouselTabs.style.cursor = 'grabbing';
                    e.preventDefault();
                }
            });

            carouselTabs.addEventListener('mouseup', (e) => {
                if (!isMouseDown) return;

                isMouseDown = false;
                carouselTabs.style.cursor = 'default';

                // Only process swipe if we were actually dragging
                if (isDragging) {
                    const mouseEndX = e.clientX;
                    const deltaX = mouseStartX - mouseEndX;
                    const deltaTime = Date.now() - mouseStartTime;

                    // Require minimum distance and reasonable time for swipe
                    if (Math.abs(deltaX) > this.swipeThreshold && deltaTime < 1000) {
                        if (deltaX > 0 && this.currentTab === 'profile') {
                            this.switchToTab('bookings');
                        } else if (deltaX < 0 && this.currentTab === 'bookings') {
                            this.switchToTab('profile');
                        }
                    }
                }

                isDragging = false;
            });

            carouselTabs.addEventListener('mouseleave', () => {
                isMouseDown = false;
                isDragging = false;
                carouselTabs.style.cursor = 'default';
            });
        }
    }

    handleSwipe() {
        const deltaX = this.touchStartX - this.touchEndX;
        const deltaY = this.touchStartY - this.touchEndY;
        const swipeTime = Date.now() - this.swipeStartTime;
        const velocity = Math.abs(deltaX) / swipeTime;

        // Only process horizontal swipes
        if (Math.abs(deltaY) > Math.abs(deltaX)) {
            return;
        }

        // Check if swipe meets threshold (distance or velocity)
        const meetsThreshold = Math.abs(deltaX) > this.swipeThreshold || velocity > this.swipeVelocityThreshold;

        if (meetsThreshold) {
            if (deltaX > 0 && this.currentTab === 'profile') {
                // Swipe left - go to bookings
                this.switchToTab('bookings');
                this.showSwipeFeedback('left');
            } else if (deltaX < 0 && this.currentTab === 'bookings') {
                // Swipe right - go to profile
                this.switchToTab('profile');
                this.showSwipeFeedback('right');
            }
        }
    }

    showSwipeFeedback(direction) {
        const container = document.querySelector('.ochd-unified-profile-container');
        if (!container) return;

        const feedback = document.createElement('div');
        feedback.className = 'ochd-swipe-feedback';
        feedback.textContent = direction === 'left' ? '→' : '←';
        feedback.style.cssText = `
            position: fixed;
            top: 50%;
            ${direction === 'left' ? 'right: 20px;' : 'left: 20px;'}
            transform: translateY(-50%);
            font-size: 2rem;
            color: var(--ochd-primary-color);
            opacity: 0.8;
            pointer-events: none;
            z-index: 1000;
            animation: swipeFeedback 0.6s ease-out forwards;
        `;

        document.body.appendChild(feedback);

        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 600);
    }

    switchTab(event) {
        if (this.isTransitioning) return;
        
        const tab = event.currentTarget;
        const targetTab = tab.getAttribute('data-tab');
        
        if (targetTab === this.currentTab) return;
        
        this.switchToTab(targetTab);
    }

    switchToTab(targetTab) {
        if (this.isTransitioning || targetTab === this.currentTab) return;
        
        this.isTransitioning = true;
        
        // Update tab states
        this.updateTabStates(targetTab);
        
        // Animate content transition
        this.animateContentTransition(targetTab);
        
        // Update indicator
        this.updateIndicator();
        
        // Load content for the new tab
        this.loadTabContent(targetTab);
        
        this.currentTab = targetTab;

        // Update URL hash if not already set
        const currentHash = window.location.hash.substring(1);
        if (currentHash !== targetTab) {
            window.history.replaceState(null, null, `#${targetTab}`);
        }

        // Reset transition flag after animation
        setTimeout(() => {
            this.isTransitioning = false;
        }, 500);

        console.log(`OCHandyDude Carousel: Switched to ${targetTab} tab`);
    }

    updateTabStates(activeTab) {
        const tabs = document.querySelectorAll('.ochd-carousel-tab');
        const contents = document.querySelectorAll('.ochd-carousel-content');
        
        tabs.forEach(tab => {
            const tabName = tab.getAttribute('data-tab');
            const isActive = tabName === activeTab;
            
            tab.classList.toggle('active', isActive);
            tab.setAttribute('aria-selected', isActive ? 'true' : 'false');
            
            // Add transition classes for carousel effect
            if (isActive) {
                tab.classList.add('carousel-active');
                tab.classList.remove('carousel-inactive');
            } else {
                tab.classList.add('carousel-inactive');
                tab.classList.remove('carousel-active');
            }
        });
        
        contents.forEach(content => {
            const contentId = content.id;
            const isActive = contentId === activeTab;
            
            content.classList.toggle('active', isActive);
        });
    }

    animateContentTransition(targetTab) {
        const currentContent = document.querySelector('.ochd-carousel-content.active');
        const targetContent = document.getElementById(targetTab);
        
        if (!currentContent || !targetContent) return;
        
        // Slide out current content
        currentContent.style.transform = 'translateX(-100%)';
        currentContent.style.opacity = '0';
        
        // Prepare target content
        targetContent.style.transform = 'translateX(100%)';
        targetContent.style.opacity = '0';
        targetContent.classList.add('active');
        
        // Animate target content in
        setTimeout(() => {
            targetContent.style.transform = 'translateX(0)';
            targetContent.style.opacity = '1';
        }, 100);
        
        // Clean up after animation
        setTimeout(() => {
            if (currentContent !== targetContent) {
                currentContent.classList.remove('active');
                currentContent.style.transform = '';
                currentContent.style.opacity = '';
            }
        }, 500);
    }

    updateIndicator() {
        const indicator = document.querySelector('.ochd-carousel-indicator');
        const activeTab = document.querySelector('.ochd-carousel-tab.active');
        const carouselTabs = document.querySelector('.ochd-carousel-tabs');

        if (!indicator || !activeTab || !carouselTabs) return;

        // Get positions relative to the carousel tabs container
        const tabRect = activeTab.getBoundingClientRect();
        const carouselRect = carouselTabs.getBoundingClientRect();

        // Calculate position relative to the carousel tabs container
        const left = tabRect.left - carouselRect.left;
        const width = tabRect.width;

        // Apply the position
        indicator.style.left = `${left}px`;
        indicator.style.width = `${width}px`;

        console.log(`OCHandyDude Carousel: Updated indicator - left: ${left}px, width: ${width}px`);
    }

    navigateWithKeyboard(key) {
        if (key === 'ArrowRight' && this.currentTab === 'profile') {
            this.switchToTab('bookings');
        } else if (key === 'ArrowLeft' && this.currentTab === 'bookings') {
            this.switchToTab('profile');
        }
    }

    setupHashNavigation() {
        // Handle initial hash on page load
        const hash = window.location.hash.substring(1);
        if (hash && (hash === 'profile' || hash === 'bookings')) {
            this.switchToTab(hash);
        }

        // Handle hash changes (back/forward navigation)
        window.addEventListener('hashchange', () => {
            const newHash = window.location.hash.substring(1);
            if (newHash && (newHash === 'profile' || newHash === 'bookings')) {
                this.switchToTab(newHash);
            }
        });
    }

    setupMenuIntegration() {
        // Handle clicks from profile menu dropdown
        document.addEventListener('click', (e) => {
            const link = e.target.closest('[data-carousel-tab]');
            if (link) {
                e.preventDefault();
                const targetTab = link.getAttribute('data-carousel-tab');
                this.switchToTab(targetTab);

                // Update URL hash
                window.history.pushState(null, null, `#${targetTab}`);

                // Close profile dropdown if open
                const profileMenu = document.querySelector('.ochd-profile-menu');
                if (profileMenu) {
                    profileMenu.classList.remove('is-active');
                }
            }
        });
    }

    loadTabContent(tabName = this.currentTab) {
        if (tabName === 'bookings') {
            // Initialize Invoice Ninja integration if available
            if (window.invoiceNinjaIntegration) {
                window.invoiceNinjaIntegration.init();
            }

            // Style Invoice Ninja button to match profile button
            this.styleInvoiceNinjaButton();
        }
    }

    styleInvoiceNinjaButton() {
        // Wait for Invoice Ninja button to be rendered
        setTimeout(() => {
            const invoiceButton = document.querySelector('.ochd-custom-portal-button-wrapper a, .ochd-custom-portal-button-wrapper .invoiceninja-client-portal-button');
            if (invoiceButton) {
                // Ensure consistent styling
                invoiceButton.classList.add('ochd-styled-portal-button');

                // Clear existing content to rebuild it properly
                const originalText = invoiceButton.textContent.trim();
                invoiceButton.innerHTML = '';

                // Add icon
                const icon = document.createElement('span');
                icon.className = 'ochd-button-icon';
                icon.textContent = '💳';
                invoiceButton.appendChild(icon);

                // Add text
                const text = document.createElement('span');
                text.className = 'ochd-button-text';
                text.textContent = originalText || 'Access Client Portal';
                invoiceButton.appendChild(text);

                // Ensure the button has the exact same classes and structure as profile button
                invoiceButton.style.cssText = '';  // Clear any inline styles

                console.log('OCHandyDude Carousel: Invoice Ninja button styled to match profile button');
            } else {
                console.log('OCHandyDude Carousel: Invoice Ninja button not found, retrying...');
                // Retry after a longer delay
                setTimeout(() => this.styleInvoiceNinjaButton(), 1000);
            }
        }, 500);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if carousel tabs exist
    if (document.querySelector('.ochd-carousel-tabs')) {
        window.ochdCarouselTabs = new OCHandyDudeCarouselTabs();
    }
});

// Make globally available for debugging
window.OCHandyDudeCarouselTabs = OCHandyDudeCarouselTabs;
