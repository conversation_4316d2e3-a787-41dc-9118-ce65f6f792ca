/* OCHandyDude Profile Menu Styles v4.1 (Enhanced with Improved Animations) */

:root {
    --ochd-bg-light: rgba(255, 255, 255, 0.85);
    --ochd-bg-dark: rgba(40, 40, 40, 0.85);
    --ochd-text-light: #333;
    --ochd-text-dark: #eee;
    --ochd-border-light: #e5e5e5;
    --ochd-border-dark: #555;
    --ochd-hover-light: #f0f0f0;
    --ochd-hover-dark: rgba(255, 255, 255, 0.1);
    --ochd-primary-color: #0073e6;
}

/* --- Main profile menu container --- */
.ochd-profile-menu {
    position: relative;
}

/* --- Profile icon (SVG) styles --- */
.ochd-profile-icon {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}
/* SVG icon styles are inherited from mobile-menu.css for consistency */

/* Enhanced active state to match standardized hover effects */
.ochd-profile-menu.is-active .ochd-profile-icon svg {
    stroke: #007bff;
    transform: scale(1.1) rotate(90deg);
}

/* Active state background highlight */
.ochd-profile-menu.is-active {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
    transform: translateY(-1px);
}

/* --- Dropdown menu styles with proper interaction handling --- */
.ochd-profile-dropdown {
    display: block;
    visibility: hidden;
    opacity: 0;
    position: absolute;
    top: calc(100% + 15px); /* Spacing from icon */
    right: 0;
    min-width: 240px;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--ochd-border-light);
    background-color: var(--ochd-bg-light);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transform: translateY(-10px);
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                visibility 0.3s,
                pointer-events 0.3s;

    /* CRITICAL: Disable all mouse interactions when hidden */
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Dropdown becomes visible and interactive when profile menu has .is-active class */
.ochd-profile-menu.is-active .ochd-profile-dropdown {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);

    /* CRITICAL: Re-enable mouse interactions when visible */
    pointer-events: auto;
    user-select: auto;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
}

/* --- Dropdown menu content styles --- */
.ochd-profile-dropdown a {
    color: var(--ochd-text-light);
    padding: 12px 18px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-align: left;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 6px;
    margin: 2px 8px;
    position: relative;
    overflow: hidden;
}

.ochd-profile-dropdown a:hover {
    background-color: var(--ochd-hover-light);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Menu link icons and text */
.ochd-menu-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
    transition: transform 0.3s ease;
}

.ochd-menu-text {
    flex: 1;
    transition: transform 0.3s ease;
    font-weight: 500;
}

/* Unified styling for all menu items - removed special highlighting for consistency */
/* Enhanced dropdown header with responsive text and ellipsis */
.ochd-dropdown-header {
    padding: 16px 18px;
    border-bottom: 1px solid var(--ochd-border-light);
    min-width: 0; /* Allow shrinking */
    max-width: 280px; /* Reasonable maximum width */
}

.ochd-dropdown-header strong {
    display: block;
    font-size: 15px;
    color: var(--ochd-text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
    margin-bottom: 2px;
    /* Responsive font sizing */
    font-size: clamp(13px, 2.5vw, 15px);
}

.ochd-dropdown-header small {
    color: #777;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
    /* Responsive font sizing */
    font-size: clamp(11px, 2vw, 13px);
}
.ochd-dropdown-footer { border-top: 1px solid var(--ochd-border-light); }
.ochd-guest-menu { padding: 15px; display: flex; }
.ochd-guest-login-button { flex-grow: 1; display: block; box-sizing: border-box; padding: 12px; border-radius: 8px; background-color: var(--ochd-primary-color); color: white !important; text-align: center; font-weight: 500; transition: all 0.2s ease-out; }
.ochd-guest-login-button:hover { background-color: #338dff; color: white !important; transform: scale(1.03); }

/* --- Dark mode styles --- */
@media (prefers-color-scheme: dark) {
    .ochd-profile-dropdown { background-color: var(--ochd-bg-dark); border-color: var(--ochd-border-dark); }
    .ochd-profile-dropdown a { color: var(--ochd-text-dark); }
    .ochd-profile-dropdown a:hover {
        background-color: var(--ochd-hover-dark);
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
    }
    .ochd-dropdown-header, .ochd-dropdown-footer { border-color: var(--ochd-border-dark); }
    .ochd-dropdown-header strong { color: var(--ochd-text-dark); }
    .ochd-dropdown-header small { color: #aaa; }

    /* Dark mode menu items - uniform styling for all items */
}

/* ================================================================= */
/* --- COMPREHENSIVE MY PROFILE PAGE STYLING --- */
/* ================================================================= */

/* Main profile and bookings container with modern layout */
.ochd-profile-container,
.ochd-bookings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--ochd-text-light);
}

/* Profile and bookings page main heading - consistent styling */
.ochd-profile-container h2,
.ochd-bookings-container h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ochd-primary-color);
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
}

.ochd-profile-container h2::after,
.ochd-bookings-container h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--ochd-primary-color), #007bff);
    border-radius: 2px;
}

/* Enhanced profile cards with modern design */
.ochd-profile-card {
    background: var(--ochd-bg-light);
    border: 1px solid var(--ochd-border-light);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.ochd-profile-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--ochd-primary-color), #007bff, #6f42c1);
    border-radius: 16px 16px 0 0;
}

.ochd-profile-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 123, 255, 0.3);
}

/* Profile card headings */
.ochd-profile-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ochd-primary-color);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ochd-profile-card h3::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--ochd-primary-color);
    border-radius: 50%;
    flex-shrink: 0;
}

/* Profile information styling */
.ochd-profile-card p {
    margin-bottom: 1rem;
    font-size: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.ochd-profile-card p:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.ochd-profile-card p:hover {
    background-color: rgba(0, 123, 255, 0.02);
    border-radius: 8px;
    padding-left: 1rem;
    padding-right: 1rem;
}

.ochd-profile-card p strong {
    color: var(--ochd-primary-color);
    font-weight: 600;
    min-width: 100px;
    flex-shrink: 0;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Address formatting */
.ochd-profile-card p br + * {
    margin-left: 1rem;
    color: #666;
}

/* External link styling */
.ochd-profile-container > p {
    text-align: center;
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(111, 66, 193, 0.1));
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.ochd-profile-container > p a {
    color: var(--ochd-primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
}

.ochd-profile-container > p a::after {
    content: '→';
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.ochd-profile-container > p a:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 123, 255, 0.3);
    background: rgba(255, 255, 255, 0.8);
}

.ochd-profile-container > p a:hover::after {
    transform: translateX(4px);
}

/* ================================================================= */
/* --- ENHANCED TAB STYLING FOR BOOKINGS PAGE --- */
/* ================================================================= */

.ochd-tabs {
    background: var(--ochd-bg-light);
    border: 1px solid var(--ochd-border-light);
    border-radius: 12px 12px 0 0;
    padding: 0;
    margin-bottom: 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
}

.ochd-tab-link {
    background-color: transparent;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 1rem 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1rem;
    font-weight: 500;
    color: var(--ochd-text-light);
    border-right: 1px solid var(--ochd-border-light);
    position: relative;
    flex: 1;
    text-align: center;
    min-width: 120px;
}

.ochd-tab-link:last-child {
    border-right: none;
}

.ochd-tab-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--ochd-primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.ochd-tab-link:hover {
    background-color: var(--ochd-hover-light);
    color: var(--ochd-primary-color);
    transform: translateY(-1px);
}

.ochd-tab-link.active {
    background-color: var(--ochd-primary-color);
    color: white;
    font-weight: 600;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ochd-tab-link.active::before {
    transform: scaleX(1);
    background: white;
}

.ochd-tab-content {
    display: none;
    background: var(--ochd-bg-light);
    border: 1px solid var(--ochd-border-light);
    border-top: none;
    border-radius: 0 0 12px 12px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    min-height: 300px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ================================================================= */
/* --- RESPONSIVE DESIGN --- */
/* ================================================================= */

@media (max-width: 768px) {
    .ochd-unified-profile-container,
    .ochd-profile-container,
    .ochd-bookings-container {
        padding: 20px 15px;
    }

    /* Responsive carousel tabs */
    .ochd-carousel-track {
        gap: 1rem;
        padding: 0.75rem 1.5rem;
        border-radius: 40px;
    }

    .ochd-carousel-tab {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        min-width: 140px;
        gap: 0.5rem;
    }

    .ochd-tab-icon {
        font-size: 1.1rem;
    }

    .ochd-carousel-tab:not(.active) {
        transform: scale(0.92) translateZ(-10px);
    }

    .ochd-carousel-tab.active {
        transform: scale(1.03) translateZ(10px);
    }

    .ochd-carousel-tab:hover:not(.active) {
        transform: scale(0.95) translateZ(-5px);
    }

    .ochd-carousel-tab.active:hover {
        transform: scale(1.05) translateZ(15px);
    }

    .ochd-profile-edit-button {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
        gap: 0.5rem;
    }

    /* Custom billing portal button inherits mobile styling from ochd-profile-edit-button */

    .ochd-profile-container h2,
    .ochd-bookings-container h2 {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .ochd-profile-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
    }

    .ochd-profile-card h3 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .ochd-profile-card p {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .ochd-profile-card p strong {
        min-width: auto;
        margin-bottom: 0.25rem;
    }

    .ochd-tabs {
        flex-direction: column;
    }

    .ochd-tab-link {
        border-right: none;
        border-bottom: 1px solid var(--ochd-border-light);
        flex: none;
    }

    .ochd-tab-link:last-child {
        border-bottom: none;
    }

    .ochd-tab-content {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .ochd-unified-profile-container {
        padding: 15px 10px;
    }

    /* Mobile carousel tabs */
    .ochd-carousel-tabs {
        margin-bottom: 2rem;
    }

    .ochd-carousel-track {
        gap: 0.75rem;
        padding: 0.5rem 1rem;
        border-radius: 30px;
        flex-direction: column;
        align-items: center;
    }

    .ochd-carousel-tab {
        padding: 0.75rem 1.25rem;
        font-size: 0.95rem;
        min-width: 120px;
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }

    .ochd-tab-text {
        font-size: 0.9rem;
    }

    .ochd-carousel-indicator {
        display: none; /* Hide indicator on mobile for cleaner look */
    }

    /* Enable swipe gestures on mobile */
    .ochd-unified-profile-container {
        touch-action: pan-y;
        -webkit-overflow-scrolling: touch;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .ochd-carousel-content {
        min-height: 300px;
        touch-action: pan-y;
    }

    /* Mobile swipe indicator */
    .ochd-carousel-tabs::after {
        content: '← Swipe →';
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.8rem;
        color: #6c757d;
        opacity: 0.7;
        font-style: italic;
    }

    /* Hide swipe indicator on larger screens */
    @media (min-width: 769px) {
        .ochd-carousel-tabs::after {
            display: none;
        }
    }

    /* Enhanced mobile button styling */
    .ochd-profile-edit-button {
        min-height: 48px !important; /* Minimum touch target size */
        touch-action: manipulation !important;
    }

    /* Mobile dropdown header adjustments */
    .ochd-dropdown-header {
        padding: 12px 16px;
        max-width: 250px;
    }

    .ochd-dropdown-header strong {
        font-size: 13px;
    }

    .ochd-dropdown-header small {
        font-size: 11px;
    }

    .ochd-profile-container h2,
    .ochd-bookings-container h2 {
        font-size: 1.75rem;
    }

    .ochd-profile-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .ochd-profile-card h3 {
        font-size: 1.2rem;
    }

    .ochd-tab-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .ochd-tab-content {
        padding: 1rem;
    }
}

/* ================================================================= */
/* --- VERY NARROW SCREENS (320px-480px) --- */
/* ================================================================= */

@media (max-width: 480px) {
    /* Keep carousel tabs in horizontal row on narrow screens */
    .ochd-carousel-track {
        flex-direction: row !important;
        gap: 0.25rem !important;
        padding: 0.5rem 0.75rem !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE/Edge */
    }

    /* Hide scrollbar for webkit browsers */
    .ochd-carousel-track::-webkit-scrollbar {
        display: none !important;
    }

    .ochd-carousel-tab {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.85rem !important;
        min-width: 80px !important;
        width: auto !important;
        max-width: none !important;
        flex-shrink: 0 !important;
        white-space: nowrap !important;
    }

    .ochd-tab-icon {
        font-size: 0.9rem !important;
    }

    .ochd-tab-text {
        font-size: 0.8rem !important;
    }

    /* Adjust transforms for smaller tabs */
    .ochd-carousel-tab:not(.active) {
        transform: scale(0.9) translateZ(-5px) !important;
    }

    .ochd-carousel-tab.active {
        transform: scale(1.0) translateZ(5px) !important;
    }

    .ochd-carousel-tab:hover:not(.active) {
        transform: scale(0.92) translateZ(-2px) !important;
    }

    .ochd-carousel-tab.active:hover {
        transform: scale(1.02) translateZ(8px) !important;
    }
}

/* ================================================================= */
/* --- EXTRA NARROW SCREENS (320px and below) --- */
/* ================================================================= */

@media (max-width: 360px) {
    .ochd-carousel-tab {
        padding: 0.4rem 0.6rem !important;
        font-size: 0.8rem !important;
        min-width: 70px !important;
        gap: 0.25rem !important;
    }

    .ochd-tab-icon {
        font-size: 0.85rem !important;
    }

    .ochd-tab-text {
        font-size: 0.75rem !important;
    }

    /* Even more compact spacing */
    .ochd-carousel-track {
        gap: 0.15rem !important;
        padding: 0.4rem 0.5rem !important;
    }
}

/* ================================================================= */
/* --- LANDSCAPE MOBILE ORIENTATION --- */
/* ================================================================= */

@media (max-width: 768px) and (orientation: landscape) {
    .ochd-carousel-track {
        flex-direction: row !important;
        gap: 0.5rem !important;
        padding: 0.4rem 1rem !important;
    }

    .ochd-carousel-tab {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
        min-width: 90px !important;
        width: auto !important;
        max-width: none !important;
    }
}

/* ================================================================= */
/* --- CAROUSEL TAB NAVIGATION SYSTEM --- */
/* ================================================================= */

/* Unified profile container */
.ochd-unified-profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--ochd-text-light);
}

/* Carousel tabs container */
.ochd-carousel-tabs {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

/* Carousel track */
.ochd-carousel-track {
    display: flex;
    gap: 2rem;
    position: relative;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
    border-radius: 50px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Individual carousel tab */
.ochd-carousel-tab {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: transparent;
    border: none;
    border-radius: 40px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1.1rem;
    font-weight: 600;
    color: #6c757d;
    position: relative;
    overflow: hidden;
    min-width: 160px;
    justify-content: center;
    z-index: 2;
}

/* Tab icon */
.ochd-tab-icon {
    font-size: 1.3rem;
    transition: all 0.3s ease;
    filter: grayscale(0.3);
}

/* Tab text */
.ochd-tab-text {
    transition: all 0.3s ease;
    white-space: nowrap;
}

/* Inactive tab state (dimmed, behind) */
.ochd-carousel-tab:not(.active) {
    opacity: 0.6;
    transform: scale(0.95) translateZ(-10px);
    color: #8e9297;
    background: rgba(108, 117, 125, 0.05);
}

.ochd-carousel-tab:not(.active) .ochd-tab-icon {
    filter: grayscale(0.7);
    opacity: 0.7;
}

/* Active tab state (prominent, in front) */
.ochd-carousel-tab.active {
    opacity: 1;
    transform: scale(1.05) translateZ(10px);
    color: white;
    background: linear-gradient(135deg, var(--ochd-primary-color), #0056b3);
    box-shadow:
        0 8px 24px rgba(0, 123, 255, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 3;
}

.ochd-carousel-tab.active .ochd-tab-icon {
    filter: grayscale(0);
    opacity: 1;
    transform: scale(1.1);
}

.ochd-carousel-tab.active .ochd-tab-text {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Hover effects */
.ochd-carousel-tab:hover:not(.active) {
    opacity: 0.8;
    transform: scale(0.98) translateZ(-5px);
    background: rgba(0, 123, 255, 0.1);
    color: var(--ochd-primary-color);
}

.ochd-carousel-tab:hover:not(.active) .ochd-tab-icon {
    filter: grayscale(0.3);
    opacity: 0.9;
}

.ochd-carousel-tab.active:hover {
    transform: scale(1.08) translateZ(15px);
    box-shadow:
        0 12px 32px rgba(0, 123, 255, 0.5),
        0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Carousel indicator */
.ochd-carousel-indicator {
    position: absolute;
    bottom: -15px;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--ochd-primary-color), #0056b3);
    border-radius: 2px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.4);
    transform-origin: left center;
    z-index: 1;
}

/* Carousel content containers */
.ochd-carousel-content {
    display: none;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 400px;
}

.ochd-carousel-content.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

/* Swipe feedback animations */
@keyframes swipeFeedback {
    0% {
        opacity: 0;
        transform: translateY(-50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-50%) scale(1) translateX(20px);
    }
}

/* Swiping state */
.ochd-unified-profile-container.ochd-swiping {
    cursor: grabbing;
}

.ochd-unified-profile-container.ochd-swiping .ochd-carousel-content {
    transition: none;
}

/* Prevent text selection during carousel tab interactions */
.ochd-carousel-tabs {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection in content areas */
.ochd-carousel-content {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* ================================================================= */
/* --- TASK/INVOICE EXPANSION FUNCTIONALITY --- */
/* ================================================================= */

/* Task and invoice card headers with expand buttons */
.ochd-task-header,
.ochd-invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.ochd-task-header-actions,
.ochd-invoice-header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Expand button styling */
.ochd-expand-btn {
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.3);
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--ochd-primary-color);
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ochd-expand-btn:hover {
    background: rgba(0, 123, 255, 0.2);
    transform: scale(1.05);
}

.ochd-expand-btn.expanded {
    background: var(--ochd-primary-color);
    color: white;
    transform: rotate(180deg);
}

/* Expansion panel styling */
.ochd-expansion-panel {
    margin-top: 1rem;
    border-top: 1px solid rgba(0, 123, 255, 0.2);
    padding-top: 1rem;
    animation: expandIn 0.3s ease-out;
}

.ochd-expansion-header h5 {
    margin: 0 0 0.75rem 0;
    color: var(--ochd-primary-color);
    font-size: 0.95rem;
    font-weight: 600;
}

.ochd-expansion-content {
    background: rgba(0, 123, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

/* Expansion content layouts */
.ochd-expansion-invoice-summary,
.ochd-expansion-task-summary {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ochd-expansion-task-item {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    border: 1px solid rgba(0, 123, 255, 0.1);
    margin-bottom: 0.5rem;
}

.ochd-expansion-task-item:last-child {
    margin-bottom: 0;
}

.ochd-expansion-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(0, 123, 255, 0.1);
}

.ochd-expansion-row:last-child {
    border-bottom: none;
}

.ochd-expansion-label {
    font-weight: 500;
    color: #666;
    font-size: 0.9rem;
}

.ochd-expansion-value {
    font-weight: 600;
    color: var(--ochd-text-light);
    text-align: right;
}

.ochd-no-data {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 1rem;
}

/* Animation for expansion */
@keyframes expandIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card expanded state */
.ochd-task-card.expanded,
.ochd-invoice-card.expanded {
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2);
    border-color: rgba(0, 123, 255, 0.3);
}

/* ================================================================= */
/* --- EASY!APPOINTMENTS BOOKING FORM INTEGRATION --- */
/* ================================================================= */

/* Ensure booking forms work well within unified profile carousel */
.ochd-carousel-content .ochd-booking-container {
    margin: 0;
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.ochd-carousel-content .ochd-booking-form-wrapper {
    border-radius: 12px;
}

.ochd-carousel-content .ochd-booking-form-wrapper iframe.easyappointments-iframe {
    border-radius: 12px;
    /* Ensure iframe fits within carousel content */
    max-height: calc(100vh - 200px);
}

/* Mobile adjustments for booking form within carousel */
@media (max-width: 768px) {
    .ochd-carousel-content .ochd-booking-container {
        margin: 0 -10px;
    }

    .ochd-carousel-content .ochd-booking-form-wrapper iframe.easyappointments-iframe {
        /* Adjust for carousel tab height */
        height: calc(100vh - 180px) !important;
        max-height: calc(100vh - 180px) !important;
    }

    .ochd-carousel-content #ochd-guest-overlay {
        height: calc(100vh - 180px) !important;
        max-height: calc(100vh - 180px) !important;
    }
}

/* Profile actions styling */
.ochd-profile-actions {
    text-align: center;
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(111, 66, 193, 0.1));
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.ochd-profile-edit-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
    color: var(--ochd-primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.ochd-profile-edit-button:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 123, 255, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 249, 250, 0.95));
    text-decoration: none;
    color: var(--ochd-primary-color);
}

.ochd-button-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.ochd-profile-edit-button:hover .ochd-button-icon {
    transform: rotate(90deg) scale(1.1);
}

/* Custom Invoice Ninja button wrapper */
.ochd-custom-portal-button-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Custom billing portal button - inherits from ochd-profile-edit-button, no additional border needed */
.ochd-custom-client-portal-button {
    /* Button already inherits all styling from ochd-profile-edit-button class */
    /* No additional border or styling needed to prevent double border */
}

/* Custom billing portal button inherits all styling from ochd-profile-edit-button */

/* Custom billing portal button uses clean ochd-profile-edit-button styling */

/* ================================================================= */
/* --- COMPREHENSIVE DARK MODE SUPPORT --- */
/* ================================================================= */

@media (prefers-color-scheme: dark) {
    .ochd-unified-profile-container,
    .ochd-profile-container,
    .ochd-bookings-container {
        color: var(--ochd-text-dark);
    }

    /* Dark mode carousel tabs */
    .ochd-carousel-track {
        background: linear-gradient(135deg, rgba(40, 40, 40, 0.9), rgba(33, 37, 41, 0.8));
        border-color: rgba(77, 166, 255, 0.2);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .ochd-carousel-tab:not(.active) {
        color: #adb5bd;
        background: rgba(77, 166, 255, 0.08);
    }

    .ochd-carousel-tab:not(.active) .ochd-tab-icon {
        filter: grayscale(0.8);
        opacity: 0.6;
    }

    .ochd-carousel-tab.active {
        background: linear-gradient(135deg, #4da6ff, #007bff);
        color: #1a1a1a;
        box-shadow:
            0 8px 24px rgba(77, 166, 255, 0.5),
            0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .ochd-carousel-tab:hover:not(.active) {
        background: rgba(77, 166, 255, 0.15);
        color: #4da6ff;
    }

    .ochd-carousel-indicator {
        background: linear-gradient(90deg, #4da6ff, #007bff);
        box-shadow: 0 2px 8px rgba(77, 166, 255, 0.5);
    }

    /* Dark mode profile actions */
    .ochd-profile-actions {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.15), rgba(138, 99, 210, 0.15));
        border-color: rgba(77, 166, 255, 0.3);
    }

    .ochd-profile-edit-button {
        background: linear-gradient(135deg, rgba(33, 37, 41, 0.9), rgba(40, 40, 40, 0.8));
        color: #4da6ff;
        border-color: rgba(77, 166, 255, 0.4);
        box-shadow: 0 4px 16px rgba(77, 166, 255, 0.3);
    }

    .ochd-profile-edit-button:hover {
        background: linear-gradient(135deg, rgba(33, 37, 41, 1), rgba(40, 40, 40, 0.95));
        box-shadow: 0 8px 24px rgba(77, 166, 255, 0.4);
        color: #4da6ff;
    }

    /* Custom billing portal button inherits dark mode styling from ochd-profile-edit-button */

    .ochd-profile-container h2,
    .ochd-bookings-container h2 {
        color: #4da6ff;
    }

    .ochd-profile-card {
        background: var(--ochd-bg-dark);
        border-color: var(--ochd-border-dark);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .ochd-profile-card::before {
        background: linear-gradient(90deg, #4da6ff, #007bff, #8a63d2);
    }

    .ochd-profile-card:hover {
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.4);
        border-color: rgba(77, 166, 255, 0.4);
    }

    .ochd-profile-card h3 {
        color: #4da6ff;
    }

    .ochd-profile-card h3::before {
        background: #4da6ff;
    }

    .ochd-profile-card p {
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .ochd-profile-card p:hover {
        background-color: rgba(77, 166, 255, 0.05);
    }

    .ochd-profile-card p strong {
        color: #4da6ff;
    }

    .ochd-profile-card p br + * {
        color: #aaa;
    }

    .ochd-profile-container > p {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.15), rgba(138, 99, 210, 0.15));
        border-color: rgba(77, 166, 255, 0.3);
    }

    .ochd-profile-container > p a {
        color: #4da6ff;
        background: rgba(0, 0, 0, 0.3);
    }

    .ochd-profile-container > p a:hover {
        background: rgba(0, 0, 0, 0.5);
        box-shadow: 0 8px 24px rgba(77, 166, 255, 0.3);
    }

    .ochd-tabs {
        background: var(--ochd-bg-dark);
        border-color: var(--ochd-border-dark);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .ochd-tab-link {
        color: var(--ochd-text-dark);
        border-color: var(--ochd-border-dark);
    }

    .ochd-tab-link:hover {
        background-color: var(--ochd-hover-dark);
        color: #4da6ff;
    }

    .ochd-tab-link.active {
        background-color: #4da6ff;
        color: #1a1a1a;
    }

    .ochd-tab-content {
        background: var(--ochd-bg-dark);
        border-color: var(--ochd-border-dark);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }
}

/* ================================================================= */
/* --- INVOICE NINJA INTEGRATION STYLES --- */
/* ================================================================= */

/* Loading states */
.ochd-loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: #6c757d;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.03), rgba(0, 123, 255, 0.01));
    border-radius: 16px;
    border: 1px solid rgba(0, 123, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.ochd-loading-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
}

.ochd-loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(0, 123, 255, 0.1);
    border-top: 4px solid var(--ochd-primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1.5rem;
    position: relative;
}

.ochd-loading-spinner::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top: 2px solid rgba(0, 123, 255, 0.3);
    animation: spin 0.8s linear infinite reverse;
}

.ochd-loading-state p {
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0;
    color: var(--ochd-primary-color);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Error states */
.ochd-error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    color: #dc3545;
}

.ochd-error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.ochd-retry-button {
    background-color: var(--ochd-primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.ochd-retry-button:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

/* Empty states */
.ochd-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    color: #666;
}

.ochd-empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Project cards */
.ochd-projects-section {
    margin-bottom: 2rem;
}

.ochd-project-card {
    background: var(--ochd-bg-light);
    border: 1px solid var(--ochd-border-light);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ochd-project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--ochd-primary-color), #007bff);
}

.ochd-project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.ochd-project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.ochd-project-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--ochd-primary-color);
    flex: 1;
}

.ochd-project-description {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.ochd-project-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #777;
}

.ochd-project-meta span {
    white-space: nowrap;
}

.ochd-project-tasks {
    border-top: 1px solid var(--ochd-border-light);
    padding-top: 1rem;
    margin-top: 1rem;
}

.ochd-project-tasks h5 {
    margin: 0 0 0.75rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--ochd-text-light);
}

.ochd-task-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ochd-task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: rgba(0, 123, 255, 0.05);
    border-radius: 6px;
    font-size: 0.9rem;
}

.ochd-task-item-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.ochd-task-name {
    color: var(--ochd-text-light);
    font-weight: 500;
}

.ochd-task-time-small {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
}

/* Bookings list container */
.ochd-bookings-list {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

/* Subcategory tabs */
.ochd-subcategory-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    padding: 0.5rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.1);
    backdrop-filter: blur(8px);
}

.ochd-subcategory-tab {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: #6c757d;
    font-weight: 500;
    font-size: 0.95rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ochd-subcategory-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.ochd-subcategory-tab:hover {
    background: rgba(0, 123, 255, 0.08);
    color: var(--ochd-primary-color);
    transform: translateY(-1px);
}

.ochd-subcategory-tab:hover::before {
    left: 100%;
}

.ochd-subcategory-tab.active {
    background: linear-gradient(135deg, var(--ochd-primary-color), #0056b3);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transform: translateY(-2px);
}

.ochd-subcategory-content {
    position: relative;
}

.ochd-subcategory-panel {
    display: none;
}

.ochd-subcategory-panel.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Section headers */
.ochd-section-header {
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 123, 255, 0.04));
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.15);
    position: relative;
    overflow: hidden;
}

.ochd-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.ochd-section-header h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--ochd-primary-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ochd-section-icon {
    font-size: 1.2rem;
    opacity: 0.8;
}

.ochd-section-count {
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    margin-left: auto;
}

.ochd-section-header p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95rem;
    font-style: italic;
}

/* Standalone tasks section (primary focus) */
.ochd-standalone-tasks-section {
    margin-bottom: 3rem;
}

.ochd-standalone-task-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    border: 1px solid rgba(0, 123, 255, 0.15);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow:
        0 8px 32px rgba(0, 123, 255, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.ochd-standalone-task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3, #004085);
    border-radius: 16px 16px 0 0;
}

.ochd-standalone-task-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(0, 123, 255, 0.03) 0%, transparent 70%);
    pointer-events: none;
}

.ochd-standalone-task-card:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow:
        0 16px 48px rgba(0, 123, 255, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 123, 255, 0.25);
}

/* Project groups section */
.ochd-project-groups-section {
    margin-top: 3rem;
    position: relative;
}

.ochd-project-groups-section::before {
    content: '';
    position: absolute;
    top: -1.5rem;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.3), transparent);
}

.ochd-project-group {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.95), rgba(255, 255, 255, 0.9));
    border: 1px solid rgba(108, 117, 125, 0.15);
    border-radius: 12px;
    margin-bottom: 1.5rem;
    box-shadow:
        0 4px 16px rgba(108, 117, 125, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    backdrop-filter: blur(8px);
}

.ochd-project-group:hover {
    box-shadow:
        0 8px 24px rgba(108, 117, 125, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
    border-color: rgba(0, 123, 255, 0.2);
}

.ochd-project-group-header {
    padding: 1.5rem 2rem;
    cursor: pointer;
    border-radius: 12px 12px 0 0;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 249, 250, 0.6));
    position: relative;
}

.ochd-project-group-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #6c757d, #495057);
    border-radius: 0 4px 4px 0;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.ochd-project-group-header:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 123, 255, 0.04));
}

.ochd-project-group-header:hover::before {
    background: linear-gradient(180deg, #007bff, #0056b3);
    opacity: 1;
    width: 6px;
}

.ochd-project-group-header.expanded {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.12), rgba(0, 123, 255, 0.06));
    border-bottom: 1px solid rgba(0, 123, 255, 0.15);
}

.ochd-project-group-header.expanded::before {
    background: linear-gradient(180deg, #007bff, #0056b3);
    opacity: 1;
    width: 6px;
}

.ochd-project-group-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0; /* Allow text truncation */
}

.ochd-project-expand-icon {
    font-size: 1rem;
    color: var(--ochd-primary-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    font-weight: bold;
}

.ochd-project-group-header.expanded .ochd-project-expand-icon {
    transform: rotate(90deg);
    background: rgba(0, 123, 255, 0.2);
}

.ochd-project-group-title h4 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--ochd-text-light);
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.ochd-project-group-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    flex-shrink: 0;
}

.ochd-project-group-meta span {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.3rem 0.8rem;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 20px;
    font-size: 0.85rem;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.ochd-project-group-meta span:hover {
    background: rgba(0, 123, 255, 0.1);
    color: var(--ochd-primary-color);
}

.ochd-project-number {
    background: rgba(0, 123, 255, 0.1) !important;
    color: var(--ochd-primary-color) !important;
    font-weight: 600 !important;
}

.ochd-project-group-content {
    padding: 0 2rem 2rem 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(248, 249, 250, 0.4));
    border-top: 1px solid rgba(0, 123, 255, 0.1);
}

.ochd-project-description {
    color: #6c757d;
    margin: 1.5rem 0;
    line-height: 1.6;
    font-style: italic;
    padding: 1rem;
    background: rgba(0, 123, 255, 0.03);
    border-radius: 8px;
    border-left: 4px solid rgba(0, 123, 255, 0.3);
}

.ochd-project-tasks-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
}

.ochd-project-task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 249, 250, 0.6));
    border-radius: 10px;
    border: 1px solid rgba(0, 123, 255, 0.1);
    border-left: 4px solid var(--ochd-primary-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.ochd-project-task-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.02), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ochd-project-task-item:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 123, 255, 0.04));
    transform: translateX(4px) translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.15);
    border-left-color: #0056b3;
}

.ochd-project-task-item:hover::before {
    opacity: 1;
}

.ochd-task-item-meta {
    display: flex;
    gap: 1.5rem;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.ochd-task-item-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.2rem 0.6rem;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 12px;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.ochd-task-item-meta span:hover {
    background: rgba(0, 123, 255, 0.1);
    color: var(--ochd-primary-color);
}

.ochd-task-created-small {
    color: #6c757d;
}

.ochd-task-created-small::before {
    content: '📅';
    margin-right: 0.2rem;
}

.ochd-task-time-small::before {
    content: '⏱';
    margin-right: 0.2rem;
}

.ochd-no-tasks {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 2rem;
    background: rgba(108, 117, 125, 0.05);
    border-radius: 8px;
    margin: 1rem 0;
}

/* Enhanced standalone task styling */
.ochd-standalone-task-card .ochd-task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.ochd-standalone-task-card .ochd-task-header h4 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--ochd-text-light);
    line-height: 1.3;
    flex: 1;
}

.ochd-standalone-task-card .ochd-task-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.ochd-standalone-task-card .ochd-task-meta span {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.4rem 1rem;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 20px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.ochd-standalone-task-card .ochd-task-meta span:hover {
    background: rgba(0, 123, 255, 0.1);
    color: var(--ochd-primary-color);
    transform: translateY(-1px);
}

/* Task start time display (primary) */
.ochd-task-start-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 123, 255, 0.04));
    border-radius: 8px;
    border-left: 4px solid var(--ochd-primary-color);
}

.ochd-start-date-label {
    font-weight: 600;
    color: var(--ochd-primary-color);
    font-size: 0.9rem;
}

.ochd-start-date-value {
    font-weight: 700;
    color: var(--ochd-text-light);
    font-size: 1rem;
}

/* Secondary metadata (smaller, less prominent) */
.ochd-task-meta-secondary {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 123, 255, 0.1);
    font-size: 0.8rem;
    color: #6c757d;
}

.ochd-task-meta-secondary span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.25rem 0.6rem;
    background: rgba(108, 117, 125, 0.08);
    border-radius: 12px;
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.ochd-task-meta-secondary span:hover {
    background: rgba(0, 123, 255, 0.08);
    color: var(--ochd-primary-color);
}

.ochd-task-created::before {
    content: '📅';
}

.ochd-task-updated::before {
    content: '🔄';
}

.ochd-task-time::before {
    content: '⏱';
}

.ochd-task-rate::before {
    content: '💰';
}

.ochd-task-card {
    background: var(--ochd-bg-light);
    border: 1px solid var(--ochd-border-light);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ochd-task-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ochd-task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    gap: 1rem;
}

.ochd-task-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--ochd-text-light);
    flex: 1;
}

.ochd-task-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.85rem;
    color: #777;
}

.ochd-task-meta span {
    white-space: nowrap;
}

/* Status badges */
.ochd-status-badge {
    padding: 0.4rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.ochd-status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.ochd-status-badge:hover::before {
    left: 100%;
}

.ochd-status-badge.small {
    padding: 0.3rem 0.7rem;
    font-size: 0.7rem;
    letter-spacing: 0.5px;
}

.ochd-status-badge.draft {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.15), rgba(108, 117, 125, 0.08));
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.3);
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
}

.ochd-status-badge.draft::after {
    content: '📝';
    font-size: 0.7rem;
}

.ochd-status-badge.active {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.3), rgba(40, 167, 69, 0.2));
    color: #0d4a14;
    border: 1px solid rgba(40, 167, 69, 0.6);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4);
    font-weight: 700;
}

.ochd-status-badge.active::after {
    content: '🟢';
    font-size: 0.7rem;
}

.ochd-status-badge.in-progress {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.4);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.ochd-status-badge.in-progress::after {
    content: '🟡';
    font-size: 0.7rem;
}

.ochd-status-badge.completed {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.1));
    color: #004085;
    border: 1px solid rgba(0, 123, 255, 0.4);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.ochd-status-badge.completed::after {
    content: '✅';
    font-size: 0.7rem;
}

.ochd-status-badge.cancelled {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.3), rgba(220, 53, 69, 0.2));
    color: #5a1a1f;
    border: 1px solid rgba(220, 53, 69, 0.6);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
    font-weight: 700;
}

.ochd-status-badge.cancelled::after {
    content: '❌';
    font-size: 0.7rem;
}

.ochd-status-badge.overdue {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
    color: #8b2635;
    border: 1px solid rgba(255, 107, 107, 0.4);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.ochd-status-badge.overdue::after {
    content: '⚠️';
    font-size: 0.7rem;
}

.ochd-status-badge.paused {
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.2), rgba(111, 66, 193, 0.1));
    color: #4a2c6a;
    border: 1px solid rgba(111, 66, 193, 0.4);
    box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
}

.ochd-status-badge.paused::after {
    content: '⏸️';
    font-size: 0.7rem;
}

.ochd-status-badge.sent {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.ochd-status-badge.viewed {
    background: rgba(111, 66, 193, 0.1);
    color: #6f42c1;
    border: 1px solid rgba(111, 66, 193, 0.3);
}

.ochd-status-badge.approved {
    background: rgba(32, 201, 151, 0.1);
    color: #20c997;
    border: 1px solid rgba(32, 201, 151, 0.3);
}

.ochd-status-badge.partial {
    background: rgba(253, 126, 20, 0.1);
    color: #fd7e14;
    border: 1px solid rgba(253, 126, 20, 0.3);
}

.ochd-status-badge.paid {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.ochd-status-badge.reversed {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Additional custom badge mappings for new scheme */
.ochd-status-badge.review {
    background: linear-gradient(135deg, rgba(253, 126, 20, 0.3), rgba(253, 126, 20, 0.2));
    color: #8b4000;
    border: 1px solid rgba(253, 126, 20, 0.7);
    box-shadow: 0 2px 8px rgba(253, 126, 20, 0.4);
    font-weight: 700;
}

.ochd-status-badge.almost-done {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.1));
    color: #004085;
    border: 1px solid rgba(0, 123, 255, 0.4);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* Overdue badge enhancement */
.ochd-status-badge.overdue {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.3), rgba(255, 107, 107, 0.2)) !important;
    color: #721c24 !important;
    border: 1px solid rgba(255, 107, 107, 0.6) !important;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4) !important;
    font-weight: 700 !important;
}

/* ================================================================= */
/* --- CLICKABLE INVOICE STATUS BADGES --- */
/* ================================================================= */

/* Make invoice status badges clickable */
.ochd-invoice-card .ochd-status-badge {
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.ochd-invoice-card .ochd-status-badge:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
}

.ochd-invoice-card .ochd-status-badge::after {
    content: '🔗';
    position: absolute;
    right: -8px;
    top: -8px;
    font-size: 0.7rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ochd-invoice-card .ochd-status-badge:hover::after {
    opacity: 0.8;
}

/* Special styling for overdue badges when clickable */
.ochd-invoice-card .ochd-status-badge.overdue:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.5) !important;
}

/* Add subtle animation to indicate clickability */
@keyframes pulseClickable {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.ochd-invoice-card .ochd-status-badge.overdue {
    animation: pulseClickable 2s infinite;
}
}

.ochd-invoice-dates .overdue-date {
    color: #dc3545;
    font-weight: 600;
}

/* Invoice cards */
.ochd-invoices-section {
    margin-bottom: 2rem;
}

.ochd-invoice-card {
    background: var(--ochd-bg-light);
    border: 1px solid var(--ochd-border-light);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ochd-invoice-card.unpaid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc3545, #fd7e14);
}

.ochd-invoice-card.paid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #28a745, #20c997);
}

.ochd-invoice-card.overdue::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc3545, #e74c3c);
}

.ochd-invoice-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.ochd-invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.ochd-invoice-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--ochd-primary-color);
    flex: 1;
}

.ochd-invoice-details {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 2rem;
}

.ochd-invoice-amount {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.ochd-invoice-amount strong {
    font-size: 1.25rem;
    color: var(--ochd-primary-color);
}

.ochd-balance {
    font-size: 0.9rem;
    color: #dc3545;
    font-weight: 500;
}

.ochd-invoice-dates {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: #777;
    text-align: right;
}

.ochd-invoice-notes {
    color: #666;
    font-style: italic;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--ochd-border-light);
    line-height: 1.5;
}

/* Client Portal Section */
.ochd-client-portal-section {
    margin-top: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(111, 66, 193, 0.1));
    border-radius: 12px;
    border: 1px solid rgba(0, 123, 255, 0.2);
    text-align: center;
}

.ochd-client-portal-description {
    margin-bottom: 1.5rem;
    color: var(--ochd-text-light);
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Client portal section styling - buttons are handled by unified button styles above */

/* ================================================================= */
/* --- RESPONSIVE DESIGN FOR INVOICE NINJA ELEMENTS --- */
/* ================================================================= */

@media (max-width: 768px) {
    .ochd-project-header,
    .ochd-task-header,
    .ochd-invoice-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .ochd-project-meta,
    .ochd-task-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .ochd-invoice-details {
        flex-direction: column;
        gap: 1rem;
    }

    .ochd-invoice-dates {
        text-align: left;
    }

    .ochd-task-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .ochd-client-portal-section {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .ochd-client-portal-description {
        font-size: 1rem;
    }

    /* New responsive styles for task-focused UI */
    .ochd-section-header {
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .ochd-section-header h4 {
        font-size: 1.1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .ochd-section-count {
        margin-left: 0;
        align-self: flex-start;
    }

    .ochd-standalone-task-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .ochd-standalone-task-card .ochd-task-header h4 {
        font-size: 1.2rem;
    }

    .ochd-standalone-task-card .ochd-task-meta {
        flex-direction: column;
        gap: 0.75rem;
    }

    .ochd-project-group-header {
        padding: 1rem 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .ochd-project-group-title {
        width: 100%;
    }

    .ochd-project-group-title h4 {
        font-size: 1.1rem;
        white-space: normal;
    }

    .ochd-project-group-meta {
        flex-wrap: wrap;
        gap: 0.75rem;
        width: 100%;
    }

    .ochd-project-group-content {
        padding: 0 1.5rem 1.5rem 1.5rem;
    }

    .ochd-project-task-item {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .ochd-task-item-meta {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .ochd-bookings-list {
        gap: 2rem;
    }

    /* Responsive subcategory tabs */
    .ochd-subcategory-tabs {
        flex-direction: column;
        gap: 0.25rem;
        padding: 0.75rem;
    }

    .ochd-subcategory-tab {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
        text-align: center;
    }

    /* Responsive design for new time display */
    .ochd-task-start-time {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        padding: 0.5rem 0.75rem;
    }

    .ochd-start-date-label {
        font-size: 0.8rem;
    }

    .ochd-start-date-value {
        font-size: 0.9rem;
    }

    .ochd-task-meta-secondary {
        flex-direction: column;
        gap: 0.5rem;
    }

    .ochd-task-meta-secondary span {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }
}

@media (max-width: 480px) {
    .ochd-project-card,
    .ochd-task-card,
    .ochd-invoice-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .ochd-project-header h4,
    .ochd-task-header h4,
    .ochd-invoice-header h4 {
        font-size: 1.1rem;
    }

    .ochd-invoice-amount strong {
        font-size: 1.1rem;
    }

    .ochd-client-portal-section {
        padding: 1rem;
    }
}

/* ================================================================= */
/* --- DARK MODE SUPPORT FOR INVOICE NINJA ELEMENTS --- */
/* ================================================================= */

@media (prefers-color-scheme: dark) {
    .ochd-loading-state,
    .ochd-empty-state {
        color: #aaa;
    }

    .ochd-error-state {
        color: #ff6b6b;
    }

    .ochd-project-card,
    .ochd-task-card,
    .ochd-invoice-card {
        background: var(--ochd-bg-dark);
        border-color: var(--ochd-border-dark);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .ochd-project-card::before,
    .ochd-invoice-card.paid::before {
        background: linear-gradient(90deg, #4da6ff, #007bff);
    }

    .ochd-invoice-card.unpaid::before {
        background: linear-gradient(90deg, #ff6b6b, #ffa726);
    }

    .ochd-project-card:hover,
    .ochd-task-card:hover,
    .ochd-invoice-card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    }

    .ochd-project-header h4,
    .ochd-task-header h4,
    .ochd-invoice-header h4 {
        color: #4da6ff;
    }

    .ochd-project-description,
    .ochd-invoice-notes {
        color: #aaa;
    }

    .ochd-project-meta,
    .ochd-task-meta,
    .ochd-invoice-dates {
        color: #999;
    }

    .ochd-task-time-small {
        color: #aaa;
    }

    .ochd-invoice-card.overdue::before {
        background: linear-gradient(90deg, #ff6b6b, #ff5722);
    }

    .ochd-task-name {
        color: var(--ochd-text-dark);
    }

    .ochd-task-item {
        background: rgba(77, 166, 255, 0.1);
    }

    .ochd-project-tasks,
    .ochd-invoice-notes {
        border-color: var(--ochd-border-dark);
    }

    .ochd-project-tasks h5 {
        color: var(--ochd-text-dark);
    }

    .ochd-invoice-amount strong {
        color: #4da6ff;
    }

    .ochd-client-portal-section {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.15), rgba(138, 99, 210, 0.15));
        border-color: rgba(77, 166, 255, 0.3);
    }

    .ochd-client-portal-description {
        color: var(--ochd-text-dark);
    }

    /* Client portal buttons use unified button styles */

    /* Dark mode for new task-focused UI */
    .ochd-section-header {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.15), rgba(77, 166, 255, 0.08));
        border-color: rgba(77, 166, 255, 0.25);
    }

    .ochd-section-header::before {
        background: linear-gradient(90deg, #4da6ff, #007bff);
    }

    .ochd-section-header h4 {
        color: #4da6ff;
    }

    .ochd-section-header p {
        color: #aaa;
    }

    .ochd-section-count {
        background: rgba(77, 166, 255, 0.15);
        color: #4da6ff;
    }

    .ochd-standalone-task-card {
        background: linear-gradient(135deg, rgba(33, 37, 41, 0.95), rgba(52, 58, 64, 0.9));
        border-color: rgba(77, 166, 255, 0.2);
        box-shadow:
            0 8px 32px rgba(77, 166, 255, 0.15),
            0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .ochd-standalone-task-card::before {
        background: linear-gradient(90deg, #4da6ff, #007bff, #0056b3);
    }

    .ochd-standalone-task-card:hover {
        box-shadow:
            0 16px 48px rgba(77, 166, 255, 0.2),
            0 8px 24px rgba(0, 0, 0, 0.4);
        border-color: rgba(77, 166, 255, 0.35);
    }

    .ochd-standalone-task-card .ochd-task-header h4 {
        color: var(--ochd-text-dark);
    }

    .ochd-standalone-task-card .ochd-task-meta span {
        background: rgba(77, 166, 255, 0.15);
        color: #aaa;
    }

    .ochd-standalone-task-card .ochd-task-meta span:hover {
        background: rgba(77, 166, 255, 0.25);
        color: #4da6ff;
    }

    .ochd-project-group {
        background: linear-gradient(135deg, rgba(33, 37, 41, 0.95), rgba(52, 58, 64, 0.9));
        border-color: rgba(108, 117, 125, 0.25);
        box-shadow:
            0 4px 16px rgba(108, 117, 125, 0.15),
            0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .ochd-project-group:hover {
        box-shadow:
            0 8px 24px rgba(108, 117, 125, 0.2),
            0 4px 16px rgba(0, 0, 0, 0.4);
        border-color: rgba(77, 166, 255, 0.3);
    }

    .ochd-project-group-header {
        background: linear-gradient(135deg, rgba(52, 58, 64, 0.8), rgba(33, 37, 41, 0.6));
    }

    .ochd-project-group-header::before {
        background: linear-gradient(180deg, #6c757d, #495057);
    }

    .ochd-project-group-header:hover {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.15), rgba(77, 166, 255, 0.08));
    }

    .ochd-project-group-header:hover::before {
        background: linear-gradient(180deg, #4da6ff, #007bff);
    }

    .ochd-project-group-header.expanded {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.2), rgba(77, 166, 255, 0.1));
        border-color: rgba(77, 166, 255, 0.25);
    }

    .ochd-project-group-header.expanded::before {
        background: linear-gradient(180deg, #4da6ff, #007bff);
    }

    .ochd-project-group-title h4 {
        color: var(--ochd-text-dark);
    }

    .ochd-project-expand-icon {
        background: rgba(77, 166, 255, 0.2);
        color: #4da6ff;
    }

    .ochd-project-group-header.expanded .ochd-project-expand-icon {
        background: rgba(77, 166, 255, 0.3);
    }

    .ochd-project-group-meta span {
        background: rgba(108, 117, 125, 0.2);
        color: #aaa;
    }

    .ochd-project-group-meta span:hover {
        background: rgba(77, 166, 255, 0.2);
        color: #4da6ff;
    }

    .ochd-project-number {
        background: rgba(77, 166, 255, 0.2) !important;
        color: #4da6ff !important;
    }

    .ochd-project-group-content {
        background: linear-gradient(135deg, rgba(52, 58, 64, 0.6), rgba(33, 37, 41, 0.4));
        border-color: rgba(77, 166, 255, 0.15);
    }

    .ochd-project-description {
        background: rgba(77, 166, 255, 0.08);
        border-color: rgba(77, 166, 255, 0.3);
        color: #aaa;
    }

    .ochd-project-task-item {
        background: linear-gradient(135deg, rgba(52, 58, 64, 0.8), rgba(33, 37, 41, 0.6));
        border-color: rgba(77, 166, 255, 0.2);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .ochd-project-task-item:hover {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.15), rgba(77, 166, 255, 0.08));
        box-shadow: 0 4px 16px rgba(77, 166, 255, 0.2);
        border-color: #4da6ff;
    }

    .ochd-task-item-meta span {
        background: rgba(108, 117, 125, 0.2);
        color: #aaa;
    }

    .ochd-task-item-meta span:hover {
        background: rgba(77, 166, 255, 0.2);
        color: #4da6ff;
    }

    .ochd-task-created-small {
        color: #999;
    }

    .ochd-no-tasks {
        background: rgba(108, 117, 125, 0.1);
        color: #aaa;
    }

    /* Dark mode for subcategory tabs */
    .ochd-subcategory-tabs {
        background: rgba(33, 37, 41, 0.8);
        border-color: rgba(77, 166, 255, 0.2);
    }

    .ochd-subcategory-tab {
        color: #aaa;
    }

    .ochd-subcategory-tab:hover {
        background: rgba(77, 166, 255, 0.15);
        color: #4da6ff;
    }

    .ochd-subcategory-tab.active {
        background: linear-gradient(135deg, #4da6ff, #007bff);
        color: white;
        box-shadow: 0 4px 12px rgba(77, 166, 255, 0.4);
    }

    /* Dark mode for new task time display */
    .ochd-task-start-time {
        background: linear-gradient(135deg, rgba(77, 166, 255, 0.15), rgba(77, 166, 255, 0.08));
        border-color: #4da6ff;
    }

    .ochd-start-date-label {
        color: #4da6ff;
    }

    .ochd-start-date-value {
        color: var(--ochd-text-dark);
    }

    .ochd-task-meta-secondary {
        border-color: rgba(77, 166, 255, 0.2);
        color: #aaa;
    }

    .ochd-task-meta-secondary span {
        background: rgba(108, 117, 125, 0.15);
        color: #aaa;
    }

    .ochd-task-meta-secondary span:hover {
        background: rgba(77, 166, 255, 0.15);
        color: #4da6ff;
    }
}

/* ================================================================= */
/* --- ADDITIONAL ENHANCEMENTS --- */
/* ================================================================= */

/* Loading state for profile cards */
.ochd-profile-card.loading {
    position: relative;
    overflow: hidden;
}

.ochd-profile-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Empty state styling */
.ochd-profile-empty {
    text-align: center;
    padding: 3rem 2rem;
    color: #666;
    font-style: italic;
}

.ochd-profile-empty::before {
    content: '👤';
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Success/Error message styling */
.ochd-profile-message {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.ochd-profile-message.success {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #155724;
}

.ochd-profile-message.error {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #721c24;
}

/* Print styles */
@media print {
    .ochd-profile-container {
        box-shadow: none;
        background: white;
        color: black;
    }

    .ochd-profile-card {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .ochd-profile-card::before {
        display: none;
    }

    .ochd-tabs,
    .ochd-profile-container > p {
        display: none;
    }
}

/* ================================================================= */
/* --- INVISIBLE CLICKABLE AREA PREVENTION --- */
/* ================================================================= */

/* Ensure hidden elements cannot be interacted with */
.ochd-profile-dropdown:not(.is-active),
.ochd-main-menu-container:not(.is-open),
[aria-hidden="true"],
[style*="display: none"],
[style*="visibility: hidden"] {
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* Prevent any invisible overlays from blocking interactions */
.ochd-profile-menu:not(.is-active) .ochd-profile-dropdown,
.ochd-mobile-menu-toggle:not(.is-open) + .ochd-main-menu-container {
    pointer-events: none !important;
    z-index: -1 !important;
}

/* Ensure visible menus are fully interactive */
.ochd-profile-menu.is-active .ochd-profile-dropdown,
.ochd-main-menu-container.is-open {
    pointer-events: auto !important;
    user-select: auto !important;
    -webkit-user-select: auto !important;
    -moz-user-select: auto !important;
    -ms-user-select: auto !important;
    z-index: 1000 !important;
}

/* Additional safety for any remaining invisible elements */
*[style*="opacity: 0"]:not(.is-active):not(.is-open) {
    pointer-events: none !important;
}

/* Ensure menu links within visible menus are clickable */
.ochd-profile-menu.is-active .ochd-profile-dropdown a,
.ochd-main-menu-container.is-open .ochd-menu-button__link {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* ================================================================= */
/* --- ADDITIONAL MOBILE ICON COLOR FIXES --- */
/* ================================================================= */

/* Ensure mobile menu icon is always white - additional specificity */
.ochd-mobile-menu-toggle span,
.ochd-mobile-menu-toggle:not(:hover) span,
.ochd-mobile-menu-toggle:not(.is-open) span,
button.ochd-mobile-menu-toggle span {
    background-color: #fff !important;
    background: #fff !important;
    color: #fff !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Force white color on mobile devices specifically */
@media (max-width: 768px) {
    .ochd-mobile-menu-toggle span,
    .ochd-mobile-menu-toggle span::before,
    .ochd-mobile-menu-toggle span::after {
        background-color: #fff !important;
        background: #fff !important;
        border-color: #fff !important;
        color: #fff !important;
        opacity: 1 !important;
        filter: brightness(1) !important;
    }

    /* Override any theme or plugin styles */
    .ochd-mobile-menu-toggle[style*="color"] span,
    .ochd-mobile-menu-toggle[class*="color"] span {
        background-color: #fff !important;
        color: #fff !important;
    }
}

/* Ensure consistency with other header icons */
.ochd-mobile-menu-toggle span {
    background-color: #fff !important;
    background-image: none !important;
    background-clip: border-box !important;
    -webkit-background-clip: border-box !important;
}