.ochd-booking-form-wrapper {
    padding-left: 8px;
    padding-right: 8px;
    background-color: transparent;
    border-radius: 88px;

    /* Remove all margins and center the form */
    margin: 0 auto !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;

    /* Center alignment and proper layout */
    text-align: center;
    box-sizing: border-box;
    overflow: visible;

    /* Ensure proper centering */
    display: block;
    width: 100%;
    max-width: 100%;
}

.ochd-booking-form-wrapper > * {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.ochd-booking-form-wrapper iframe.easyappointments-iframe {
    display: block;
    border: none;
    width: 100%;
    border-radius: 80px;

    /* Dynamic height - no fixed constraints */
    height: auto !important;
    min-height: 600px; /* Reduced minimum for dynamic sizing */
    max-height: none !important;

    /* Remove all spacing and center the iframe */
    margin: 0 auto !important;
    padding: 0 !important;

    /* Proper layout and alignment */
    box-sizing: border-box;
    vertical-align: top;

    /* Ensure iframe is centered within wrapper */
    text-align: left; /* Reset text alignment for iframe content */
}

#ochd-guest-overlay {
    border-radius: 88px;
}

.ochd-button {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 16px;
    margin: 10px;
    cursor: pointer;
    border: 1px solid transparent;
}

.ochd-login-button {
    background-color: transparent;
    border-color: #aaa;
    color: #eee;
}

.ochd-login-button:hover {
    background-color: rgba(255,255,255,0.1);
}

.ochd-register-button {
    background-color: #0073e6;
    color: white;
    border-color: #0073e6;
}

/* ================================================================= */
/* --- MOBILE VIEWPORT HEIGHT FIX FOR BOOKING FORM --- */
/* ================================================================= */

/* Mobile devices: Fix double scrolling issue by constraining iframe height */
@media (max-width: 768px) {
    .ochd-booking-container {
        /* Allow container to expand with content */
        max-height: none;
        overflow: visible;
        position: relative;
    }

    .ochd-booking-form-wrapper {
        /* Adjust padding for mobile */
        padding-left: 4px;
        padding-right: 4px;
        height: auto;
        overflow: visible;
    }

    .ochd-booking-form-wrapper iframe.easyappointments-iframe {
        /* Dynamic height with mobile-optimized constraints */
        height: auto !important;
        max-height: 1100px !important; /* Prevent excessive height on mobile */
        min-height: 700px !important; /* Adequate minimum for mobile forms */

        /* Center the iframe on mobile */
        margin: 0 auto !important;
        display: block !important;

        /* Allow iframe content to determine height */
        overflow-y: auto;
        overflow-x: hidden;

        /* Let iframe handle its own scrolling if needed */
        scrolling: auto;

        /* Smooth scrolling within iframe */
        scroll-behavior: smooth;
    }

    /* Ensure guest overlay covers the full iframe area with perfect alignment */
    #ochd-guest-overlay {
        height: 100% !important;
        max-height: none !important;
        overflow: hidden;
        min-height: 1000px;

        /* Perfect alignment with form */
        margin: 0 !important;
        padding: 0 !important;
        top: 0 !important;
        left: 0 !important;
        position: absolute !important;

        /* Ensure overlay matches iframe dimensions exactly */
        box-sizing: border-box !important;
        border-radius: inherit !important;
    }
}

/* Small mobile devices: Ensure full content visibility */
@media (max-width: 480px) {
    .ochd-booking-form-wrapper iframe.easyappointments-iframe {
        /* Allow full content height on small screens */
        height: auto !important;
        max-height: none !important;
        min-height: 900px; /* Generous minimum for small screens */
    }

    #ochd-guest-overlay {
        height: 100% !important;
        max-height: none !important;
        min-height: 900px;
    }
}

/* Very small mobile devices: Maintain full content visibility */
@media (max-width: 360px) {
    .ochd-booking-form-wrapper iframe.easyappointments-iframe {
        /* Allow full content height even on very small screens */
        height: auto !important;
        max-height: none !important;
        min-height: 800px; /* Adequate minimum for very small screens */
    }

    #ochd-guest-overlay {
        height: 100% !important;
        max-height: none !important;
        min-height: 800px;
    }
}

/* Landscape orientation on mobile: Ensure full content visibility */
@media (max-width: 768px) and (orientation: landscape) {
    .ochd-booking-form-wrapper iframe.easyappointments-iframe {
        /* Landscape: Allow full content height */
        height: auto !important;
        max-height: none !important;
        min-height: 700px; /* Reasonable minimum for landscape */
    }

    #ochd-guest-overlay {
        height: 100% !important;
        max-height: none !important;
        min-height: 700px;
    }
}

/* Additional layout fixes to prevent visual spacing issues */
.ochd-booking-container {
    /* Prevent theme interference with spacing */
    margin: 0 !important;
    padding: 0 !important;

    /* Ensure proper layout */
    box-sizing: border-box;
    overflow: visible;
    position: relative;
    width: 100%;
    background-color: transparent;
}

/* Ensure integrated booking iframe has optimal spacing */
.ochd-integrated-booking-iframe {
    /* Remove any spacing that causes visual layout issues */
    margin: 0 !important;
    padding: 0 !important;

    /* Ensure proper box model */
    box-sizing: border-box !important;
    vertical-align: top !important;

    /* Prevent theme CSS from interfering */
    display: block !important;
    border: none !important;
    width: 100% !important;
}

/* Additional centering and layout optimization */
.ochd-booking-container {
    /* Ensure container is centered and has proper layout */
    margin: 0 auto !important;
    text-align: center !important;
    width: 100% !important;
    max-width: 100% !important;

    /* Remove any theme-imposed spacing */
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.ochd-booking-form-wrapper {
    /* Ensure wrapper centers its content */
    text-align: center !important;
    margin: 0 auto !important;
    display: block !important;
    width: 100% !important;
}

/* Ensure all iframe instances are properly centered */
iframe.easyappointments-iframe,
iframe.ochd-integrated-booking-iframe {
    margin: 0 auto !important;
    display: block !important;
    text-align: left !important; /* Reset text alignment for iframe content */
}
