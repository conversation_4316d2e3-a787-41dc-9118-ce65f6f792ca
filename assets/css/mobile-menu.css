/* OCHandyDude Header & Menu Styles v5.4 (Enhanced with Improved Animations) */

/* --- Global styles and background animation for pages --- */
body {
    background-attachment: fixed;
    background-size: cover;
    background-repeat: no-repeat;
    animation: gentle-drift 400s infinite alternate ease-in-out;
}

@keyframes gentle-drift {
    from { background-position: top left; }
    to { background-position: bottom right; }
}

.easyappointments-iframe {
    border-radius: 80px;
    overflow: hidden;
    background-color: transparent;
}

main.wp-block-group[style*="margin-top"] {
    margin-top: 25px !important;
}

.wp-block-group.is-content-justification-space-between {
    padding: 10px 20px !important;
    box-sizing: border-box;
}

/* --- Styles for unified header icons and links --- */
.ochd-unified-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 6px;
    padding: 4px;
}

.ochd-unified-icon-wrapper + .ochd-unified-icon-wrapper {
    margin-left: 12px;
}

.ochd-unified-icon-wrapper svg {
    width: 30px;
    height: 30px;
    stroke: #fff;
    stroke-width: 1.8;
    fill: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Standardized hover effect for all icons */
.ochd-unified-icon-wrapper:hover svg {
    stroke: #007bff;
    transform: scale(1.1);
}

/* Enhanced hover effect with background highlight */
.ochd-unified-icon-wrapper:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
    transform: translateY(-1px);
}

/* Focus states for accessibility */
.ochd-unified-icon-wrapper:focus,
.ochd-unified-icon-wrapper:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
}

.ochd-unified-icon-wrapper:focus svg,
.ochd-unified-icon-wrapper:focus-visible svg {
    stroke: #007bff;
    transform: scale(1.1);
}

/* Active state for clicked icons */
.ochd-icon-active {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-radius: 6px !important;
    transform: translateY(-1px) !important;
}

.ochd-icon-active svg {
    stroke: #007bff !important;
    transform: scale(1.1) !important;
}

/* Active state for mobile menu toggle */
.ochd-mobile-menu-toggle.ochd-icon-active span {
    background-color: #007bff !important;
}

/* Cart icon specific styling */
.ochd-cart-icon {
    position: relative;
}

/* Ensure cart icon gets the same hover treatment */
.ochd-cart-icon:hover svg {
    stroke: #007bff;
    transform: scale(1.1);
}

.ochd-cart-icon:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
    transform: translateY(-1px);
}

.wc-block-mini-cart__button { display: none !important; } /* Hide original cart button */

/* --- Styles for mobile menu toggle button --- */
.ochd-mobile-menu-toggle {
    display: flex;
    width: 30px;
    height: 30px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background: transparent;
    border: none;
    padding: 0;
    gap: 5px;
    z-index: 1002;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 6px;
}

/* Standardized hover effect matching other icons */
.ochd-mobile-menu-toggle:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.1) translateY(-1px);
}

.ochd-mobile-menu-toggle:hover span {
    background-color: #007bff;
}

/* Focus states for accessibility */
.ochd-mobile-menu-toggle:focus,
.ochd-mobile-menu-toggle:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    background-color: rgba(0, 123, 255, 0.1);
}

.ochd-mobile-menu-toggle:focus span,
.ochd-mobile-menu-toggle:focus-visible span {
    background-color: #007bff;
}

.ochd-mobile-menu-toggle span {
    display: block;
    width: 90%;
    height: 2.5px;
    background-color: #fff !important; /* Force white color to override theme styles */
    border-radius: 3px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    /* Additional properties to ensure visibility */
    opacity: 1 !important;
    visibility: visible !important;
}
.ochd-mobile-menu-toggle.is-open { transform: rotate(180deg); }
.ochd-mobile-menu-toggle.is-open span:nth-child(1) { transform: translateY(7.5px) rotate(45deg); }
.ochd-mobile-menu-toggle.is-open span:nth-child(2) { opacity: 0; }
.ochd-mobile-menu-toggle.is-open span:nth-child(3) { transform: translateY(-7.5px) rotate(-45deg); }

/* --- Styles for mobile navigation menu container with proper interaction handling --- */
.ochd-main-menu-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px 0;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out,
                opacity 0.4s ease-in-out,
                padding 0.4s ease-in-out,
                pointer-events 0.4s ease-in-out;

    /* CRITICAL: Disable all mouse interactions when hidden */
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.ochd-main-menu-container.is-open {
    max-height: 500px;
    opacity: 1;

    /* CRITICAL: Re-enable mouse interactions when visible */
    pointer-events: auto;
    user-select: auto;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
}

/* --- Styles for individual menu button links --- */
.ochd-menu-button__link {
    display: block;
    border-radius: 9999px;
    border-width: 2px;
    border-style: solid;
    padding: 10px 25px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    color: white !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    background: transparent !important;
}
.ochd-menu-button__link:hover {
    border-color: white !important;
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-3px);
}

/* Styles for call-to-action (CTA) buttons */
.ochd-menu-button.cta-button .ochd-menu-button__link {
    background: linear-gradient(29deg,rgb(6,147,227) 3%,rgb(155,81,224) 100%) !important;
    border-color: transparent !important;
    transform: scale(1.1);
}
.ochd-menu-button.cta-button .ochd-menu-button__link:hover {
    transform: scale(1.15) translateY(-3px);
    box-shadow: 0px 5px 15px rgba(155, 81, 224, 0.4);
}

/* ================================================================= */
/* --- Responsive Design: Adaptive scaling for different screen sizes --- */
/* ================================================================= */

/* For medium-sized devices and tablets */
@media (max-width: 780px) {
    .ochd-menu-button__link {
        padding: 10px 20px;
        font-size: 15px;
    }
    .ochd-menu-button.cta-button .ochd-menu-button__link {
        transform: scale(1.05); /* Smaller CTA scaling to prevent overflow */
    }
    .ochd-menu-button.cta-button .ochd-menu-button__link:hover {
        transform: scale(1.1) translateY(-3px);
    }
}

/* For smaller mobile devices */
@media (max-width: 525px) {
    .ochd-main-menu-container {
        gap: 8px; /* Reduce spacing between elements */
    }
    .ochd-menu-button__link {
        font-size: 14px;
        padding: 8px 15px;
    }
}

/* For very small mobile devices */
@media (max-width: 430px) {
    .ochd-menu-button__link {
        font-size: 12px;
        padding: 8px 12px;
    }
}

/* For extra small mobile devices */
@media (max-width: 365px) {
    .ochd-menu-button__link {
        font-size: 10px;
        padding: 8px 10px;
    }
}

/* ================================================================= */
/* --- MOBILE UI/UX FIXES AND VISUAL ENHANCEMENTS --- */
/* ================================================================= */

/* Fix 1: Mobile Menu Icon Color - Force white color on all states */
.ochd-mobile-menu-toggle span,
.ochd-mobile-menu-toggle:not(:hover):not(:focus) span,
.ochd-mobile-menu-toggle:not(.is-open) span {
    background-color: #fff !important;
    color: #fff !important;
    border-color: #fff !important;
}

/* Ensure mobile menu icon is always white on mobile devices */
@media (max-width: 768px) {
    .ochd-mobile-menu-toggle span {
        background-color: #fff !important;
        opacity: 1 !important;
        filter: none !important;
    }

    /* Override any theme styles that might affect mobile menu color */
    .ochd-mobile-menu-toggle span::before,
    .ochd-mobile-menu-toggle span::after {
        background-color: #fff !important;
    }
}

/* Fix 2: Logo Click Border Removal */
.wp-site-blocks a,
.wp-block-site-logo a,
.custom-logo-link,
.site-logo a,
.logo a,
a[href*="logo"],
img[class*="logo"],
.wp-block-site-logo img,
.custom-logo {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
    text-decoration: none !important;
}

/* Remove focus outline from logo while maintaining accessibility */
.wp-site-blocks a:focus,
.wp-block-site-logo a:focus,
.custom-logo-link:focus,
.site-logo a:focus,
.logo a:focus {
    outline: 2px solid transparent !important;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.5) !important;
    border-radius: 4px;
}

/* Fix 3: Logo Movement/Animation Prevention */
.wp-block-site-logo,
.wp-block-site-logo img,
.custom-logo,
.custom-logo-link,
.site-logo,
.logo {
    transform: none !important;
    animation: none !important;
    transition: none !important;
    position: static !important;
    will-change: auto !important;
}

/* Prevent logo movement during scroll */
.wp-block-site-logo img,
.custom-logo {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    animation: none !important;
}

/* Override any scroll-triggered animations on logo */
.wp-block-site-logo[style*="transform"],
.custom-logo[style*="transform"] {
    transform: none !important;
}

/* Fix 4: Enhanced Visual Effects for Consistency */

/* Enhanced button styling across all components */
button,
.wp-block-button__link,
.wp-element-button,
input[type="submit"],
input[type="button"],
.ochd-button,
.btn {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-radius: 8px !important;
}

button:hover,
.wp-block-button__link:hover,
.wp-element-button:hover,
input[type="submit"]:hover,
input[type="button"]:hover,
.ochd-button:hover,
.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced card and container styling */
.wp-block-group,
.wp-block-columns,
.wp-block-column,
.entry-content > *,
.page-content > * {
    transition: all 0.3s ease !important;
}

/* Add subtle shadows to content blocks */
.wp-block-group:not(.is-style-no-shadow),
.wp-block-columns:not(.is-style-no-shadow) {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05) !important;
    border-radius: 12px !important;
}

.wp-block-group:hover:not(.is-style-no-shadow),
.wp-block-columns:hover:not(.is-style-no-shadow) {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    transform: translateY(-1px) !important;
}

/* Enhanced form styling */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="url"],
textarea,
select {
    border-radius: 8px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    padding: 12px 16px !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
    border-color: rgba(0, 123, 255, 0.5) !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* Enhanced image styling */
img:not(.wp-block-site-logo img):not(.custom-logo) {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

img:hover:not(.wp-block-site-logo img):not(.custom-logo) {
    transform: scale(1.02) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced link styling */
a:not(.wp-block-button__link):not(.ochd-menu-button__link):not(.custom-logo-link) {
    transition: all 0.3s ease !important;
    text-decoration-thickness: 2px !important;
    text-underline-offset: 3px !important;
}

a:hover:not(.wp-block-button__link):not(.ochd-menu-button__link):not(.custom-logo-link) {
    text-decoration-thickness: 3px !important;
    text-underline-offset: 4px !important;
}

/* Enhanced table styling */
table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

table th,
table td {
    transition: background-color 0.3s ease !important;
}

table tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* Enhanced blockquote styling */
blockquote {
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    border-left: 4px solid rgba(0, 123, 255, 0.5) !important;
}

blockquote:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    transform: translateX(4px) !important;
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
    /* Ensure all interactive elements are touch-friendly */
    button,
    .wp-block-button__link,
    .ochd-button,
    input[type="submit"],
    input[type="button"] {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 12px 20px !important;
    }

    /* Enhanced mobile form styling */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    input[type="url"],
    textarea,
    select {
        font-size: 16px !important; /* Prevent zoom on iOS */
        padding: 16px !important;
    }

    /* Mobile-optimized shadows (lighter for performance) */
    .wp-block-group:not(.is-style-no-shadow),
    .wp-block-columns:not(.is-style-no-shadow) {
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05) !important;
    }
}
